# Customer Analytics Testing Guide

## Overview
This guide provides step-by-step instructions to test the Customer Transaction Summary filter UI and High Alerts transaction details functionality after applying the fixes.

## Pre-Testing Setup

### 1. Files to Check
Ensure these files have been updated with the fixes:
- `js/customerAnalytics.js` - Enhanced with debugging and improved initialization
- `js/app.js` - Enhanced initialization with retry mechanism
- `test_customer_analytics_fix.html` - New test file
- `test_customer_analytics_with_data.js` - Test script for console

### 2. Browser Console Setup
Open browser developer tools (F12) and keep the Console tab open to monitor debug messages.

## Testing Procedures

### Test 1: Basic Functionality Test

1. **Open Test File**
   - Navigate to `test_customer_analytics_fix.html`
   - Open browser console
   - Look for initialization messages

2. **Run Basic Tests**
   ```
   Click "Test Initialization" - Should show ✓ Customer Analytics object exists
   Click "Test Elements" - Should show element availability status
   Click "Test with Sample Data" - Should process 2 sample transactions
   Click "Test Modal" - Should briefly show/hide modal
   ```

3. **Expected Results**
   - All tests should pass without errors
   - Consol<PERSON> should show detailed debug information
   - No error messages in red

### Test 2: Main Application Test

1. **Load Main Application**
   - Open `application.html`
   - Wait for full page load
   - Check console for Customer Analytics initialization messages

2. **Load Test Data**
   - Upload one of the existing CSV files (e.g., `daily_transactions_2024-01-20.csv`)
   - Wait for processing to complete
   - Check console for data processing messages

3. **Navigate to Customer Analytics**
   - Click on "Customer Analytics" in the navigation
   - Look for the Customer Transaction Summary section
   - Check if filter controls are visible

### Test 3: Filter UI Testing

1. **Test Alert Type Filter**
   ```
   - Select "All Alert Customers" - Should show all customers with alerts
   - Select "High Alerts (Both MMK & USD)" - Should show only high alert customers
   - Select "MMK Alerts (≥1B MMK)" - Should show MMK alert customers
   - Select "USD Alerts (≥10K USD)" - Should show USD alert customers
   ```

2. **Test Sort By Filter**
   ```
   - Select "Alert Priority (High to Low)" - Should sort by alert severity
   - Select "MMK Amount (High to Low)" - Should sort by MMK amounts
   - Select "USD Amount (High to Low)" - Should sort by USD amounts
   - Select "Total Transactions (High to Low)" - Should sort by transaction count
   ```

3. **Test Date Range Filter**
   ```
   - Select "All Dates" - Should show all date ranges
   - Select "Today" - Should filter to today's transactions
   - Select "This Week" - Should filter to this week's transactions
   ```

4. **Expected Results**
   - Table should update immediately when filters change
   - Console should show filter change messages
   - Row count should change based on filter selection

### Test 4: High Alerts Transaction Details

1. **Generate Test Alerts**
   - In console, run: `window.customerAnalytics.testCustomerAlerts()`
   - This creates sample high-value customers
   - Table should populate with test alert customers

2. **Test View Details**
   - Click "View Details" button on any alert customer row
   - Modal should appear with customer transaction details
   - Check modal header shows correct customer name and date

3. **Test Modal Functionality**
   ```
   - Modal should display transaction summary statistics
   - Individual transaction table should show detailed transactions
   - Close button (X) should close modal
   - Backdrop click should close modal
   - Escape key should close modal
   - Export button should download CSV file
   ```

4. **Expected Results**
   - Modal appears smoothly with animation
   - All transaction details are visible
   - Summary statistics are calculated correctly
   - Modal closes properly with all methods

### Test 5: Real Data Testing

1. **Load Real Transaction Data**
   - Upload a CSV file with actual transaction data
   - Wait for processing to complete

2. **Run Console Test**
   ```javascript
   // In browser console:
   testCustomerAnalyticsWithData();
   ```

3. **Verify Results**
   - Check console output for processing statistics
   - Verify customer count and alert count
   - Test filters with real data
   - Test modal with real customer data

## Debugging Commands

If issues occur, use these console commands:

### Basic Status Check
```javascript
// Quick status
quickTestCA();

// Detailed debug
debugCustomerAnalytics();

// Check initialization
console.log('CA Available:', !!window.customerAnalytics);
console.log('CA Initialized:', window.customerAnalytics?.isInitialized);
```

### Element Availability Check
```javascript
// Check critical elements
window.customerAnalytics?.logElementAvailability();

// Check specific element
console.log('Filter element:', !!document.getElementById('customerAlertFilter'));
console.log('Table body:', !!document.getElementById('customerAnalyticsTableBody'));
console.log('Modal:', !!document.getElementById('customerTransactionDetailsModal'));
```

### Data Status Check
```javascript
// Check data processing
console.log('Customer data size:', window.customerAnalytics?.customerData.size);
console.log('Alert customers:', window.customerAnalytics?.alertCustomers.size);
console.log('All transactions:', window.customerAnalytics?.allTransactions.length);

// Check raw data
console.log('Raw data:', window.dataProcessor?.rawData?.length);
```

### Force Refresh
```javascript
// Force refresh customer analytics
window.customerAnalytics?.refreshAnalytics();

// Force reprocess data
if (window.dataProcessor?.rawData) {
    window.customerAnalytics?.processTransactionData(window.dataProcessor.rawData);
}
```

## Common Issues and Solutions

### Issue 1: Filters Not Working
**Symptoms**: Changing filters doesn't update the table
**Solution**: 
1. Check console for element availability errors
2. Run `window.customerAnalytics?.logElementAvailability()`
3. Ensure page is fully loaded before testing

### Issue 2: Modal Not Displaying
**Symptoms**: Clicking "View Details" doesn't show modal
**Solution**:
1. Check console for modal element errors
2. Verify modal HTML elements exist in DOM
3. Run `console.log('Modal element:', !!document.getElementById('customerTransactionDetailsModal'))`

### Issue 3: No Alert Customers Shown
**Symptoms**: Table shows "No high-value customer alerts detected"
**Solution**:
1. Check if data has been loaded: `console.log('Raw data:', window.dataProcessor?.rawData?.length)`
2. Run test alerts: `window.customerAnalytics?.testCustomerAlerts()`
3. Check thresholds: MMK ≥1B, USD ≥10K

### Issue 4: Console Errors
**Symptoms**: Red error messages in console
**Solution**:
1. Note the specific error message
2. Check if all required scripts are loaded
3. Verify DOM elements exist
4. Try refreshing the page

## Success Criteria

The fixes are working correctly if:

✅ **Filter UI**
- All three filter controls respond to changes
- Table updates immediately when filters change
- Console shows filter change events

✅ **High Alerts**
- Alert customers appear in the table
- "View Details" buttons are clickable
- Modal displays with correct customer information
- Transaction details are shown in modal

✅ **Data Processing**
- Transaction data is processed without errors
- Customer data is grouped correctly
- Alerts are generated for high-value customers
- Console shows detailed processing information

✅ **No Console Errors**
- No red error messages in console
- All debug messages show successful operations
- Element availability checks pass

## Reporting Issues

If problems persist after testing:

1. **Capture Console Output**
   - Copy all console messages (especially errors)
   - Note the specific test that failed

2. **Provide Context**
   - Browser version and type
   - CSV file used for testing
   - Specific steps that led to the issue

3. **Include Debug Information**
   - Run `debugCustomerAnalytics()` and include output
   - Include element availability check results
   - Note any missing DOM elements

The enhanced debugging should make it easy to identify and resolve any remaining issues.
