/**
 * Performance Monitor
 * Monitors memory usage, CPU performance, and provides optimization utilities
 */

class PerformanceMonitor {
    constructor() {
        this.isInitialized = false;
        this.memoryLimit = 2048; // 2GB in MB (increased from 1GB)
        this.warningThreshold = 0.85; // 85% of memory limit (increased from 80%)
        this.criticalThreshold = 0.95; // 95% of memory limit (increased from 90%)

        // Performance metrics
        this.metrics = {
            memoryUsage: 0,
            cpuUsage: 0,
            fileProcessingTime: 0,
            ttrProcessingTime: 0,
            lastGarbageCollection: Date.now()
        };

        // Monitoring intervals
        this.monitoringInterval = null;
        this.gcInterval = null;

        // Performance optimization flags
        this.optimizationMode = 'normal'; // normal, memory-saver, performance

        this.init();
    }

    // Initialize performance monitoring
    init() {
        if (this.isInitialized) return;

        // Start memory monitoring
        this.startMemoryMonitoring();

        // Set up garbage collection scheduling
        this.scheduleGarbageCollection();

        // Set up performance event listeners
        this.setupPerformanceListeners();

        this.isInitialized = true;
        console.log('Performance Monitor initialized');
    }

    // Start monitoring memory usage
    startMemoryMonitoring() {
        this.monitoringInterval = setInterval(() => {
            this.updateMemoryMetrics();
            this.checkMemoryThresholds();
        }, 5000); // Check every 5 seconds
    }

    // Update memory metrics with browser compatibility
    updateMemoryMetrics() {
        // Check browser compatibility first
        if (!BROWSER_SUPPORT?.PERFORMANCE_API && typeof performance === 'undefined') {
            console.warn('Performance API not supported in this browser');
            return null;
        }

        if (performance && performance.memory) {
            const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
            const limit = Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024);

            this.metrics.memoryUsage = used;

            // Update UI if available
            this.updateMemoryDisplay(used, total, limit);

            return { used, total, limit };
        } else {
            // Fallback for browsers without performance.memory
            console.warn('performance.memory not available, using estimated values');
            const estimatedUsage = this.estimateMemoryUsage();
            this.updateMemoryDisplay(estimatedUsage, estimatedUsage * 1.5, estimatedUsage * 2);
            return { used: estimatedUsage, total: estimatedUsage * 1.5, limit: estimatedUsage * 2 };
        }
    }

    // Check memory thresholds and take action
    checkMemoryThresholds() {
        const memoryInfo = this.updateMemoryMetrics();
        if (!memoryInfo) return;

        const usageRatio = memoryInfo.used / this.memoryLimit;

        if (usageRatio >= this.criticalThreshold) {
            this.handleCriticalMemory();
        } else if (usageRatio >= this.warningThreshold) {
            this.handleMemoryWarning();
        }
    }

    // Handle critical memory situation
    handleCriticalMemory() {
        console.warn('Critical memory usage detected, initiating emergency cleanup');

        // Switch to memory-saver mode
        this.optimizationMode = 'memory-saver';

        // Force garbage collection
        this.forceGarbageCollection();

        // Clear non-essential caches
        this.clearCaches();

        // Notify user
        if (window.app) {
            window.app.showNotification(
                'High memory usage detected. Switching to memory-saver mode.',
                'warning',
                5000
            );
        }
    }

    // Handle memory warning
    handleMemoryWarning() {
        console.warn('Memory usage approaching limit, optimizing performance');

        // Trigger garbage collection
        this.forceGarbageCollection();

        // Reduce processing chunk sizes
        this.optimizeChunkSizes();
    }

    // Force garbage collection
    forceGarbageCollection() {
        if (window.gc) {
            window.gc();
        } else {
            // Indirect garbage collection trigger
            const largeArray = new Array(1000000).fill(null);
            largeArray.length = 0;
        }

        this.metrics.lastGarbageCollection = Date.now();
        console.log('Garbage collection triggered');
    }

    // Schedule regular garbage collection
    scheduleGarbageCollection() {
        this.gcInterval = setInterval(() => {
            // Only trigger GC if memory usage is above 50%
            const memoryInfo = this.updateMemoryMetrics();
            if (memoryInfo && (memoryInfo.used / this.memoryLimit) > 0.5) {
                this.forceGarbageCollection();
            }
        }, 30000); // Every 30 seconds
    }

    // Clear non-essential caches
    clearCaches() {
        // Clear TTR customer history cache
        if (window.ttrMonitoring && window.ttrMonitoring.rules) {
            window.ttrMonitoring.rules.customerHistory.clear();
            console.log('TTR customer history cache cleared');
        }

        // Clear file handler caches
        if (window.fileHandler) {
            window.fileHandler.processedData = [];
            console.log('File handler cache cleared');
        }


    }

    // Optimize chunk sizes based on memory usage
    optimizeChunkSizes() {
        const memoryInfo = this.updateMemoryMetrics();
        if (!memoryInfo) return;

        const usageRatio = memoryInfo.used / this.memoryLimit;

        if (window.fileHandler) {
            if (usageRatio > 0.7) {
                // Reduce chunk size for high memory usage
                window.fileHandler.chunkSize = 256 * 1024; // 256KB
            } else if (usageRatio < 0.3) {
                // Increase chunk size for low memory usage
                window.fileHandler.chunkSize = 1024 * 1024; // 1MB
            }
        }
    }

    // Set up performance event listeners
    setupPerformanceListeners() {
        // Listen for file processing events
        document.addEventListener('fileProcessingStart', (e) => {
            this.metrics.fileProcessingStartTime = performance.now();
        });

        document.addEventListener('fileProcessingEnd', (e) => {
            if (this.metrics.fileProcessingStartTime) {
                this.metrics.fileProcessingTime = performance.now() - this.metrics.fileProcessingStartTime;
                console.log(`File processing completed in ${this.metrics.fileProcessingTime.toFixed(2)}ms`);
            }
        });

        // Listen for TTR processing events
        document.addEventListener('ttrProcessingStart', (e) => {
            this.metrics.ttrProcessingStartTime = performance.now();
        });

        document.addEventListener('ttrProcessingEnd', (e) => {
            if (this.metrics.ttrProcessingStartTime) {
                this.metrics.ttrProcessingTime = performance.now() - this.metrics.ttrProcessingStartTime;
                console.log(`TTR processing completed in ${this.metrics.ttrProcessingTime.toFixed(2)}ms`);
            }
        });
    }

    // Update memory display in UI
    updateMemoryDisplay(used, total, limit) {
        const memoryElement = document.getElementById('memoryUsage');
        if (memoryElement) {
            const usagePercent = Math.round((used / this.memoryLimit) * 100);
            memoryElement.textContent = `Memory: ${used}MB / ${this.memoryLimit}MB (${usagePercent}%)`;

            // Update color based on usage
            memoryElement.className = 'memory-usage';
            if (usagePercent >= 90) {
                memoryElement.classList.add('critical');
            } else if (usagePercent >= 80) {
                memoryElement.classList.add('warning');
            } else {
                memoryElement.classList.add('normal');
            }
        }
    }

    // Get current performance metrics
    getMetrics() {
        return {
            ...this.metrics,
            memoryInfo: this.updateMemoryMetrics(),
            optimizationMode: this.optimizationMode,
            timestamp: Date.now()
        };
    }

    // Set optimization mode
    setOptimizationMode(mode) {
        this.optimizationMode = mode;

        switch (mode) {
            case 'memory-saver':
                this.enableMemorySaverMode();
                break;
            case 'performance':
                this.enablePerformanceMode();
                break;
            default:
                this.enableNormalMode();
        }

        console.log(`Optimization mode set to: ${mode}`);
    }

    // Enable memory-saver mode
    enableMemorySaverMode() {
        // Reduce chunk sizes
        if (window.fileHandler) {
            window.fileHandler.chunkSize = 128 * 1024; // 128KB
        }

        // Increase garbage collection frequency
        if (this.gcInterval) {
            clearInterval(this.gcInterval);
            this.scheduleGarbageCollection(15000); // Every 15 seconds
        }

        // Reduce TTR alert limit
        if (window.ttrMonitoring) {
            window.ttrMonitoring.config.maxAlerts = 50000;
        }
    }

    // Enable performance mode
    enablePerformanceMode() {
        // Increase chunk sizes
        if (window.fileHandler) {
            window.fileHandler.chunkSize = 2048 * 1024; // 2MB
        }

        // Reduce garbage collection frequency
        if (this.gcInterval) {
            clearInterval(this.gcInterval);
            this.scheduleGarbageCollection(60000); // Every 60 seconds
        }

        // Increase TTR alert limit
        if (window.ttrMonitoring) {
            window.ttrMonitoring.config.maxAlerts = 200000;
        }
    }

    // Enable normal mode
    enableNormalMode() {
        // Reset to default chunk sizes
        if (window.fileHandler) {
            window.fileHandler.chunkSize = 512 * 1024; // 512KB
        }

        // Reset garbage collection frequency
        if (this.gcInterval) {
            clearInterval(this.gcInterval);
            this.scheduleGarbageCollection(30000); // Every 30 seconds
        }

        // Reset TTR alert limit
        if (window.ttrMonitoring) {
            window.ttrMonitoring.config.maxAlerts = 100000;
        }
    }

    // Estimate memory usage for browsers without performance.memory
    estimateMemoryUsage() {
        try {
            // Rough estimation based on DOM elements and data structures
            const domElements = document.querySelectorAll('*').length;
            const estimatedDomMemory = domElements * 0.001; // ~1KB per element estimate

            // Add estimated data memory
            let dataMemory = 0;
            if (window.dataProcessor && window.dataProcessor.rawData) {
                dataMemory = window.dataProcessor.rawData.length * 0.002; // ~2KB per record estimate
            }

            // Add base application memory
            const baseMemory = 10; // 10MB base

            return Math.round(baseMemory + estimatedDomMemory + dataMemory);
        } catch (error) {
            console.warn('Memory estimation failed:', error);
            return 50; // Default 50MB estimate
        }
    }

    // Get current memory usage with fallback
    getMemoryUsage() {
        if (performance && performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        } else {
            // Fallback estimation
            const estimated = this.estimateMemoryUsage();
            return {
                used: estimated,
                total: estimated * 1.5,
                limit: estimated * 2,
                estimated: true
            };
        }
    }

    // Cleanup and destroy
    destroy() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }

        if (this.gcInterval) {
            clearInterval(this.gcInterval);
        }

        this.isInitialized = false;
        console.log('Performance Monitor destroyed');
    }
}

// Create global instance
window.performanceMonitor = new PerformanceMonitor();
