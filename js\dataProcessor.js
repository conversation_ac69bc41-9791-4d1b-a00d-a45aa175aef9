/**
 * Data Processor Module
 * Handles data aggregation, filtering, and calculations
 */

class DataProcessor {
    constructor() {
        // Development mode detection for conditional logging
        this.isDevelopmentMode = this.detectDevelopmentMode();

        this.rawData = [];
        this.filteredData = [];
        this.summaryMetrics = {
            totalTransactions: 0,
            totalAmount: 0,
            hocCount: 0,
            hocAmount: 0,
            hocAmountMMK: 0,
            hocAmountUSD: 0,
            hocCreditCount: 0,
            hocCreditAmount: 0,
            hocCreditAmountMMK: 0,
            hocCreditAmountUSD: 0,
            hocDebitCount: 0,
            hocDebitAmount: 0,
            hocDebitAmountMMK: 0,
            hocDebitAmountUSD: 0,
            ibdCount: 0,
            ibdAmount: 0,
            ibdAmountMMK: 0,
            ibdAmountUSD: 0,
            ibdCreditCount: 0,
            ibdCreditAmount: 0,
            ibdCreditAmountMMK: 0,
            ibdCreditAmountUSD: 0,
            ibdDebitCount: 0,
            ibdDebitAmount: 0,
            ibdDebitAmountMMK: 0,
            ibdDebitAmountUSD: 0,
            wuCount: 0,
            wuAmount: 0,
            wuAmountMMK: 0,
            wuAmountUSD: 0,
            wuCreditCount: 0,
            wuCreditAmount: 0,
            wuCreditAmountMMK: 0,
            wuCreditAmountUSD: 0,
            wuDebitCount: 0,
            wuDebitAmount: 0,
            wuDebitAmountMMK: 0,
            wuDebitAmountUSD: 0,
            // HOC currency-specific credit amounts
            hocCreditAmountCNY: 0,
            hocCreditAmountEUR: 0,
            hocCreditAmountINR: 0,
            hocCreditAmountJPY: 0,
            hocCreditAmountSGD: 0,
            hocCreditAmountTHB: 0,
            // HOC currency-specific debit amounts
            hocDebitAmountCNY: 0,
            hocDebitAmountEUR: 0,
            hocDebitAmountINR: 0,
            hocDebitAmountJPY: 0,
            hocDebitAmountSGD: 0,
            hocDebitAmountTHB: 0,
            // IBD currency-specific credit amounts
            ibdCreditAmountCNY: 0,
            ibdCreditAmountEUR: 0,
            ibdCreditAmountINR: 0,
            ibdCreditAmountJPY: 0,
            ibdCreditAmountSGD: 0,
            ibdCreditAmountTHB: 0,
            // IBD currency-specific debit amounts
            ibdDebitAmountCNY: 0,
            ibdDebitAmountEUR: 0,
            ibdDebitAmountINR: 0,
            ibdDebitAmountJPY: 0,
            ibdDebitAmountSGD: 0,
            ibdDebitAmountTHB: 0,
            // WU currency-specific credit amounts
            wuCreditAmountCNY: 0,
            wuCreditAmountEUR: 0,
            wuCreditAmountINR: 0,
            wuCreditAmountJPY: 0,
            wuCreditAmountSGD: 0,
            wuCreditAmountTHB: 0,
            // WU currency-specific debit amounts
            wuDebitAmountCNY: 0,
            wuDebitAmountEUR: 0,
            wuDebitAmountINR: 0,
            wuDebitAmountJPY: 0,
            wuDebitAmountSGD: 0,
            wuDebitAmountTHB: 0,
            hocUniqueSerialCount: 0,
            ibdUniqueSerialCount: 0,
            wuUniqueSerialCount: 0,
            totalUniqueSerialCount: 0,
            currencyCounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            currencyAmounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            currencyCreditAmounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            currencyDebitAmounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            // High-value transaction counts by currency
            highValueTransactionCounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            highValueTransactionCount: 0,  // Count of transactions > 1B MMK
            highValueTransactionCountUSD: 0  // Count of transactions > 10K USD
        };

        // Sets to track unique serial numbers
        this.hocSerialNumbers = new Set();
        this.ibdSerialNumbers = new Set();
        this.wuSerialNumbers = new Set();
        this.allSerialNumbers = new Set();

        // High-value transaction thresholds
        this.HIGH_VALUE_THRESHOLD = 1000000000; // 1 billion MMK
        this.HIGH_VALUE_THRESHOLD_USD = 10000; // 10,000 USD

        // Track previous high-value count for change notifications
        this.previousHighValueCount = 0;
        this.previousHighValueCountUSD = 0;

        // Track high-value transaction counts per date
        this.dateHighValueCounts = new Map();
        this.dateHighValueCountsUSD = new Map();

        // Track high-value transactions per file for proper file management
        this.fileHighValueTransactions = new Map(); // fileId -> array of high-value transactions (MMK)
        this.fileHighValueTransactionsUSD = new Map(); // fileId -> array of high-value transactions (USD)
        this.fileHighValueCounts = new Map(); // fileId -> { dateCountMap, totalCount }
        this.fileHighValueCountsUSD = new Map(); // fileId -> { dateCountMap, totalCount }

        // Track file processing and removal counts for high-value transaction analytics
        this.highValueFileMetrics = {
            filesProcessed: 0,      // Total files with high-value transactions processed
            filesRemoved: 0,        // Total files with high-value transactions removed
            activeFiles: 0          // Currently active files with high-value transactions
        };

        // Track toggle state for persistence across updates
        this.breakdownToggleState = {
            isExpanded: false,
            isInitialized: false
        };

        // Set to track processed files (by unique identifier)
        this.processedFiles = new Set();

        // Flag to prevent frequent validation scheduling
        this.validationScheduled = false;
    }

    // Detect development mode for conditional logging
    detectDevelopmentMode() {
        // Check for common development indicators
        return (
            window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1' ||
            window.location.hostname === '' ||
            window.location.protocol === 'file:' ||
            window.location.port !== '' ||
            (typeof window !== 'undefined' && window.location.search.includes('debug=true'))
        );
    }

    // Production-safe logging methods that prevent sensitive data exposure
    logDebug(message, ...args) {
        if (this.isDevelopmentMode && this.isSafeToLog(message, args)) {
            console.log(`[DEBUG] ${this.sanitizeLogMessage(message)}`, ...this.sanitizeLogArgs(args));
        }
    }

    logInfo(message, ...args) {
        // Always log info messages but with cleaner format in production and sanitization
        const sanitizedMessage = this.sanitizeLogMessage(message);
        const sanitizedArgs = this.sanitizeLogArgs(args);

        if (this.isDevelopmentMode) {
            console.log(`[INFO] ${sanitizedMessage}`, ...sanitizedArgs);
        } else {
            console.log(`[INFO] ${sanitizedMessage}`);
        }
    }

    logWarn(message, ...args) {
        // Always log warnings but sanitize sensitive data
        console.warn(`[WARN] ${this.sanitizeLogMessage(message)}`, ...this.sanitizeLogArgs(args));
    }

    logError(message, ...args) {
        // Always log errors but sanitize sensitive data
        console.error(`[ERROR] ${this.sanitizeLogMessage(message)}`, ...this.sanitizeLogArgs(args));
    }

    // Check if it's safe to log the message (doesn't contain sensitive data)
    isSafeToLog(message, args) {
        const sensitivePatterns = [
            /transaction.*amount/i,
            /customer.*name/i,
            /account.*number/i,
            /serial.*number/i,
            /\b\d{10,}\b/, // Long numbers that might be account numbers
            /\b\d+\.\d{2}\b.*MMK|USD/i, // Currency amounts
            /TRANSACTION_AMOUNT/i,
            /CUSTOMER_ID/i,
            /ACCOUNT_NUMBER/i
        ];

        const messageStr = String(message);
        const argsStr = args.map(arg => String(arg)).join(' ');
        const fullText = messageStr + ' ' + argsStr;

        return !sensitivePatterns.some(pattern => pattern.test(fullText));
    }

    // Sanitize log messages to remove sensitive data
    sanitizeLogMessage(message) {
        if (typeof message !== 'string') {
            return String(message);
        }

        return message
            .replace(/\b\d{10,}\b/g, '[ACCOUNT_NUMBER]') // Replace long numbers
            .replace(/\b\d+\.\d{2}\b\s*(MMK|USD)/gi, '[AMOUNT] $1') // Replace currency amounts
            .replace(/TRANSACTION_AMOUNT[=:]\s*[\d.]+/gi, 'TRANSACTION_AMOUNT=[AMOUNT]')
            .replace(/CUSTOMER_ID[=:]\s*\w+/gi, 'CUSTOMER_ID=[CUSTOMER_ID]')
            .replace(/ACCOUNT_NUMBER[=:]\s*\w+/gi, 'ACCOUNT_NUMBER=[ACCOUNT_NUMBER]');
    }

    // Sanitize log arguments to remove sensitive data
    sanitizeLogArgs(args) {
        return args.map(arg => {
            if (typeof arg === 'object' && arg !== null) {
                // For objects, create a sanitized copy
                const sanitized = {};
                for (const key in arg) {
                    if (this.isSensitiveField(key)) {
                        sanitized[key] = '[REDACTED]';
                    } else if (typeof arg[key] === 'string' || typeof arg[key] === 'number') {
                        sanitized[key] = this.sanitizeValue(String(arg[key]));
                    } else {
                        sanitized[key] = arg[key];
                    }
                }
                return sanitized;
            } else {
                return this.sanitizeValue(String(arg));
            }
        });
    }

    // Check if a field name indicates sensitive data
    isSensitiveField(fieldName) {
        const sensitiveFields = [
            'TRANSACTION_AMOUNT',
            'CUSTOMER_ID',
            'CUSTOMER_NAME',
            'ACCOUNT_NUMBER',
            'SERIAL_NO',
            'SERIAL_NUMBER',
            'TRANSACTION_REF_NUMBER'
        ];
        return sensitiveFields.includes(fieldName.toUpperCase());
    }

    // Sanitize individual values
    sanitizeValue(value) {
        return value
            .replace(/\b\d{10,}\b/g, '[ACCOUNT_NUMBER]')
            .replace(/\b\d+\.\d{2}\b/g, '[AMOUNT]');
    }

    // Helper function to safely add numeric values
    safeAdd(a, b) {
        const numA = typeof a === 'number' && !isNaN(a) ? a : 0;
        const numB = typeof b === 'number' && !isNaN(b) ? b : 0;
        return numA + numB;
    }



    // Process the raw data
    processData(data) {
        this.rawData = data || [];
        this.filteredData = [...this.rawData];

        // Reset metrics before calculating
        this.resetMetrics();

        // Calculate summary metrics
        this.calculateMetrics(this.filteredData);

        // Auto-fix WU processing if needed (after initial calculation)
        this.autoFixWUProcessingIfNeeded();

        // Update the UI with the calculated metrics
        this.updateUI();







        // Log data status
        if (this.rawData.length === 0) {
            this.logDebug('Data processor reset with empty data');
        } else {
            this.logInfo(`Data processor updated with ${this.rawData.length} records`);
        }
    }

    // Update metrics incrementally (for memory optimization)
    updateMetrics(newMetrics) {
        // Check if newMetrics is valid
        if (!newMetrics) {
            this.logWarn('Received invalid metrics, skipping update');
            return;
        }

        // If metrics are not initialized, initialize them
        if (!this.summaryMetrics) {
            this.resetMetrics();
        }

        // Log incoming metrics for debugging
        if (newMetrics.totalTransactions > 0) {
            this.logDebug(`DataProcessor: Updating metrics with ${newMetrics.totalTransactions} new transactions`);
            this.logDebug(`Incoming metrics breakdown - HOC: ${newMetrics.hocCount}, IBD: ${newMetrics.ibdCount}`);
        }

        // Store previous values for logging
        const prevTotal = this.summaryMetrics.totalTransactions;

        // Add the new metrics to the existing ones with proper validation
        this.summaryMetrics.totalTransactions = this.safeAdd(this.summaryMetrics.totalTransactions, newMetrics.totalTransactions);
        this.summaryMetrics.totalAmount = this.safeAdd(this.summaryMetrics.totalAmount, newMetrics.totalAmount);
        this.summaryMetrics.hocCount = this.safeAdd(this.summaryMetrics.hocCount, newMetrics.hocCount);
        this.summaryMetrics.hocAmount = this.safeAdd(this.summaryMetrics.hocAmount, newMetrics.hocAmount);
        this.summaryMetrics.hocAmountMMK = this.safeAdd(this.summaryMetrics.hocAmountMMK, newMetrics.hocAmountMMK || 0);
        this.summaryMetrics.hocAmountUSD = this.safeAdd(this.summaryMetrics.hocAmountUSD, newMetrics.hocAmountUSD || 0);
        this.summaryMetrics.hocCreditCount = this.safeAdd(this.summaryMetrics.hocCreditCount, newMetrics.hocCreditCount);
        this.summaryMetrics.hocCreditAmount = this.safeAdd(this.summaryMetrics.hocCreditAmount, newMetrics.hocCreditAmount);
        this.summaryMetrics.hocCreditAmountMMK = this.safeAdd(this.summaryMetrics.hocCreditAmountMMK, newMetrics.hocCreditAmountMMK || 0);
        this.summaryMetrics.hocCreditAmountUSD = this.safeAdd(this.summaryMetrics.hocCreditAmountUSD, newMetrics.hocCreditAmountUSD || 0);
        this.summaryMetrics.hocDebitCount = this.safeAdd(this.summaryMetrics.hocDebitCount, newMetrics.hocDebitCount);
        this.summaryMetrics.hocDebitAmount = this.safeAdd(this.summaryMetrics.hocDebitAmount, newMetrics.hocDebitAmount);
        this.summaryMetrics.hocDebitAmountMMK = this.safeAdd(this.summaryMetrics.hocDebitAmountMMK, newMetrics.hocDebitAmountMMK || 0);
        this.summaryMetrics.hocDebitAmountUSD = this.safeAdd(this.summaryMetrics.hocDebitAmountUSD, newMetrics.hocDebitAmountUSD || 0);
        this.summaryMetrics.ibdCount = this.safeAdd(this.summaryMetrics.ibdCount, newMetrics.ibdCount);
        this.summaryMetrics.ibdAmount = this.safeAdd(this.summaryMetrics.ibdAmount, newMetrics.ibdAmount);
        this.summaryMetrics.ibdAmountMMK = this.safeAdd(this.summaryMetrics.ibdAmountMMK, newMetrics.ibdAmountMMK || 0);
        this.summaryMetrics.ibdAmountUSD = this.safeAdd(this.summaryMetrics.ibdAmountUSD, newMetrics.ibdAmountUSD || 0);
        this.summaryMetrics.ibdCreditCount = this.safeAdd(this.summaryMetrics.ibdCreditCount, newMetrics.ibdCreditCount);
        this.summaryMetrics.ibdCreditAmount = this.safeAdd(this.summaryMetrics.ibdCreditAmount, newMetrics.ibdCreditAmount);
        this.summaryMetrics.ibdCreditAmountMMK = this.safeAdd(this.summaryMetrics.ibdCreditAmountMMK, newMetrics.ibdCreditAmountMMK || 0);
        this.summaryMetrics.ibdCreditAmountUSD = this.safeAdd(this.summaryMetrics.ibdCreditAmountUSD, newMetrics.ibdCreditAmountUSD || 0);
        this.summaryMetrics.ibdDebitCount = this.safeAdd(this.summaryMetrics.ibdDebitCount, newMetrics.ibdDebitCount);
        this.summaryMetrics.ibdDebitAmount = this.safeAdd(this.summaryMetrics.ibdDebitAmount, newMetrics.ibdDebitAmount);
        this.summaryMetrics.ibdDebitAmountMMK = this.safeAdd(this.summaryMetrics.ibdDebitAmountMMK, newMetrics.ibdDebitAmountMMK || 0);
        this.summaryMetrics.ibdDebitAmountUSD = this.safeAdd(this.summaryMetrics.ibdDebitAmountUSD, newMetrics.ibdDebitAmountUSD || 0);

        // Add WU metrics
        this.summaryMetrics.wuCount = this.safeAdd(this.summaryMetrics.wuCount || 0, newMetrics.wuCount || 0);
        this.summaryMetrics.wuAmount = this.safeAdd(this.summaryMetrics.wuAmount || 0, newMetrics.wuAmount || 0);
        this.summaryMetrics.wuAmountMMK = this.safeAdd(this.summaryMetrics.wuAmountMMK || 0, newMetrics.wuAmountMMK || 0);
        this.summaryMetrics.wuAmountUSD = this.safeAdd(this.summaryMetrics.wuAmountUSD || 0, newMetrics.wuAmountUSD || 0);
        this.summaryMetrics.wuCreditCount = this.safeAdd(this.summaryMetrics.wuCreditCount || 0, newMetrics.wuCreditCount || 0);
        this.summaryMetrics.wuCreditAmount = this.safeAdd(this.summaryMetrics.wuCreditAmount || 0, newMetrics.wuCreditAmount || 0);
        this.summaryMetrics.wuCreditAmountMMK = this.safeAdd(this.summaryMetrics.wuCreditAmountMMK || 0, newMetrics.wuCreditAmountMMK || 0);
        this.summaryMetrics.wuCreditAmountUSD = this.safeAdd(this.summaryMetrics.wuCreditAmountUSD || 0, newMetrics.wuCreditAmountUSD || 0);
        this.summaryMetrics.wuDebitCount = this.safeAdd(this.summaryMetrics.wuDebitCount || 0, newMetrics.wuDebitCount || 0);
        this.summaryMetrics.wuDebitAmount = this.safeAdd(this.summaryMetrics.wuDebitAmount || 0, newMetrics.wuDebitAmount || 0);
        this.summaryMetrics.wuDebitAmountMMK = this.safeAdd(this.summaryMetrics.wuDebitAmountMMK || 0, newMetrics.wuDebitAmountMMK || 0);
        this.summaryMetrics.wuDebitAmountUSD = this.safeAdd(this.summaryMetrics.wuDebitAmountUSD || 0, newMetrics.wuDebitAmountUSD || 0);

        // Merge serial numbers from new metrics into global sets if provided
        if (newMetrics.hocSerialNumbers && newMetrics.hocSerialNumbers instanceof Set) {
            newMetrics.hocSerialNumbers.forEach(serialNo => {
                this.hocSerialNumbers.add(serialNo);
                this.allSerialNumbers.add(serialNo);
            });
        }
        if (newMetrics.ibdSerialNumbers && newMetrics.ibdSerialNumbers instanceof Set) {
            newMetrics.ibdSerialNumbers.forEach(serialNo => {
                this.ibdSerialNumbers.add(serialNo);
                this.allSerialNumbers.add(serialNo);
            });
        }
        if (newMetrics.wuSerialNumbers && newMetrics.wuSerialNumbers instanceof Set) {
            newMetrics.wuSerialNumbers.forEach(serialNo => {
                this.wuSerialNumbers.add(serialNo);
                this.allSerialNumbers.add(serialNo);
            });
        }

        // Update unique serial counts based on actual Set sizes (not accumulated)
        // Serial numbers are unique across all files, so we use the Set sizes directly
        this.summaryMetrics.hocUniqueSerialCount = this.hocSerialNumbers ? this.hocSerialNumbers.size : 0;
        this.summaryMetrics.ibdUniqueSerialCount = this.ibdSerialNumbers ? this.ibdSerialNumbers.size : 0;
        this.summaryMetrics.wuUniqueSerialCount = this.wuSerialNumbers ? this.wuSerialNumbers.size : 0;
        this.summaryMetrics.totalUniqueSerialCount = this.allSerialNumbers ? this.allSerialNumbers.size : 0;

        // Update currency counts with proper validation for all supported currencies
        if (newMetrics.currencyCounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (newMetrics.currencyCounts[currency] !== undefined) {
                    this.summaryMetrics.currencyCounts[currency] = this.safeAdd(this.summaryMetrics.currencyCounts[currency], newMetrics.currencyCounts[currency]);
                }
            });
        }

        if (newMetrics.currencyAmounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (newMetrics.currencyAmounts[currency] !== undefined) {
                    this.summaryMetrics.currencyAmounts[currency] = this.safeAdd(this.summaryMetrics.currencyAmounts[currency], newMetrics.currencyAmounts[currency]);
                }
            });
        }

        if (newMetrics.currencyCreditAmounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (newMetrics.currencyCreditAmounts[currency] !== undefined) {
                    this.summaryMetrics.currencyCreditAmounts[currency] = this.safeAdd(this.summaryMetrics.currencyCreditAmounts[currency], newMetrics.currencyCreditAmounts[currency]);
                }
            });
        }

        if (newMetrics.currencyDebitAmounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (newMetrics.currencyDebitAmounts[currency] !== undefined) {
                    this.summaryMetrics.currencyDebitAmounts[currency] = this.safeAdd(this.summaryMetrics.currencyDebitAmounts[currency], newMetrics.currencyDebitAmounts[currency]);
                }
            });
        }

        // Update high-value transaction counts by currency
        if (newMetrics.highValueTransactionCounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (newMetrics.highValueTransactionCounts[currency] !== undefined) {
                    this.summaryMetrics.highValueTransactionCounts[currency] = this.safeAdd(this.summaryMetrics.highValueTransactionCounts[currency], newMetrics.highValueTransactionCounts[currency]);
                }
            });
        }

        // Update legacy high-value transaction counts for backward compatibility
        if (newMetrics.highValueTransactionCount !== undefined) {
            this.summaryMetrics.highValueTransactionCount = this.safeAdd(this.summaryMetrics.highValueTransactionCount, newMetrics.highValueTransactionCount);
        }
        if (newMetrics.highValueTransactionCountUSD !== undefined) {
            this.summaryMetrics.highValueTransactionCountUSD = this.safeAdd(this.summaryMetrics.highValueTransactionCountUSD, newMetrics.highValueTransactionCountUSD);
        }

        // Log the change in metrics
        if (newMetrics.totalTransactions > 0) {
            this.logDebug(`Metrics updated: ${prevTotal} → ${this.summaryMetrics.totalTransactions} transactions (added ${this.summaryMetrics.totalTransactions - prevTotal})`);
            if (newMetrics.highValueTransactionCount > 0) {
                this.logDebug(`High-value transactions added: ${newMetrics.highValueTransactionCount} (total: ${this.summaryMetrics.highValueTransactionCount})`);
            }
        }

        // Recalculate currency amounts to ensure consistency after adding metrics
        // This ensures Summary Metrics use the same calculation logic as Transaction Breakdown
        this.summaryMetrics.currencyAmounts.MMK = this.safeAdd(
            this.safeAdd(this.summaryMetrics.hocAmountMMK || 0, this.summaryMetrics.ibdAmountMMK || 0),
            this.summaryMetrics.wuAmountMMK || 0
        );
        this.summaryMetrics.currencyAmounts.USD = this.safeAdd(
            this.safeAdd(this.summaryMetrics.hocAmountUSD || 0, this.summaryMetrics.ibdAmountUSD || 0),
            this.summaryMetrics.wuAmountUSD || 0
        );

        // For other currencies, calculate total amounts (credit + debit)
        const otherCurrencies = ['SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        otherCurrencies.forEach(currency => {
            this.summaryMetrics.currencyAmounts[currency] = this.safeAdd(
                this.summaryMetrics.currencyCreditAmounts[currency] || 0,
                this.summaryMetrics.currencyDebitAmounts[currency] || 0
            );
        });

        // Auto-fix WU processing if needed (after metrics update)
        setTimeout(() => {
            this.autoFixWUProcessingIfNeeded();
        }, 50);

        // Update the UI with the updated metrics
        this.updateUI();






    }

    // Subtract metrics (for file removal)
    subtractMetrics(metricsToSubtract) {
        // Check if metricsToSubtract is valid
        if (!metricsToSubtract) {
            this.logWarn('Received invalid metrics to subtract, skipping operation');
            return;
        }

        // If metrics are not initialized, nothing to subtract from
        if (!this.summaryMetrics) {
            this.logWarn('No metrics initialized, nothing to subtract from');
            return;
        }

        // Log metrics being subtracted for debugging
        if (metricsToSubtract.totalTransactions > 0) {
            this.logDebug(`DataProcessor: Subtracting ${metricsToSubtract.totalTransactions} transactions from metrics`);
            this.logDebug(`Subtracting metrics breakdown - HOC: ${metricsToSubtract.hocCount}, IBD: ${metricsToSubtract.ibdCount}`);
        }

        // Helper function to safely subtract numeric values (never go below 0)
        const safeSubtract = (a, b) => {
            const numA = typeof a === 'number' && !isNaN(a) ? a : 0;
            const numB = typeof b === 'number' && !isNaN(b) ? b : 0;
            return Math.max(0, numA - numB);
        };

        // Store previous values for logging
        const prevTotal = this.summaryMetrics.totalTransactions;

        // Subtract the metrics with proper validation
        this.summaryMetrics.totalTransactions = safeSubtract(this.summaryMetrics.totalTransactions, metricsToSubtract.totalTransactions);
        this.summaryMetrics.totalAmount = safeSubtract(this.summaryMetrics.totalAmount, metricsToSubtract.totalAmount);
        this.summaryMetrics.hocCount = safeSubtract(this.summaryMetrics.hocCount, metricsToSubtract.hocCount);
        this.summaryMetrics.hocAmount = safeSubtract(this.summaryMetrics.hocAmount, metricsToSubtract.hocAmount);
        this.summaryMetrics.hocAmountMMK = safeSubtract(this.summaryMetrics.hocAmountMMK, metricsToSubtract.hocAmountMMK || 0);
        this.summaryMetrics.hocAmountUSD = safeSubtract(this.summaryMetrics.hocAmountUSD, metricsToSubtract.hocAmountUSD || 0);
        this.summaryMetrics.hocCreditCount = safeSubtract(this.summaryMetrics.hocCreditCount, metricsToSubtract.hocCreditCount);
        this.summaryMetrics.hocCreditAmount = safeSubtract(this.summaryMetrics.hocCreditAmount, metricsToSubtract.hocCreditAmount);
        this.summaryMetrics.hocCreditAmountMMK = safeSubtract(this.summaryMetrics.hocCreditAmountMMK, metricsToSubtract.hocCreditAmountMMK || 0);
        this.summaryMetrics.hocCreditAmountUSD = safeSubtract(this.summaryMetrics.hocCreditAmountUSD, metricsToSubtract.hocCreditAmountUSD || 0);
        this.summaryMetrics.hocDebitCount = safeSubtract(this.summaryMetrics.hocDebitCount, metricsToSubtract.hocDebitCount);
        this.summaryMetrics.hocDebitAmount = safeSubtract(this.summaryMetrics.hocDebitAmount, metricsToSubtract.hocDebitAmount);
        this.summaryMetrics.hocDebitAmountMMK = safeSubtract(this.summaryMetrics.hocDebitAmountMMK, metricsToSubtract.hocDebitAmountMMK || 0);
        this.summaryMetrics.hocDebitAmountUSD = safeSubtract(this.summaryMetrics.hocDebitAmountUSD, metricsToSubtract.hocDebitAmountUSD || 0);
        this.summaryMetrics.ibdCount = safeSubtract(this.summaryMetrics.ibdCount, metricsToSubtract.ibdCount);
        this.summaryMetrics.ibdAmount = safeSubtract(this.summaryMetrics.ibdAmount, metricsToSubtract.ibdAmount);
        this.summaryMetrics.ibdAmountMMK = safeSubtract(this.summaryMetrics.ibdAmountMMK, metricsToSubtract.ibdAmountMMK || 0);
        this.summaryMetrics.ibdAmountUSD = safeSubtract(this.summaryMetrics.ibdAmountUSD, metricsToSubtract.ibdAmountUSD || 0);
        this.summaryMetrics.ibdCreditCount = safeSubtract(this.summaryMetrics.ibdCreditCount, metricsToSubtract.ibdCreditCount);
        this.summaryMetrics.ibdCreditAmount = safeSubtract(this.summaryMetrics.ibdCreditAmount, metricsToSubtract.ibdCreditAmount);
        this.summaryMetrics.ibdCreditAmountMMK = safeSubtract(this.summaryMetrics.ibdCreditAmountMMK, metricsToSubtract.ibdCreditAmountMMK || 0);
        this.summaryMetrics.ibdCreditAmountUSD = safeSubtract(this.summaryMetrics.ibdCreditAmountUSD, metricsToSubtract.ibdCreditAmountUSD || 0);
        this.summaryMetrics.ibdDebitCount = safeSubtract(this.summaryMetrics.ibdDebitCount, metricsToSubtract.ibdDebitCount);
        this.summaryMetrics.ibdDebitAmount = safeSubtract(this.summaryMetrics.ibdDebitAmount, metricsToSubtract.ibdDebitAmount);
        this.summaryMetrics.ibdDebitAmountMMK = safeSubtract(this.summaryMetrics.ibdDebitAmountMMK, metricsToSubtract.ibdDebitAmountMMK || 0);
        this.summaryMetrics.ibdDebitAmountUSD = safeSubtract(this.summaryMetrics.ibdDebitAmountUSD, metricsToSubtract.ibdDebitAmountUSD || 0);

        // Subtract WU metrics
        this.summaryMetrics.wuCount = safeSubtract(this.summaryMetrics.wuCount || 0, metricsToSubtract.wuCount || 0);
        this.summaryMetrics.wuAmount = safeSubtract(this.summaryMetrics.wuAmount || 0, metricsToSubtract.wuAmount || 0);
        this.summaryMetrics.wuAmountMMK = safeSubtract(this.summaryMetrics.wuAmountMMK || 0, metricsToSubtract.wuAmountMMK || 0);
        this.summaryMetrics.wuAmountUSD = safeSubtract(this.summaryMetrics.wuAmountUSD || 0, metricsToSubtract.wuAmountUSD || 0);
        this.summaryMetrics.wuCreditCount = safeSubtract(this.summaryMetrics.wuCreditCount || 0, metricsToSubtract.wuCreditCount || 0);
        this.summaryMetrics.wuCreditAmount = safeSubtract(this.summaryMetrics.wuCreditAmount || 0, metricsToSubtract.wuCreditAmount || 0);
        this.summaryMetrics.wuCreditAmountMMK = safeSubtract(this.summaryMetrics.wuCreditAmountMMK || 0, metricsToSubtract.wuCreditAmountMMK || 0);
        this.summaryMetrics.wuCreditAmountUSD = safeSubtract(this.summaryMetrics.wuCreditAmountUSD || 0, metricsToSubtract.wuCreditAmountUSD || 0);
        this.summaryMetrics.wuDebitCount = safeSubtract(this.summaryMetrics.wuDebitCount || 0, metricsToSubtract.wuDebitCount || 0);
        this.summaryMetrics.wuDebitAmount = safeSubtract(this.summaryMetrics.wuDebitAmount || 0, metricsToSubtract.wuDebitAmount || 0);
        this.summaryMetrics.wuDebitAmountMMK = safeSubtract(this.summaryMetrics.wuDebitAmountMMK || 0, metricsToSubtract.wuDebitAmountMMK || 0);
        this.summaryMetrics.wuDebitAmountUSD = safeSubtract(this.summaryMetrics.wuDebitAmountUSD || 0, metricsToSubtract.wuDebitAmountUSD || 0);

        // For unique serial counts, we don't directly subtract since they're based on Sets
        // Instead, we'll just use the current size of the Sets which should be accurate
        // after serial numbers have been properly removed
        this.summaryMetrics.hocUniqueSerialCount = this.hocSerialNumbers ? this.hocSerialNumbers.size : 0;
        this.summaryMetrics.ibdUniqueSerialCount = this.ibdSerialNumbers ? this.ibdSerialNumbers.size : 0;
        this.summaryMetrics.wuUniqueSerialCount = this.wuSerialNumbers ? this.wuSerialNumbers.size : 0;
        this.summaryMetrics.totalUniqueSerialCount = this.allSerialNumbers ? this.allSerialNumbers.size : 0;

        // Update currency counts with proper validation for all supported currencies
        if (metricsToSubtract.currencyCounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (metricsToSubtract.currencyCounts[currency] !== undefined) {
                    this.summaryMetrics.currencyCounts[currency] = safeSubtract(this.summaryMetrics.currencyCounts[currency], metricsToSubtract.currencyCounts[currency]);
                }
            });
        }

        if (metricsToSubtract.currencyAmounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (metricsToSubtract.currencyAmounts[currency] !== undefined) {
                    this.summaryMetrics.currencyAmounts[currency] = safeSubtract(this.summaryMetrics.currencyAmounts[currency], metricsToSubtract.currencyAmounts[currency]);
                }
            });
        }

        if (metricsToSubtract.currencyCreditAmounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (metricsToSubtract.currencyCreditAmounts[currency] !== undefined) {
                    this.summaryMetrics.currencyCreditAmounts[currency] = safeSubtract(this.summaryMetrics.currencyCreditAmounts[currency], metricsToSubtract.currencyCreditAmounts[currency]);
                }
            });
        }

        if (metricsToSubtract.currencyDebitAmounts) {
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            supportedCurrencies.forEach(currency => {
                if (metricsToSubtract.currencyDebitAmounts[currency] !== undefined) {
                    this.summaryMetrics.currencyDebitAmounts[currency] = safeSubtract(this.summaryMetrics.currencyDebitAmounts[currency], metricsToSubtract.currencyDebitAmounts[currency]);
                }
            });
        }

        // Update high-value transaction counts
        if (metricsToSubtract.highValueTransactionCount !== undefined) {
            this.summaryMetrics.highValueTransactionCount = safeSubtract(this.summaryMetrics.highValueTransactionCount, metricsToSubtract.highValueTransactionCount);
        }
        if (metricsToSubtract.highValueTransactionCountUSD !== undefined) {
            this.summaryMetrics.highValueTransactionCountUSD = safeSubtract(this.summaryMetrics.highValueTransactionCountUSD, metricsToSubtract.highValueTransactionCountUSD);
        }

        // Log the change in metrics
        if (metricsToSubtract.totalTransactions > 0) {
            this.logDebug(`Metrics updated after subtraction: ${prevTotal} → ${this.summaryMetrics.totalTransactions} transactions (removed ${prevTotal - this.summaryMetrics.totalTransactions})`);
            this.logDebug(`Updated unique serial counts - HOC: ${this.summaryMetrics.hocUniqueSerialCount}, IBD: ${this.summaryMetrics.ibdUniqueSerialCount}, Total: ${this.summaryMetrics.totalUniqueSerialCount}`);
            if (metricsToSubtract.highValueTransactionCount > 0) {
                this.logDebug(`High-value transactions removed: ${metricsToSubtract.highValueTransactionCount} (remaining: ${this.summaryMetrics.highValueTransactionCount})`);
            }
        }

        // Recalculate currency amounts to ensure consistency after subtracting metrics
        // This ensures Summary Metrics use the same calculation logic as Transaction Breakdown
        this.summaryMetrics.currencyAmounts.MMK = this.safeAdd(
            this.safeAdd(this.summaryMetrics.hocAmountMMK || 0, this.summaryMetrics.ibdAmountMMK || 0),
            this.summaryMetrics.wuAmountMMK || 0
        );
        this.summaryMetrics.currencyAmounts.USD = this.safeAdd(
            this.safeAdd(this.summaryMetrics.hocAmountUSD || 0, this.summaryMetrics.ibdAmountUSD || 0),
            this.summaryMetrics.wuAmountUSD || 0
        );

        // For other currencies, calculate total amounts (credit + debit)
        const otherCurrencies = ['SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        otherCurrencies.forEach(currency => {
            this.summaryMetrics.currencyAmounts[currency] = this.safeAdd(
                this.summaryMetrics.currencyCreditAmounts[currency] || 0,
                this.summaryMetrics.currencyDebitAmounts[currency] || 0
            );
        });

        // Update the UI with the updated metrics
        this.updateUI();




    }

    // Reset metrics to initial state
    resetMetrics(preserveHighValueData = false) {
        this.summaryMetrics = {
            totalTransactions: 0,
            totalAmount: 0,
            hocCount: 0,
            hocAmount: 0,
            hocAmountMMK: 0,
            hocAmountUSD: 0,
            hocCreditCount: 0,
            hocCreditAmount: 0,
            hocCreditAmountMMK: 0,
            hocCreditAmountUSD: 0,
            hocDebitCount: 0,
            hocDebitAmount: 0,
            hocDebitAmountMMK: 0,
            hocDebitAmountUSD: 0,
            ibdCount: 0,
            ibdAmount: 0,
            ibdAmountMMK: 0,
            ibdAmountUSD: 0,
            ibdCreditCount: 0,
            ibdCreditAmount: 0,
            ibdCreditAmountMMK: 0,
            ibdCreditAmountUSD: 0,
            ibdDebitCount: 0,
            ibdDebitAmount: 0,
            ibdDebitAmountMMK: 0,
            ibdDebitAmountUSD: 0,
            wuCount: 0,
            wuAmount: 0,
            wuAmountMMK: 0,
            wuAmountUSD: 0,
            wuCreditCount: 0,
            wuCreditAmount: 0,
            wuCreditAmountMMK: 0,
            wuCreditAmountUSD: 0,
            wuDebitCount: 0,
            wuDebitAmount: 0,
            wuDebitAmountMMK: 0,
            wuDebitAmountUSD: 0,
            // HOC currency-specific credit amounts
            hocCreditAmountCNY: 0,
            hocCreditAmountEUR: 0,
            hocCreditAmountINR: 0,
            hocCreditAmountJPY: 0,
            hocCreditAmountSGD: 0,
            hocCreditAmountTHB: 0,
            // HOC currency-specific debit amounts
            hocDebitAmountCNY: 0,
            hocDebitAmountEUR: 0,
            hocDebitAmountINR: 0,
            hocDebitAmountJPY: 0,
            hocDebitAmountSGD: 0,
            hocDebitAmountTHB: 0,
            // IBD currency-specific credit amounts
            ibdCreditAmountCNY: 0,
            ibdCreditAmountEUR: 0,
            ibdCreditAmountINR: 0,
            ibdCreditAmountJPY: 0,
            ibdCreditAmountSGD: 0,
            ibdCreditAmountTHB: 0,
            // IBD currency-specific debit amounts
            ibdDebitAmountCNY: 0,
            ibdDebitAmountEUR: 0,
            ibdDebitAmountINR: 0,
            ibdDebitAmountJPY: 0,
            ibdDebitAmountSGD: 0,
            ibdDebitAmountTHB: 0,
            // WU currency-specific credit amounts
            wuCreditAmountCNY: 0,
            wuCreditAmountEUR: 0,
            wuCreditAmountINR: 0,
            wuCreditAmountJPY: 0,
            wuCreditAmountSGD: 0,
            wuCreditAmountTHB: 0,
            // WU currency-specific debit amounts
            wuDebitAmountCNY: 0,
            wuDebitAmountEUR: 0,
            wuDebitAmountINR: 0,
            wuDebitAmountJPY: 0,
            wuDebitAmountSGD: 0,
            wuDebitAmountTHB: 0,
            hocUniqueSerialCount: 0,
            ibdUniqueSerialCount: 0,
            wuUniqueSerialCount: 0,
            totalUniqueSerialCount: 0,
            currencyCounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            currencyAmounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            currencyCreditAmounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            currencyDebitAmounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            // High-value transaction counts by currency
            highValueTransactionCounts: {
                MMK: 0,
                USD: 0,
                SGD: 0,
                EUR: 0,
                JPY: 0,
                CNY: 0,
                THB: 0,
                INR: 0
            },
            highValueTransactionCount: 0,
            highValueTransactionCountUSD: 0
        };

        // Always reset serial numbers - they will be recalculated from the data
        this.hocSerialNumbers = new Set();
        this.ibdSerialNumbers = new Set();
        this.wuSerialNumbers = new Set();
        this.allSerialNumbers = new Set();

        // Only clear high-value data if not preserving it
        if (!preserveHighValueData) {
            this.dateHighValueCounts.clear();
            this.dateHighValueCountsUSD.clear();
            this.fileHighValueCounts.clear();
            this.fileHighValueCountsUSD.clear();
            this.fileHighValueTransactions.clear();
            this.fileHighValueTransactionsUSD.clear();
            this.breakdownToggleState = { isExpanded: false, isInitialized: false };
            this.logDebug('High-value transaction data (MMK and USD) cleared during metrics reset');
        } else {
            this.logDebug('High-value transaction data (MMK and USD) preserved during metrics reset');
        }
    }





    // Calculate metrics from the data
    calculateMetrics(data, preserveHighValueData = false) {
        // Reset metrics (preserve high-value data when filtering)
        this.resetMetrics(preserveHighValueData);



        // Log the data size being processed
        this.logDebug(`Calculating metrics for ${data.length} transactions`);

        // Process each transaction
        data.forEach(transaction => {
            // Ensure amount is a number with proper validation
            const amount = typeof transaction.TRANSACTION_AMOUNT === 'string' ?
                parseFloat(transaction.TRANSACTION_AMOUNT) || 0 :
                (typeof transaction.TRANSACTION_AMOUNT === 'number' ? transaction.TRANSACTION_AMOUNT : 0);

            // Update total counts
            this.summaryMetrics.totalTransactions++;
            this.summaryMetrics.totalAmount = this.safeAdd(this.summaryMetrics.totalAmount, amount);

            // Check if this is a high-value transaction for any currency
            // Get currency, only default to MMK if truly empty
            let currency = transaction.TRANSACTION_CURRENCY;
            if (!currency || currency.trim() === '') {
                currency = 'MMK';
            } else {
                currency = currency.toUpperCase();
            }
            if (window.CurrencyUtils && window.CurrencyUtils.isHighValueTransaction(amount, currency)) {
                // Update currency-specific high-value counts
                if (this.summaryMetrics.highValueTransactionCounts[currency] !== undefined) {
                    this.summaryMetrics.highValueTransactionCounts[currency]++;
                }

                // Maintain backward compatibility with legacy counters
                if (currency === 'MMK') {
                    this.summaryMetrics.highValueTransactionCount++;
                } else if (currency === 'USD') {
                    this.summaryMetrics.highValueTransactionCountUSD++;
                }
            } else {
                // Fallback for when CurrencyUtils is not available
                if (currency === 'MMK' && amount >= this.HIGH_VALUE_THRESHOLD) {
                    this.summaryMetrics.highValueTransactionCount++;
                    if (this.summaryMetrics.highValueTransactionCounts[currency] !== undefined) {
                        this.summaryMetrics.highValueTransactionCounts[currency]++;
                    }
                } else if (currency === 'USD' && amount >= this.HIGH_VALUE_THRESHOLD_USD) {
                    this.summaryMetrics.highValueTransactionCountUSD++;
                    if (this.summaryMetrics.highValueTransactionCounts[currency] !== undefined) {
                        this.summaryMetrics.highValueTransactionCounts[currency]++;
                    }
                }
            }

            // Determine if this is a credit or debit transaction
            const isCredit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
            const isDebit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';

            // Get the serial number (with error handling)
            const serialNo = transaction.SERIAL_NUMBER || transaction.SERIAL_NO || '';

            // Update report type counts
            if (transaction.REPORTTYPE === 'HOC') {
                this.summaryMetrics.hocCount++;
                this.summaryMetrics.hocAmount = this.safeAdd(this.summaryMetrics.hocAmount, amount);

                // Update currency-specific HOC amounts
                const currency = transaction.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    this.summaryMetrics.hocAmountMMK = this.safeAdd(this.summaryMetrics.hocAmountMMK, amount);
                } else if (currency === 'USD') {
                    this.summaryMetrics.hocAmountUSD = this.safeAdd(this.summaryMetrics.hocAmountUSD, amount);
                } else if (currency === 'CNY') {
                    // CNY is handled in credit/debit sections
                } else if (currency === 'EUR') {
                    // EUR is handled in credit/debit sections
                } else if (currency === 'INR') {
                    // INR is handled in credit/debit sections
                } else if (currency === 'JPY') {
                    // JPY is handled in credit/debit sections
                } else if (currency === 'SGD') {
                    // SGD is handled in credit/debit sections
                } else if (currency === 'THB') {
                    // THB is handled in credit/debit sections
                }

                // Track unique serial numbers for HOC
                if (serialNo) {
                    this.hocSerialNumbers.add(serialNo);
                    this.allSerialNumbers.add(serialNo);
                }

                // Update HOC credit/debit counts
                if (isCredit) {
                    this.summaryMetrics.hocCreditCount++;
                    this.summaryMetrics.hocCreditAmount = this.safeAdd(this.summaryMetrics.hocCreditAmount, amount);

                    // Update currency-specific HOC credit amounts
                    const currency = transaction.TRANSACTION_CURRENCY;
                    if (currency === 'MMK') {
                        this.summaryMetrics.hocCreditAmountMMK = this.safeAdd(this.summaryMetrics.hocCreditAmountMMK, amount);
                    } else if (currency === 'USD') {
                        this.summaryMetrics.hocCreditAmountUSD = this.safeAdd(this.summaryMetrics.hocCreditAmountUSD, amount);
                    } else if (currency === 'CNY') {
                        this.summaryMetrics.hocCreditAmountCNY = this.safeAdd(this.summaryMetrics.hocCreditAmountCNY, amount);
                    } else if (currency === 'EUR') {
                        this.summaryMetrics.hocCreditAmountEUR = this.safeAdd(this.summaryMetrics.hocCreditAmountEUR, amount);
                    } else if (currency === 'INR') {
                        this.summaryMetrics.hocCreditAmountINR = this.safeAdd(this.summaryMetrics.hocCreditAmountINR, amount);
                    } else if (currency === 'JPY') {
                        this.summaryMetrics.hocCreditAmountJPY = this.safeAdd(this.summaryMetrics.hocCreditAmountJPY, amount);
                    } else if (currency === 'SGD') {
                        this.summaryMetrics.hocCreditAmountSGD = this.safeAdd(this.summaryMetrics.hocCreditAmountSGD, amount);
                    } else if (currency === 'THB') {
                        this.summaryMetrics.hocCreditAmountTHB = this.safeAdd(this.summaryMetrics.hocCreditAmountTHB, amount);
                    }
                } else if (isDebit) {
                    this.summaryMetrics.hocDebitCount++;
                    this.summaryMetrics.hocDebitAmount = this.safeAdd(this.summaryMetrics.hocDebitAmount, amount);

                    // Update currency-specific HOC debit amounts
                    const currency = transaction.TRANSACTION_CURRENCY;
                    if (currency === 'MMK') {
                        this.summaryMetrics.hocDebitAmountMMK = this.safeAdd(this.summaryMetrics.hocDebitAmountMMK, amount);
                    } else if (currency === 'USD') {
                        this.summaryMetrics.hocDebitAmountUSD = this.safeAdd(this.summaryMetrics.hocDebitAmountUSD, amount);
                    } else if (currency === 'CNY') {
                        this.summaryMetrics.hocDebitAmountCNY = this.safeAdd(this.summaryMetrics.hocDebitAmountCNY, amount);
                    } else if (currency === 'EUR') {
                        this.summaryMetrics.hocDebitAmountEUR = this.safeAdd(this.summaryMetrics.hocDebitAmountEUR, amount);
                    } else if (currency === 'INR') {
                        this.summaryMetrics.hocDebitAmountINR = this.safeAdd(this.summaryMetrics.hocDebitAmountINR, amount);
                    } else if (currency === 'JPY') {
                        this.summaryMetrics.hocDebitAmountJPY = this.safeAdd(this.summaryMetrics.hocDebitAmountJPY, amount);
                    } else if (currency === 'SGD') {
                        this.summaryMetrics.hocDebitAmountSGD = this.safeAdd(this.summaryMetrics.hocDebitAmountSGD, amount);
                    } else if (currency === 'THB') {
                        this.summaryMetrics.hocDebitAmountTHB = this.safeAdd(this.summaryMetrics.hocDebitAmountTHB, amount);
                    }
                }
            } else if (transaction.REPORTTYPE === 'IBD') {
                this.summaryMetrics.ibdCount++;
                this.summaryMetrics.ibdAmount = this.safeAdd(this.summaryMetrics.ibdAmount, amount);

                // Update currency-specific IBD amounts
                const currency = transaction.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    this.summaryMetrics.ibdAmountMMK = this.safeAdd(this.summaryMetrics.ibdAmountMMK, amount);
                } else if (currency === 'USD') {
                    this.summaryMetrics.ibdAmountUSD = this.safeAdd(this.summaryMetrics.ibdAmountUSD, amount);
                } else if (currency === 'CNY') {
                    // CNY is handled in credit/debit sections
                } else if (currency === 'EUR') {
                    // EUR is handled in credit/debit sections
                } else if (currency === 'INR') {
                    // INR is handled in credit/debit sections
                } else if (currency === 'JPY') {
                    // JPY is handled in credit/debit sections
                } else if (currency === 'SGD') {
                    // SGD is handled in credit/debit sections
                } else if (currency === 'THB') {
                    // THB is handled in credit/debit sections
                }

                // Track unique serial numbers for IBD
                if (serialNo) {
                    this.ibdSerialNumbers.add(serialNo);
                    this.allSerialNumbers.add(serialNo);
                }

                // Update IBD credit/debit counts
                if (isCredit) {
                    this.summaryMetrics.ibdCreditCount++;
                    this.summaryMetrics.ibdCreditAmount = this.safeAdd(this.summaryMetrics.ibdCreditAmount, amount);

                    // Update currency-specific IBD credit amounts
                    const currency = transaction.TRANSACTION_CURRENCY;
                    if (currency === 'MMK') {
                        this.summaryMetrics.ibdCreditAmountMMK = this.safeAdd(this.summaryMetrics.ibdCreditAmountMMK, amount);
                    } else if (currency === 'USD') {
                        this.summaryMetrics.ibdCreditAmountUSD = this.safeAdd(this.summaryMetrics.ibdCreditAmountUSD, amount);
                    } else if (currency === 'CNY') {
                        this.summaryMetrics.ibdCreditAmountCNY = this.safeAdd(this.summaryMetrics.ibdCreditAmountCNY, amount);
                    } else if (currency === 'EUR') {
                        this.summaryMetrics.ibdCreditAmountEUR = this.safeAdd(this.summaryMetrics.ibdCreditAmountEUR, amount);
                    } else if (currency === 'INR') {
                        this.summaryMetrics.ibdCreditAmountINR = this.safeAdd(this.summaryMetrics.ibdCreditAmountINR, amount);
                    } else if (currency === 'JPY') {
                        this.summaryMetrics.ibdCreditAmountJPY = this.safeAdd(this.summaryMetrics.ibdCreditAmountJPY, amount);
                    } else if (currency === 'SGD') {
                        this.summaryMetrics.ibdCreditAmountSGD = this.safeAdd(this.summaryMetrics.ibdCreditAmountSGD, amount);
                    } else if (currency === 'THB') {
                        this.summaryMetrics.ibdCreditAmountTHB = this.safeAdd(this.summaryMetrics.ibdCreditAmountTHB, amount);
                    }
                } else if (isDebit) {
                    this.summaryMetrics.ibdDebitCount++;
                    this.summaryMetrics.ibdDebitAmount = this.safeAdd(this.summaryMetrics.ibdDebitAmount, amount);

                    // Update currency-specific IBD debit amounts
                    const currency = transaction.TRANSACTION_CURRENCY;
                    if (currency === 'MMK') {
                        this.summaryMetrics.ibdDebitAmountMMK = this.safeAdd(this.summaryMetrics.ibdDebitAmountMMK, amount);
                    } else if (currency === 'USD') {
                        this.summaryMetrics.ibdDebitAmountUSD = this.safeAdd(this.summaryMetrics.ibdDebitAmountUSD, amount);
                    } else if (currency === 'CNY') {
                        this.summaryMetrics.ibdDebitAmountCNY = this.safeAdd(this.summaryMetrics.ibdDebitAmountCNY, amount);
                    } else if (currency === 'EUR') {
                        this.summaryMetrics.ibdDebitAmountEUR = this.safeAdd(this.summaryMetrics.ibdDebitAmountEUR, amount);
                    } else if (currency === 'INR') {
                        this.summaryMetrics.ibdDebitAmountINR = this.safeAdd(this.summaryMetrics.ibdDebitAmountINR, amount);
                    } else if (currency === 'JPY') {
                        this.summaryMetrics.ibdDebitAmountJPY = this.safeAdd(this.summaryMetrics.ibdDebitAmountJPY, amount);
                    } else if (currency === 'SGD') {
                        this.summaryMetrics.ibdDebitAmountSGD = this.safeAdd(this.summaryMetrics.ibdDebitAmountSGD, amount);
                    } else if (currency === 'THB') {
                        this.summaryMetrics.ibdDebitAmountTHB = this.safeAdd(this.summaryMetrics.ibdDebitAmountTHB, amount);
                    }
                }
            } else if (transaction.REPORTTYPE === 'WU') {
                // WU (Western Union) transactions - separate metrics (not included in IBD)
                this.summaryMetrics.wuCount = this.safeAdd(this.summaryMetrics.wuCount || 0, 1);
                this.summaryMetrics.wuAmount = this.safeAdd(this.summaryMetrics.wuAmount || 0, amount);

                // Update currency-specific WU amounts
                const currency = transaction.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    this.summaryMetrics.wuAmountMMK = this.safeAdd(this.summaryMetrics.wuAmountMMK || 0, amount);
                } else if (currency === 'USD') {
                    this.summaryMetrics.wuAmountUSD = this.safeAdd(this.summaryMetrics.wuAmountUSD || 0, amount);
                } else if (currency === 'CNY') {
                    // CNY is handled in credit/debit sections
                } else if (currency === 'EUR') {
                    // EUR is handled in credit/debit sections
                } else if (currency === 'INR') {
                    // INR is handled in credit/debit sections
                } else if (currency === 'JPY') {
                    // JPY is handled in credit/debit sections
                } else if (currency === 'SGD') {
                    // SGD is handled in credit/debit sections
                } else if (currency === 'THB') {
                    // THB is handled in credit/debit sections
                }

                // Track unique serial numbers for WU
                if (serialNo) {
                    this.wuSerialNumbers.add(serialNo);
                    this.allSerialNumbers.add(serialNo);
                }

                // Update WU credit/debit counts
                if (isCredit) {
                    this.summaryMetrics.wuCreditCount = this.safeAdd(this.summaryMetrics.wuCreditCount || 0, 1);
                    this.summaryMetrics.wuCreditAmount = this.safeAdd(this.summaryMetrics.wuCreditAmount || 0, amount);

                    // Update currency-specific WU credit amounts
                    const currency = transaction.TRANSACTION_CURRENCY;
                    if (currency === 'MMK') {
                        this.summaryMetrics.wuCreditAmountMMK = this.safeAdd(this.summaryMetrics.wuCreditAmountMMK || 0, amount);
                    } else if (currency === 'USD') {
                        this.summaryMetrics.wuCreditAmountUSD = this.safeAdd(this.summaryMetrics.wuCreditAmountUSD || 0, amount);
                    } else if (currency === 'CNY') {
                        this.summaryMetrics.wuCreditAmountCNY = this.safeAdd(this.summaryMetrics.wuCreditAmountCNY || 0, amount);
                    } else if (currency === 'EUR') {
                        this.summaryMetrics.wuCreditAmountEUR = this.safeAdd(this.summaryMetrics.wuCreditAmountEUR || 0, amount);
                    } else if (currency === 'INR') {
                        this.summaryMetrics.wuCreditAmountINR = this.safeAdd(this.summaryMetrics.wuCreditAmountINR || 0, amount);
                    } else if (currency === 'JPY') {
                        this.summaryMetrics.wuCreditAmountJPY = this.safeAdd(this.summaryMetrics.wuCreditAmountJPY || 0, amount);
                    } else if (currency === 'SGD') {
                        this.summaryMetrics.wuCreditAmountSGD = this.safeAdd(this.summaryMetrics.wuCreditAmountSGD || 0, amount);
                    } else if (currency === 'THB') {
                        this.summaryMetrics.wuCreditAmountTHB = this.safeAdd(this.summaryMetrics.wuCreditAmountTHB || 0, amount);
                    }
                } else if (isDebit) {
                    this.summaryMetrics.wuDebitCount = this.safeAdd(this.summaryMetrics.wuDebitCount || 0, 1);
                    this.summaryMetrics.wuDebitAmount = this.safeAdd(this.summaryMetrics.wuDebitAmount || 0, amount);

                    // Update currency-specific WU debit amounts
                    const currency = transaction.TRANSACTION_CURRENCY;
                    if (currency === 'MMK') {
                        this.summaryMetrics.wuDebitAmountMMK = this.safeAdd(this.summaryMetrics.wuDebitAmountMMK || 0, amount);
                    } else if (currency === 'USD') {
                        this.summaryMetrics.wuDebitAmountUSD = this.safeAdd(this.summaryMetrics.wuDebitAmountUSD || 0, amount);
                    } else if (currency === 'CNY') {
                        this.summaryMetrics.wuDebitAmountCNY = this.safeAdd(this.summaryMetrics.wuDebitAmountCNY || 0, amount);
                    } else if (currency === 'EUR') {
                        this.summaryMetrics.wuDebitAmountEUR = this.safeAdd(this.summaryMetrics.wuDebitAmountEUR || 0, amount);
                    } else if (currency === 'INR') {
                        this.summaryMetrics.wuDebitAmountINR = this.safeAdd(this.summaryMetrics.wuDebitAmountINR || 0, amount);
                    } else if (currency === 'JPY') {
                        this.summaryMetrics.wuDebitAmountJPY = this.safeAdd(this.summaryMetrics.wuDebitAmountJPY || 0, amount);
                    } else if (currency === 'SGD') {
                        this.summaryMetrics.wuDebitAmountSGD = this.safeAdd(this.summaryMetrics.wuDebitAmountSGD || 0, amount);
                    } else if (currency === 'THB') {
                        this.summaryMetrics.wuDebitAmountTHB = this.safeAdd(this.summaryMetrics.wuDebitAmountTHB || 0, amount);
                    }
                }
            }

            // Update currency counts for all supported currencies
            // Note: currency variable already declared above for high-value transaction detection
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

            if (supportedCurrencies.includes(currency)) {
                this.summaryMetrics.currencyCounts[currency]++;

                // Track credit/debit amounts by currency
                if (isCredit) {
                    this.summaryMetrics.currencyCreditAmounts[currency] = this.safeAdd(this.summaryMetrics.currencyCreditAmounts[currency], amount);
                    // For net amount calculation: credit adds to the total
                    this.summaryMetrics.currencyAmounts[currency] = this.safeAdd(this.summaryMetrics.currencyAmounts[currency], amount);
                } else if (isDebit) {
                    this.summaryMetrics.currencyDebitAmounts[currency] = this.safeAdd(this.summaryMetrics.currencyDebitAmounts[currency], amount);
                    // For net amount calculation: debit subtracts from the total
                    this.summaryMetrics.currencyAmounts[currency] = this.safeAdd(this.summaryMetrics.currencyAmounts[currency], -amount);
                }
            } else {
                // Default to MMK for unsupported currencies
                this.summaryMetrics.currencyCounts.MMK++;

                // Track credit/debit amounts by currency for unsupported currencies (default to MMK)
                if (isCredit) {
                    this.summaryMetrics.currencyCreditAmounts.MMK = this.safeAdd(this.summaryMetrics.currencyCreditAmounts.MMK, amount);
                } else if (isDebit) {
                    this.summaryMetrics.currencyDebitAmounts.MMK = this.safeAdd(this.summaryMetrics.currencyDebitAmounts.MMK, amount);
                }
            }
        });

        // Update unique serial counts
        this.summaryMetrics.hocUniqueSerialCount = this.hocSerialNumbers ? this.hocSerialNumbers.size : 0;
        this.summaryMetrics.ibdUniqueSerialCount = this.ibdSerialNumbers ? this.ibdSerialNumbers.size : 0;
        this.summaryMetrics.wuUniqueSerialCount = this.wuSerialNumbers ? this.wuSerialNumbers.size : 0;
        this.summaryMetrics.totalUniqueSerialCount = this.allSerialNumbers ? this.allSerialNumbers.size : 0;

        // Calculate total currency amounts from report-type specific amounts to prevent double counting
        // This ensures consistency with worker calculations and export logic
        this.summaryMetrics.currencyAmounts.MMK = this.safeAdd(
            this.safeAdd(this.summaryMetrics.hocAmountMMK || 0, this.summaryMetrics.ibdAmountMMK || 0),
            this.summaryMetrics.wuAmountMMK || 0
        );
        this.summaryMetrics.currencyAmounts.USD = this.safeAdd(
            this.safeAdd(this.summaryMetrics.hocAmountUSD || 0, this.summaryMetrics.ibdAmountUSD || 0),
            this.summaryMetrics.wuAmountUSD || 0
        );

        // For other currencies (SGD, EUR, JPY, CNY, THB, INR), calculate from credit/debit amounts
        // since they don't have report-type specific tracking
        const otherCurrencies = ['SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        otherCurrencies.forEach(currency => {
            this.summaryMetrics.currencyAmounts[currency] = this.safeAdd(
                this.summaryMetrics.currencyCreditAmounts[currency] || 0,
                this.summaryMetrics.currencyDebitAmounts[currency] || 0
            );
        });

        // Log the calculated metrics
        this.logInfo(`Metrics calculation complete: ${this.summaryMetrics.totalTransactions} total transactions`);
        this.logDebug(`HOC: ${this.summaryMetrics.hocCount} (${this.summaryMetrics.hocUniqueSerialCount} unique serials), IBD: ${this.summaryMetrics.ibdCount} (${this.summaryMetrics.ibdUniqueSerialCount} unique serials), WU: ${this.summaryMetrics.wuCount || 0} (${this.summaryMetrics.wuUniqueSerialCount} unique serials)`);
        this.logDebug(`Total unique serial count across all types: ${this.summaryMetrics.totalUniqueSerialCount}`);
        this.logDebug(`Currency amounts - MMK: ${this.formatCurrencySpecific(this.summaryMetrics.currencyAmounts.MMK, 'MMK')}, USD: ${this.formatCurrencySpecific(this.summaryMetrics.currencyAmounts.USD, 'USD')}`);
    }



    // Update the UI with calculated metrics
    updateUI() {
        // Update summary metrics
        document.getElementById('totalTransactions').textContent = this.formatNumber(this.summaryMetrics.totalTransactions);
        document.getElementById('totalAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.currencyAmounts.MMK, 'MMK');
        document.getElementById('totalAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.currencyAmounts.USD, 'USD');

        // Update HOC transaction breakdown
        document.getElementById('hocCount').textContent = this.formatNumber(this.summaryMetrics.hocCount);
        document.getElementById('hocUniqueSerialCount').textContent = this.formatNumber(this.summaryMetrics.hocUniqueSerialCount);
        document.getElementById('hocAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocAmountMMK, 'MMK');
        document.getElementById('hocAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocAmountUSD, 'USD');

        // Update HOC credit/debit breakdown
        document.getElementById('hocCreditCount').textContent = this.formatNumber(this.summaryMetrics.hocCreditCount);
        document.getElementById('hocCreditAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocCreditAmountMMK, 'MMK');
        document.getElementById('hocCreditAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocCreditAmountUSD, 'USD');
        document.getElementById('hocCreditAmountCNY').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocCreditAmountCNY, 'CNY');
        document.getElementById('hocCreditAmountEUR').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocCreditAmountEUR, 'EUR');
        document.getElementById('hocCreditAmountINR').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocCreditAmountINR, 'INR');
        document.getElementById('hocCreditAmountJPY').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocCreditAmountJPY, 'JPY');
        document.getElementById('hocCreditAmountSGD').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocCreditAmountSGD, 'SGD');
        document.getElementById('hocCreditAmountTHB').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocCreditAmountTHB, 'THB');
        document.getElementById('hocDebitCount').textContent = this.formatNumber(this.summaryMetrics.hocDebitCount);
        document.getElementById('hocDebitAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocDebitAmountMMK, 'MMK');
        document.getElementById('hocDebitAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocDebitAmountUSD, 'USD');
        document.getElementById('hocDebitAmountCNY').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocDebitAmountCNY, 'CNY');
        document.getElementById('hocDebitAmountEUR').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocDebitAmountEUR, 'EUR');
        document.getElementById('hocDebitAmountINR').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocDebitAmountINR, 'INR');
        document.getElementById('hocDebitAmountJPY').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocDebitAmountJPY, 'JPY');
        document.getElementById('hocDebitAmountSGD').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocDebitAmountSGD, 'SGD');
        document.getElementById('hocDebitAmountTHB').textContent = this.formatCurrencySpecific(this.summaryMetrics.hocDebitAmountTHB, 'THB');

        // Update IBD transaction breakdown
        document.getElementById('ibdCount').textContent = this.formatNumber(this.summaryMetrics.ibdCount);
        document.getElementById('ibdUniqueSerialCount').textContent = this.formatNumber(this.summaryMetrics.ibdUniqueSerialCount);
        document.getElementById('ibdAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdAmountMMK, 'MMK');
        document.getElementById('ibdAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdAmountUSD, 'USD');

        // Update IBD credit/debit breakdown
        document.getElementById('ibdCreditCount').textContent = this.formatNumber(this.summaryMetrics.ibdCreditCount);
        document.getElementById('ibdCreditAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdCreditAmountMMK, 'MMK');
        document.getElementById('ibdCreditAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdCreditAmountUSD, 'USD');
        document.getElementById('ibdCreditAmountCNY').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdCreditAmountCNY, 'CNY');
        document.getElementById('ibdCreditAmountEUR').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdCreditAmountEUR, 'EUR');
        document.getElementById('ibdCreditAmountINR').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdCreditAmountINR, 'INR');
        document.getElementById('ibdCreditAmountJPY').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdCreditAmountJPY, 'JPY');
        document.getElementById('ibdCreditAmountSGD').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdCreditAmountSGD, 'SGD');
        document.getElementById('ibdCreditAmountTHB').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdCreditAmountTHB, 'THB');
        document.getElementById('ibdDebitCount').textContent = this.formatNumber(this.summaryMetrics.ibdDebitCount);
        document.getElementById('ibdDebitAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdDebitAmountMMK, 'MMK');
        document.getElementById('ibdDebitAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdDebitAmountUSD, 'USD');
        document.getElementById('ibdDebitAmountCNY').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdDebitAmountCNY, 'CNY');
        document.getElementById('ibdDebitAmountEUR').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdDebitAmountEUR, 'EUR');
        document.getElementById('ibdDebitAmountINR').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdDebitAmountINR, 'INR');
        document.getElementById('ibdDebitAmountJPY').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdDebitAmountJPY, 'JPY');
        document.getElementById('ibdDebitAmountSGD').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdDebitAmountSGD, 'SGD');
        document.getElementById('ibdDebitAmountTHB').textContent = this.formatCurrencySpecific(this.summaryMetrics.ibdDebitAmountTHB, 'THB');

        // Update WU transaction breakdown
        document.getElementById('wuCount').textContent = this.formatNumber(this.summaryMetrics.wuCount || 0);
        document.getElementById('wuUniqueSerialCount').textContent = this.formatNumber(this.summaryMetrics.wuUniqueSerialCount || 0);
        document.getElementById('wuAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuAmountMMK || 0, 'MMK');
        document.getElementById('wuAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuAmountUSD || 0, 'USD');

        // Update WU credit/debit breakdown
        document.getElementById('wuCreditCount').textContent = this.formatNumber(this.summaryMetrics.wuCreditCount || 0);
        document.getElementById('wuCreditAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuCreditAmountMMK || 0, 'MMK');
        document.getElementById('wuCreditAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuCreditAmountUSD || 0, 'USD');
        document.getElementById('wuCreditAmountCNY').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuCreditAmountCNY || 0, 'CNY');
        document.getElementById('wuCreditAmountEUR').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuCreditAmountEUR || 0, 'EUR');
        document.getElementById('wuCreditAmountINR').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuCreditAmountINR || 0, 'INR');
        document.getElementById('wuCreditAmountJPY').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuCreditAmountJPY || 0, 'JPY');
        document.getElementById('wuCreditAmountSGD').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuCreditAmountSGD || 0, 'SGD');
        document.getElementById('wuCreditAmountTHB').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuCreditAmountTHB || 0, 'THB');
        document.getElementById('wuDebitCount').textContent = this.formatNumber(this.summaryMetrics.wuDebitCount || 0);
        document.getElementById('wuDebitAmountMMK').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuDebitAmountMMK || 0, 'MMK');
        document.getElementById('wuDebitAmountUSD').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuDebitAmountUSD || 0, 'USD');
        document.getElementById('wuDebitAmountCNY').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuDebitAmountCNY || 0, 'CNY');
        document.getElementById('wuDebitAmountEUR').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuDebitAmountEUR || 0, 'EUR');
        document.getElementById('wuDebitAmountINR').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuDebitAmountINR || 0, 'INR');
        document.getElementById('wuDebitAmountJPY').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuDebitAmountJPY || 0, 'JPY');
        document.getElementById('wuDebitAmountSGD').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuDebitAmountSGD || 0, 'SGD');
        document.getElementById('wuDebitAmountTHB').textContent = this.formatCurrencySpecific(this.summaryMetrics.wuDebitAmountTHB || 0, 'THB');

        // Update files processed count
        document.getElementById('filesProcessed').textContent = window.fileHandler ? window.fileHandler.files.length : 0;

        // Update high-value transaction count (MMK)
        const highValueElement = document.getElementById('highValueTransactionCount');
        if (highValueElement) {
            // Use the calculated high-value count from metrics (which reflects filtered data)
            // Only fall back to file-based calculation if metrics count is 0 and we have file data
            let displayCount = this.summaryMetrics.highValueTransactionCount;

            if (displayCount === 0 && this.fileHighValueCounts.size > 0) {
                // Calculate total high-value transactions from all active files as fallback
                displayCount = Array.from(this.fileHighValueCounts.values())
                    .reduce((total, fileData) => total + fileData.totalCount, 0);
                this.summaryMetrics.highValueTransactionCount = displayCount;
            }

            highValueElement.textContent = this.formatNumber(displayCount);

            // Always update the breakdown display to reflect current state
            this.updateHighValueFileBreakdownCombined();

            // Check for significant changes and show notification
            this.checkHighValueTransactionChanges();
        }

        // Update high-value transaction count (USD)
        const highValueElementUSD = document.getElementById('highValueTransactionCountUSD');
        if (highValueElementUSD) {
            // Use the calculated high-value count from metrics (which reflects filtered data)
            // Only fall back to file-based calculation if metrics count is 0 and we have file data
            let displayCountUSD = this.summaryMetrics.highValueTransactionCountUSD;

            if (displayCountUSD === 0 && this.fileHighValueCountsUSD.size > 0) {
                // Calculate total high-value transactions from all active files as fallback
                displayCountUSD = Array.from(this.fileHighValueCountsUSD.values())
                    .reduce((total, fileData) => total + fileData.totalCount, 0);
                this.summaryMetrics.highValueTransactionCountUSD = displayCountUSD;
            }

            highValueElementUSD.textContent = this.formatNumber(displayCountUSD);

            // Always update the breakdown display to reflect current state
            this.updateHighValueFileBreakdownCombined();

            // Check for significant changes and show notification
            this.checkHighValueTransactionChangesUSD();
        }

        // Update high-value file metrics
        this.updateHighValueFileMetrics();

        // Update high-value file counters in UI
        const filesProcessedElement = document.getElementById('highValueFilesProcessed');
        if (filesProcessedElement) {
            filesProcessedElement.textContent = this.formatNumber(this.highValueFileMetrics.filesProcessed);
        }

        const filesRemovedElement = document.getElementById('highValueFilesRemoved');
        if (filesRemovedElement) {
            filesRemovedElement.textContent = this.formatNumber(this.highValueFileMetrics.filesRemoved);
        }

        const filesActiveElement = document.getElementById('highValueFilesActive');
        if (filesActiveElement) {
            filesActiveElement.textContent = this.formatNumber(this.highValueFileMetrics.activeFiles);
        }

        // Apply enhanced formatting to breakdown elements
        this.enhanceBreakdownDisplay();

        // Update currency breakdown table
        this.updateCurrencyBreakdown();

        // Update TTR Summary Report table
        this.updateTTRSummaryReport();

        // Customer analytics will be updated via fileProcessed events
        // This ensures proper source file tracking and avoids duplicate processing
        this.logDebug('Customer analytics will be updated via fileProcessed events');

        // Run automatic validation and fixes
        setTimeout(() => {
            // Auto-fix WU transaction processing if needed
            this.autoFixWUProcessingIfNeeded();

            // Run a silent validation check to fix any calculation discrepancies
            const issuesFixed = this.validateAndFixCurrencyCalculations(true);
            if (issuesFixed > 0) {
                this.logInfo(`Auto-fixed ${issuesFixed} currency calculation discrepancies`);
            }
        }, 100);
    }

    // Enhanced breakdown display with better formatting and visual indicators
    enhanceBreakdownDisplay() {
        // Add enhanced formatting to all breakdown elements
        const breakdownElements = [
            'hocCount', 'hocUniqueSerialCount', 'hocAmountMMK', 'hocAmountUSD',
            'hocCreditCount', 'hocCreditAmountMMK', 'hocCreditAmountUSD',
            'hocDebitCount', 'hocDebitAmountMMK', 'hocDebitAmountUSD',
            'ibdCount', 'ibdUniqueSerialCount', 'ibdAmountMMK', 'ibdAmountUSD',
            'ibdCreditCount', 'ibdCreditAmountMMK', 'ibdCreditAmountUSD',
            'ibdDebitCount', 'ibdDebitAmountMMK', 'ibdDebitAmountUSD',
            'wuCount', 'wuUniqueSerialCount', 'wuAmountMMK', 'wuAmountUSD',
            'wuCreditCount', 'wuCreditAmountMMK', 'wuCreditAmountUSD',
            'wuDebitCount', 'wuDebitAmountMMK', 'wuDebitAmountUSD'
        ];

        breakdownElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                // Add animation class for updated values
                element.classList.add('animate-count');

                // Add data attributes for enhanced styling
                if (elementId.includes('Amount')) {
                    const currency = elementId.includes('MMK') ? 'MMK' : 'USD';
                    element.setAttribute('data-currency', currency);

                    // Check if it's a large number (>= 1 billion for MMK, >= 1 million for USD)
                    const value = this.parseNumberFromText(element.textContent);
                    const isLarge = currency === 'MMK' ? value >= 1000000000 : value >= 1000000;
                    if (isLarge) {
                        element.setAttribute('data-large-number', 'true');
                    }
                }

                // Ensure proper text wrapping for long numbers
                this.optimizeNumberDisplay(element);

                // Remove animation class after animation completes
                setTimeout(() => {
                    element.classList.remove('animate-count');
                }, 600);
            }
        });
    }

    // Optimize number display to prevent overflow
    optimizeNumberDisplay(element) {
        if (!element) return;

        const text = element.textContent;
        const container = element.closest('.breakdown-details p, .credit-breakdown p, .debit-breakdown p');

        if (container && text) {
            // Check if text is overflowing
            const containerWidth = container.offsetWidth;
            const textWidth = element.scrollWidth;

            if (textWidth > containerWidth * 0.6) { // If number takes more than 60% of container
                // Add responsive class for better handling
                element.classList.add('long-number');

                // For very long numbers, consider abbreviation on small screens
                if (window.innerWidth <= 768 && text.length > 15) {
                    this.abbreviateNumber(element, text);
                }
            }
        }
    }

    // Abbreviate very long numbers on small screens
    abbreviateNumber(element, originalText) {
        const value = this.parseNumberFromText(originalText);
        if (value >= 1000000000) {
            const abbreviated = (value / 1000000000).toFixed(1) + 'B';
            element.setAttribute('title', originalText); // Show full number on hover
            element.setAttribute('data-abbreviated', 'true');
        } else if (value >= 1000000) {
            const abbreviated = (value / 1000000).toFixed(1) + 'M';
            element.setAttribute('title', originalText);
            element.setAttribute('data-abbreviated', 'true');
        }
    }

    // Parse number from formatted text (removes commas and currency symbols)
    parseNumberFromText(text) {
        if (!text) return 0;
        // Remove commas, currency symbols, and spaces, then parse
        const cleanText = text.replace(/[,\s]/g, '').replace(/[A-Z]+/g, '');
        return parseFloat(cleanText) || 0;
    }

    // Force recalculation of currency metrics from raw data
    recalculateCurrencyMetrics() {
        if (!this.rawData || this.rawData.length === 0) {
            this.logWarn('No raw data available for currency metrics recalculation');
            return;
        }

        this.logInfo('Recalculating currency metrics from raw data...');

        // Reset currency metrics
        const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        supportedCurrencies.forEach(currency => {
            this.summaryMetrics.currencyCounts[currency] = 0;
            this.summaryMetrics.currencyAmounts[currency] = 0;
            this.summaryMetrics.currencyCreditAmounts[currency] = 0;
            this.summaryMetrics.currencyDebitAmounts[currency] = 0;
        });

        // Recalculate from raw data
        this.rawData.forEach(transaction => {
            // Get currency, only default to MMK if truly empty
            let currency = transaction.TRANSACTION_CURRENCY;
            if (!currency || currency.trim() === '') {
                currency = 'MMK';
            } else {
                currency = currency.toUpperCase();
            }

            const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
            const isCredit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
            const isDebit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';

            // Only process supported currencies
            if (supportedCurrencies.includes(currency)) {
                this.summaryMetrics.currencyCounts[currency]++;

                if (isCredit) {
                    this.summaryMetrics.currencyCreditAmounts[currency] += amount;
                } else if (isDebit) {
                    this.summaryMetrics.currencyDebitAmounts[currency] += amount;
                }

                // Debug log for specific currencies
                if (currency === 'CNY') {
                    this.logDebug(`CNY transaction processed: ${amount} (${isCredit ? 'Credit' : isDebit ? 'Debit' : 'Other'})`);
                } else if (currency === 'JPY') {
                    this.logDebug(`JPY transaction processed: ${amount} (${isCredit ? 'Credit' : isDebit ? 'Debit' : 'Other'})`);
                }
            } else {
                this.logWarn(`Unsupported currency found during recalculation: ${currency}, skipping transaction`);
            }
        });

        // Calculate total currency amounts using the same logic as main calculation
        // For MMK and USD, use report-type specific amounts if available

        this.summaryMetrics.currencyAmounts.MMK = this.safeAdd(
            this.safeAdd(this.summaryMetrics.hocAmountMMK || 0, this.summaryMetrics.ibdAmountMMK || 0),
            this.summaryMetrics.wuAmountMMK || 0
        );
        this.summaryMetrics.currencyAmounts.USD = this.safeAdd(
            this.safeAdd(this.summaryMetrics.hocAmountUSD || 0, this.summaryMetrics.ibdAmountUSD || 0),
            this.summaryMetrics.wuAmountUSD || 0
        );

        // For other currencies, calculate total amounts (credit + debit)
        const otherCurrencies = ['SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        otherCurrencies.forEach(currency => {
            this.summaryMetrics.currencyAmounts[currency] = this.safeAdd(
                this.summaryMetrics.currencyCreditAmounts[currency] || 0,
                this.summaryMetrics.currencyDebitAmounts[currency] || 0
            );
        });

        this.logInfo('Currency metrics recalculation complete');
        this.logDebug(`Recalculated currency amounts - MMK: ${this.formatCurrencySpecific(this.summaryMetrics.currencyAmounts.MMK, 'MMK')}, USD: ${this.formatCurrencySpecific(this.summaryMetrics.currencyAmounts.USD, 'USD')}`);
    }

    // Validate and fix HOC/IBD/WU currency calculations (manual validation only)
    validateAndFixCurrencyCalculations(silent = false) {
        const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        let issuesFound = 0;
        let issuesFixed = 0;

        if (!silent) {
            this.logDebug('Validating HOC/IBD/WU currency calculations...');
        }

        supportedCurrencies.forEach(currency => {
            // Get current values
            const hocCredit = this.summaryMetrics[`hocCreditAmount${currency}`] || 0;
            const hocDebit = this.summaryMetrics[`hocDebitAmount${currency}`] || 0;
            const ibdCredit = this.summaryMetrics[`ibdCreditAmount${currency}`] || 0;
            const ibdDebit = this.summaryMetrics[`ibdDebitAmount${currency}`] || 0;
            const wuCredit = this.summaryMetrics[`wuCreditAmount${currency}`] || 0;
            const wuDebit = this.summaryMetrics[`wuDebitAmount${currency}`] || 0;

            // Calculate expected totals from raw data
            let expectedHocCredit = 0, expectedHocDebit = 0;
            let expectedIbdCredit = 0, expectedIbdDebit = 0;
            let expectedWuCredit = 0, expectedWuDebit = 0;

            if (this.rawData && this.rawData.length > 0) {
                this.rawData.forEach(transaction => {
                    const transCurrency = transaction.TRANSACTION_CURRENCY || 'MMK';
                    if (transCurrency !== currency) return;

                    const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                    const isCredit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
                    const isDebit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
                    const reportType = transaction.REPORTTYPE;

                    if (reportType === 'HOC') {
                        if (isCredit) expectedHocCredit += amount;
                        else if (isDebit) expectedHocDebit += amount;
                    } else if (reportType === 'IBD') {
                        if (isCredit) expectedIbdCredit += amount;
                        else if (isDebit) expectedIbdDebit += amount;
                    } else if (reportType === 'WU') {
                        if (isCredit) expectedWuCredit += amount;
                        else if (isDebit) expectedWuDebit += amount;
                    }
                });
            }

            // Check for discrepancies and fix them
            // Use a larger tolerance to avoid false positives during normal processing
            const tolerance = Math.max(1.0, Math.max(expectedHocCredit, expectedHocDebit, expectedIbdCredit, expectedIbdDebit, expectedWuCredit, expectedWuDebit) * 0.001);

            // Only report significant discrepancies
            if (Math.abs(hocCredit - expectedHocCredit) > tolerance && (hocCredit > 0 || expectedHocCredit > 0)) {
                if (!silent) {
                    this.logWarn(`HOC ${currency} credit mismatch: stored=${hocCredit.toLocaleString()}, expected=${expectedHocCredit.toLocaleString()}`);
                }
                this.summaryMetrics[`hocCreditAmount${currency}`] = expectedHocCredit;
                issuesFound++;
                issuesFixed++;
            }

            if (Math.abs(hocDebit - expectedHocDebit) > tolerance && (hocDebit > 0 || expectedHocDebit > 0)) {
                if (!silent) {
                    this.logWarn(`HOC ${currency} debit mismatch: stored=${hocDebit.toLocaleString()}, expected=${expectedHocDebit.toLocaleString()}`);
                }
                this.summaryMetrics[`hocDebitAmount${currency}`] = expectedHocDebit;
                issuesFound++;
                issuesFixed++;
            }

            if (Math.abs(ibdCredit - expectedIbdCredit) > tolerance && (ibdCredit > 0 || expectedIbdCredit > 0)) {
                if (!silent) {
                    this.logWarn(`IBD ${currency} credit mismatch: stored=${ibdCredit.toLocaleString()}, expected=${expectedIbdCredit.toLocaleString()}`);
                }
                this.summaryMetrics[`ibdCreditAmount${currency}`] = expectedIbdCredit;
                issuesFound++;
                issuesFixed++;
            }

            if (Math.abs(ibdDebit - expectedIbdDebit) > tolerance && (ibdDebit > 0 || expectedIbdDebit > 0)) {
                if (!silent) {
                    this.logWarn(`IBD ${currency} debit mismatch: stored=${ibdDebit.toLocaleString()}, expected=${expectedIbdDebit.toLocaleString()}`);
                }
                this.summaryMetrics[`ibdDebitAmount${currency}`] = expectedIbdDebit;
                issuesFound++;
                issuesFixed++;
            }

            if (Math.abs(wuCredit - expectedWuCredit) > tolerance && (wuCredit > 0 || expectedWuCredit > 0)) {
                if (!silent) {
                    this.logWarn(`WU ${currency} credit mismatch: stored=${wuCredit.toLocaleString()}, expected=${expectedWuCredit.toLocaleString()}`);
                }
                this.summaryMetrics[`wuCreditAmount${currency}`] = expectedWuCredit;
                issuesFound++;
                issuesFixed++;
            }

            if (Math.abs(wuDebit - expectedWuDebit) > tolerance && (wuDebit > 0 || expectedWuDebit > 0)) {
                if (!silent) {
                    this.logWarn(`WU ${currency} debit mismatch: stored=${wuDebit.toLocaleString()}, expected=${expectedWuDebit.toLocaleString()}`);
                }
                this.summaryMetrics[`wuDebitAmount${currency}`] = expectedWuDebit;
                issuesFound++;
                issuesFixed++;
            }
        });

        if (issuesFound > 0) {
            if (!silent) {
                this.logInfo(`Currency calculation validation complete: ${issuesFound} issues found, ${issuesFixed} fixed`);
            }
            // Update UI after fixing calculations
            this.updateUI();
        } else {
            if (!silent) {
                this.logDebug('Currency calculation validation complete: no issues found');
            }
        }

        return issuesFound;
    }

    // Manual function to fix HOC/IBD/WU currency calculations
    fixCurrencyCalculations() {
        this.logInfo('Manually triggering currency calculation fix...');

        // Reset all currency-specific amounts
        const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

        supportedCurrencies.forEach(currency => {
            this.summaryMetrics[`hocCreditAmount${currency}`] = 0;
            this.summaryMetrics[`hocDebitAmount${currency}`] = 0;
            this.summaryMetrics[`ibdCreditAmount${currency}`] = 0;
            this.summaryMetrics[`ibdDebitAmount${currency}`] = 0;
            this.summaryMetrics[`wuCreditAmount${currency}`] = 0;
            this.summaryMetrics[`wuDebitAmount${currency}`] = 0;
        });

        // Reset credit/debit counts
        this.summaryMetrics.hocCreditCount = 0;
        this.summaryMetrics.hocDebitCount = 0;
        this.summaryMetrics.ibdCreditCount = 0;
        this.summaryMetrics.ibdDebitCount = 0;
        this.summaryMetrics.wuCreditCount = 0;
        this.summaryMetrics.wuDebitCount = 0;

        // Recalculate from raw data
        if (this.rawData && this.rawData.length > 0) {
            this.rawData.forEach(transaction => {
                const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                const currency = transaction.TRANSACTION_CURRENCY || 'MMK';
                const isCredit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
                const isDebit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
                const reportType = transaction.REPORTTYPE;

                if (reportType === 'HOC') {
                    if (isCredit) {
                        this.summaryMetrics.hocCreditCount++;
                        this.summaryMetrics[`hocCreditAmount${currency}`] =
                            (this.summaryMetrics[`hocCreditAmount${currency}`] || 0) + amount;
                    } else if (isDebit) {
                        this.summaryMetrics.hocDebitCount++;
                        this.summaryMetrics[`hocDebitAmount${currency}`] =
                            (this.summaryMetrics[`hocDebitAmount${currency}`] || 0) + amount;
                    }
                } else if (reportType === 'IBD') {
                    if (isCredit) {
                        this.summaryMetrics.ibdCreditCount++;
                        this.summaryMetrics[`ibdCreditAmount${currency}`] =
                            (this.summaryMetrics[`ibdCreditAmount${currency}`] || 0) + amount;
                    } else if (isDebit) {
                        this.summaryMetrics.ibdDebitCount++;
                        this.summaryMetrics[`ibdDebitAmount${currency}`] =
                            (this.summaryMetrics[`ibdDebitAmount${currency}`] || 0) + amount;
                    }
                } else if (reportType === 'WU') {
                    if (isCredit) {
                        this.summaryMetrics.wuCreditCount++;
                        this.summaryMetrics[`wuCreditAmount${currency}`] =
                            (this.summaryMetrics[`wuCreditAmount${currency}`] || 0) + amount;
                    } else if (isDebit) {
                        this.summaryMetrics.wuDebitCount++;
                        this.summaryMetrics[`wuDebitAmount${currency}`] =
                            (this.summaryMetrics[`wuDebitAmount${currency}`] || 0) + amount;
                    }
                }
            });
        }

        this.logInfo('Currency calculation fix complete - updating UI...');
        this.updateUI();

        // Log summary of fixed calculations
        supportedCurrencies.forEach(currency => {
            const hocCredit = this.summaryMetrics[`hocCreditAmount${currency}`] || 0;
            const hocDebit = this.summaryMetrics[`hocDebitAmount${currency}`] || 0;
            const ibdCredit = this.summaryMetrics[`ibdCreditAmount${currency}`] || 0;
            const ibdDebit = this.summaryMetrics[`ibdDebitAmount${currency}`] || 0;
            const wuCredit = this.summaryMetrics[`wuCreditAmount${currency}`] || 0;
            const wuDebit = this.summaryMetrics[`wuDebitAmount${currency}`] || 0;

            if (hocCredit > 0 || hocDebit > 0 || ibdCredit > 0 || ibdDebit > 0 || wuCredit > 0 || wuDebit > 0) {
                this.logInfo(`${currency} - HOC: C=${hocCredit.toLocaleString()}, D=${hocDebit.toLocaleString()} | IBD: C=${ibdCredit.toLocaleString()}, D=${ibdDebit.toLocaleString()} | WU: C=${wuCredit.toLocaleString()}, D=${wuDebit.toLocaleString()}`);
            }
        });
    }

    // Test function to verify currency calculations are working correctly
    testCurrencyCalculations() {
        this.logInfo('Testing currency calculations...');

        if (!this.rawData || this.rawData.length === 0) {
            this.logWarn('No raw data available for testing');
            return false;
        }

        const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        let testsPassed = 0;
        let totalTests = 0;

        supportedCurrencies.forEach(currency => {
            // Calculate expected values from raw data
            let expectedHocCredit = 0, expectedHocDebit = 0;
            let expectedIbdCredit = 0, expectedIbdDebit = 0;
            let expectedWuCredit = 0, expectedWuDebit = 0;

            this.rawData.forEach(transaction => {
                const transCurrency = transaction.TRANSACTION_CURRENCY || 'MMK';
                if (transCurrency !== currency) return;

                const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                const isCredit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
                const isDebit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
                const reportType = transaction.REPORTTYPE;

                if (reportType === 'HOC') {
                    if (isCredit) expectedHocCredit += amount;
                    else if (isDebit) expectedHocDebit += amount;
                } else if (reportType === 'IBD') {
                    if (isCredit) expectedIbdCredit += amount;
                    else if (isDebit) expectedIbdDebit += amount;
                } else if (reportType === 'WU') {
                    if (isCredit) expectedWuCredit += amount;
                    else if (isDebit) expectedWuDebit += amount;
                }
            });

            // Get current values
            const hocCredit = this.summaryMetrics[`hocCreditAmount${currency}`] || 0;
            const hocDebit = this.summaryMetrics[`hocDebitAmount${currency}`] || 0;
            const ibdCredit = this.summaryMetrics[`ibdCreditAmount${currency}`] || 0;
            const ibdDebit = this.summaryMetrics[`ibdDebitAmount${currency}`] || 0;
            const wuCredit = this.summaryMetrics[`wuCreditAmount${currency}`] || 0;
            const wuDebit = this.summaryMetrics[`wuDebitAmount${currency}`] || 0;

            // Test each calculation with appropriate tolerance
            const tolerance = Math.max(1.0, Math.max(expectedHocCredit, expectedHocDebit, expectedIbdCredit, expectedIbdDebit, expectedWuCredit, expectedWuDebit) * 0.001);
            const tests = [
                { name: `HOC ${currency} Credit`, expected: expectedHocCredit, actual: hocCredit },
                { name: `HOC ${currency} Debit`, expected: expectedHocDebit, actual: hocDebit },
                { name: `IBD ${currency} Credit`, expected: expectedIbdCredit, actual: ibdCredit },
                { name: `IBD ${currency} Debit`, expected: expectedIbdDebit, actual: ibdDebit },
                { name: `WU ${currency} Credit`, expected: expectedWuCredit, actual: wuCredit },
                { name: `WU ${currency} Debit`, expected: expectedWuDebit, actual: wuDebit }
            ];

            tests.forEach(test => {
                // Only test currencies that have actual data
                if (test.expected > 0 || test.actual > 0) {
                    totalTests++;
                    if (Math.abs(test.expected - test.actual) <= tolerance) {
                        testsPassed++;
                        if (test.expected > 0) {
                            this.logDebug(`✓ ${test.name}: ${test.actual.toLocaleString()} (expected: ${test.expected.toLocaleString()})`);
                        }
                    } else {
                        this.logWarn(`✗ ${test.name}: ${test.actual.toLocaleString()} (expected: ${test.expected.toLocaleString()})`);
                    }
                }
            });
        });

        const success = testsPassed === totalTests;
        this.logInfo(`Currency calculation test complete: ${testsPassed}/${totalTests} tests passed (${success ? 'SUCCESS' : 'FAILED'})`);
        return success;
    }

    // Fix currency total amounts that may have been calculated incorrectly
    fixCurrencyNetAmounts() {
        this.logInfo('Fixing currency total amounts (credit + debit calculation)...');

        const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        let fixedCount = 0;

        supportedCurrencies.forEach(currency => {
            const creditAmount = this.summaryMetrics.currencyCreditAmounts[currency] || 0;
            const debitAmount = this.summaryMetrics.currencyDebitAmounts[currency] || 0;
            const currentAmount = this.summaryMetrics.currencyAmounts[currency] || 0;
            const correctAmount = creditAmount + debitAmount;

            // Check if the current amount is incorrect
            const tolerance = Math.max(1.0, Math.max(Math.abs(currentAmount), Math.abs(correctAmount)) * 0.001);

            if (Math.abs(currentAmount - correctAmount) > tolerance && (Math.abs(currentAmount) > 1 || Math.abs(correctAmount) > 1)) {
                this.logInfo(`Fixing ${currency}: stored=${currentAmount.toLocaleString()}, correct=${correctAmount.toLocaleString()} (credit=${creditAmount.toLocaleString()}, debit=${debitAmount.toLocaleString()})`);
                this.summaryMetrics.currencyAmounts[currency] = correctAmount;
                fixedCount++;
            }
        });

        if (fixedCount > 0) {
            this.logInfo(`Fixed ${fixedCount} currency total amount calculations`);
            this.updateUI();
        } else {
            this.logInfo('All currency total amounts are correct');
        }

        return fixedCount;
    }

    // Diagnostic function to check WU transaction processing
    diagnoseWUTransactions() {
        this.logInfo('Diagnosing WU transaction processing...');

        if (!this.rawData || this.rawData.length === 0) {
            this.logWarn('No raw data available for WU diagnosis');
            return;
        }

        let wuTransactionCount = 0;
        let wuTransactionsByType = { credit: 0, debit: 0, other: 0 };
        let wuAmountsByType = { credit: 0, debit: 0, other: 0 };
        let wuByCurrency = {};
        let sampleWUTransactions = [];

        this.rawData.forEach((transaction, index) => {
            if (transaction.REPORTTYPE === 'WU') {
                wuTransactionCount++;

                const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                const currency = transaction.TRANSACTION_CURRENCY || 'MMK';
                const isCredit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
                const isDebit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
                const serialNo = transaction.SERIAL_NUMBER || transaction.SERIAL_NO;

                // Track by type
                if (isCredit) {
                    wuTransactionsByType.credit++;
                    wuAmountsByType.credit += amount;
                } else if (isDebit) {
                    wuTransactionsByType.debit++;
                    wuAmountsByType.debit += amount;
                } else {
                    wuTransactionsByType.other++;
                    wuAmountsByType.other += amount;
                }

                // Track by currency
                if (!wuByCurrency[currency]) {
                    wuByCurrency[currency] = { count: 0, amount: 0, credit: 0, debit: 0 };
                }
                wuByCurrency[currency].count++;
                wuByCurrency[currency].amount += amount;
                if (isCredit) wuByCurrency[currency].credit += amount;
                if (isDebit) wuByCurrency[currency].debit += amount;

                // Collect sample transactions (first 5)
                if (sampleWUTransactions.length < 5) {
                    sampleWUTransactions.push({
                        index: index,
                        amount: amount,
                        currency: currency,
                        type: isCredit ? 'Credit' : isDebit ? 'Debit' : 'Other',
                        serialNo: serialNo,
                        role: transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE
                    });
                }
            }
        });

        // Log diagnosis results
        this.logInfo(`WU Transaction Diagnosis Results:`);
        this.logInfo(`  Total WU transactions found in raw data: ${wuTransactionCount}`);
        this.logInfo(`  WU transactions by type: Credit=${wuTransactionsByType.credit}, Debit=${wuTransactionsByType.debit}, Other=${wuTransactionsByType.other}`);
        this.logInfo(`  WU amounts by type: Credit=${wuAmountsByType.credit.toLocaleString()}, Debit=${wuAmountsByType.debit.toLocaleString()}, Other=${wuAmountsByType.other.toLocaleString()}`);

        this.logInfo(`  WU transactions by currency:`);
        Object.keys(wuByCurrency).forEach(currency => {
            const data = wuByCurrency[currency];
            this.logInfo(`    ${currency}: Count=${data.count}, Amount=${data.amount.toLocaleString()}, Credit=${data.credit.toLocaleString()}, Debit=${data.debit.toLocaleString()}`);
        });

        this.logInfo(`  Current WU metrics in summaryMetrics:`);
        this.logInfo(`    wuCount: ${this.summaryMetrics.wuCount || 0}`);
        this.logInfo(`    wuAmount: ${(this.summaryMetrics.wuAmount || 0).toLocaleString()}`);
        this.logInfo(`    wuAmountMMK: ${(this.summaryMetrics.wuAmountMMK || 0).toLocaleString()}`);
        this.logInfo(`    wuAmountUSD: ${(this.summaryMetrics.wuAmountUSD || 0).toLocaleString()}`);
        this.logInfo(`    wuCreditCount: ${this.summaryMetrics.wuCreditCount || 0}`);
        this.logInfo(`    wuDebitCount: ${this.summaryMetrics.wuDebitCount || 0}`);
        this.logInfo(`    wuUniqueSerialCount: ${this.summaryMetrics.wuUniqueSerialCount || 0}`);

        this.logInfo(`  Sample WU transactions:`);
        sampleWUTransactions.forEach((sample, i) => {
            this.logInfo(`    ${i + 1}. Index=${sample.index}, Amount=${sample.amount.toLocaleString()}, Currency=${sample.currency}, Type=${sample.type}, Serial=${sample.serialNo}, Role=${sample.role}`);
        });

        // Check if WU serial numbers are being tracked
        this.logInfo(`  WU serial numbers tracked: ${this.wuSerialNumbers ? this.wuSerialNumbers.size : 0}`);
        if (this.wuSerialNumbers && this.wuSerialNumbers.size > 0) {
            const firstFewSerials = Array.from(this.wuSerialNumbers).slice(0, 3);
            this.logInfo(`    First few WU serials: ${firstFewSerials.join(', ')}`);
        }

        return {
            totalWUInRawData: wuTransactionCount,
            wuInMetrics: this.summaryMetrics.wuCount || 0,
            discrepancy: wuTransactionCount !== (this.summaryMetrics.wuCount || 0),
            wuByCurrency: wuByCurrency,
            sampleTransactions: sampleWUTransactions
        };
    }

    // Fix WU transaction processing by recalculating from raw data
    fixWUTransactionProcessing() {
        this.logInfo('Fixing WU transaction processing...');

        if (!this.rawData || this.rawData.length === 0) {
            this.logWarn('No raw data available for WU transaction fix');
            return false;
        }

        // Reset WU metrics
        this.summaryMetrics.wuCount = 0;
        this.summaryMetrics.wuAmount = 0;
        this.summaryMetrics.wuAmountMMK = 0;
        this.summaryMetrics.wuAmountUSD = 0;
        this.summaryMetrics.wuCreditCount = 0;
        this.summaryMetrics.wuCreditAmount = 0;
        this.summaryMetrics.wuCreditAmountMMK = 0;
        this.summaryMetrics.wuCreditAmountUSD = 0;
        this.summaryMetrics.wuDebitCount = 0;
        this.summaryMetrics.wuDebitAmount = 0;
        this.summaryMetrics.wuDebitAmountMMK = 0;
        this.summaryMetrics.wuDebitAmountUSD = 0;

        // Reset WU currency-specific amounts
        const supportedCurrencies = ['CNY', 'EUR', 'INR', 'JPY', 'SGD', 'THB'];
        supportedCurrencies.forEach(currency => {
            this.summaryMetrics[`wuCreditAmount${currency}`] = 0;
            this.summaryMetrics[`wuDebitAmount${currency}`] = 0;
        });

        // Reset WU serial numbers
        this.wuSerialNumbers = new Set();



        // Process WU transactions from raw data
        let processedWUCount = 0;
        this.rawData.forEach(transaction => {
            if (transaction.REPORTTYPE === 'WU') {
                processedWUCount++;

                const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                const currency = transaction.TRANSACTION_CURRENCY || 'MMK';
                const isCredit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
                const isDebit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
                const serialNo = transaction.SERIAL_NUMBER || transaction.SERIAL_NO;

                // Update WU counts and amounts
                this.summaryMetrics.wuCount = this.safeAdd(this.summaryMetrics.wuCount, 1);
                this.summaryMetrics.wuAmount = this.safeAdd(this.summaryMetrics.wuAmount, amount);

                // Update currency-specific WU amounts (MMK and USD only for main totals)
                if (currency === 'MMK') {
                    this.summaryMetrics.wuAmountMMK = this.safeAdd(this.summaryMetrics.wuAmountMMK, amount);
                } else if (currency === 'USD') {
                    this.summaryMetrics.wuAmountUSD = this.safeAdd(this.summaryMetrics.wuAmountUSD, amount);
                }

                // Track unique serial numbers for WU
                if (serialNo) {
                    this.wuSerialNumbers.add(serialNo);
                    this.allSerialNumbers.add(serialNo);
                }

                // Update WU credit/debit counts and amounts
                if (isCredit) {
                    this.summaryMetrics.wuCreditCount = this.safeAdd(this.summaryMetrics.wuCreditCount, 1);
                    this.summaryMetrics.wuCreditAmount = this.safeAdd(this.summaryMetrics.wuCreditAmount, amount);

                    // Update currency-specific WU credit amounts
                    if (currency === 'MMK') {
                        this.summaryMetrics.wuCreditAmountMMK = this.safeAdd(this.summaryMetrics.wuCreditAmountMMK, amount);
                    } else if (currency === 'USD') {
                        this.summaryMetrics.wuCreditAmountUSD = this.safeAdd(this.summaryMetrics.wuCreditAmountUSD, amount);
                    } else if (currency === 'CNY') {
                        this.summaryMetrics.wuCreditAmountCNY = this.safeAdd(this.summaryMetrics.wuCreditAmountCNY, amount);
                    } else if (currency === 'EUR') {
                        this.summaryMetrics.wuCreditAmountEUR = this.safeAdd(this.summaryMetrics.wuCreditAmountEUR, amount);
                    } else if (currency === 'INR') {
                        this.summaryMetrics.wuCreditAmountINR = this.safeAdd(this.summaryMetrics.wuCreditAmountINR, amount);
                    } else if (currency === 'JPY') {
                        this.summaryMetrics.wuCreditAmountJPY = this.safeAdd(this.summaryMetrics.wuCreditAmountJPY, amount);
                    } else if (currency === 'SGD') {
                        this.summaryMetrics.wuCreditAmountSGD = this.safeAdd(this.summaryMetrics.wuCreditAmountSGD, amount);
                    } else if (currency === 'THB') {
                        this.summaryMetrics.wuCreditAmountTHB = this.safeAdd(this.summaryMetrics.wuCreditAmountTHB, amount);
                    }
                } else if (isDebit) {
                    this.summaryMetrics.wuDebitCount = this.safeAdd(this.summaryMetrics.wuDebitCount, 1);
                    this.summaryMetrics.wuDebitAmount = this.safeAdd(this.summaryMetrics.wuDebitAmount, amount);

                    // Update currency-specific WU debit amounts
                    if (currency === 'MMK') {
                        this.summaryMetrics.wuDebitAmountMMK = this.safeAdd(this.summaryMetrics.wuDebitAmountMMK, amount);
                    } else if (currency === 'USD') {
                        this.summaryMetrics.wuDebitAmountUSD = this.safeAdd(this.summaryMetrics.wuDebitAmountUSD, amount);
                    } else if (currency === 'CNY') {
                        this.summaryMetrics.wuDebitAmountCNY = this.safeAdd(this.summaryMetrics.wuDebitAmountCNY, amount);
                    } else if (currency === 'EUR') {
                        this.summaryMetrics.wuDebitAmountEUR = this.safeAdd(this.summaryMetrics.wuDebitAmountEUR, amount);
                    } else if (currency === 'INR') {
                        this.summaryMetrics.wuDebitAmountINR = this.safeAdd(this.summaryMetrics.wuDebitAmountINR, amount);
                    } else if (currency === 'JPY') {
                        this.summaryMetrics.wuDebitAmountJPY = this.safeAdd(this.summaryMetrics.wuDebitAmountJPY, amount);
                    } else if (currency === 'SGD') {
                        this.summaryMetrics.wuDebitAmountSGD = this.safeAdd(this.summaryMetrics.wuDebitAmountSGD, amount);
                    } else if (currency === 'THB') {
                        this.summaryMetrics.wuDebitAmountTHB = this.safeAdd(this.summaryMetrics.wuDebitAmountTHB, amount);
                    }
                }
            }
        });

        // Update unique serial count
        this.summaryMetrics.wuUniqueSerialCount = this.wuSerialNumbers.size;

        this.logInfo(`WU transaction processing fix complete: processed ${processedWUCount} WU transactions`);
        this.logInfo(`WU metrics after fix: Count=${this.summaryMetrics.wuCount}, Amount=${this.summaryMetrics.wuAmount.toLocaleString()}, UniqueSerials=${this.summaryMetrics.wuUniqueSerialCount}`);
        this.logInfo(`WU by currency: MMK=${this.summaryMetrics.wuAmountMMK.toLocaleString()}, USD=${this.summaryMetrics.wuAmountUSD.toLocaleString()}`);

        // Update the UI
        this.updateUI();

        return processedWUCount > 0;
    }

    // Automatically detect and fix WU processing issues
    autoFixWUProcessingIfNeeded() {
        // Only run if we have raw data
        if (!this.rawData || this.rawData.length === 0) {
            return;
        }

        // Quick check: count WU transactions in raw data
        let wuInRawData = 0;
        for (let i = 0; i < Math.min(this.rawData.length, 1000); i++) { // Check first 1000 for performance
            if (this.rawData[i].REPORTTYPE === 'WU') {
                wuInRawData++;
            }
        }

        // If we found WU transactions in raw data but have 0 in metrics, auto-fix
        const wuInMetrics = this.summaryMetrics.wuCount || 0;
        if (wuInRawData > 0 && wuInMetrics === 0) {
            this.logInfo(`Auto-fixing WU processing: Found ${wuInRawData} WU transactions in raw data but 0 in metrics`);
            const success = this.fixWUTransactionProcessing();
            if (success) {
                this.logInfo('✓ WU transaction processing automatically fixed');
            }
        }
    }

    // Update currency breakdown table
    updateCurrencyBreakdown() {
        const tableBody = document.getElementById('currencyBreakdownTableBody');
        if (!tableBody) {
            this.logWarn('Currency breakdown table body not found');
            return;
        }

        // Get all supported currencies
        const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

        // Check if we need to recalculate metrics (if all currency amounts are 0 but we have raw data)
        const totalCurrencyAmount = supportedCurrencies.reduce((sum, currency) =>
            sum + Math.abs(this.summaryMetrics.currencyAmounts[currency] || 0), 0);

        if (totalCurrencyAmount === 0 && this.rawData && this.rawData.length > 0) {
            this.logInfo('Recalculating currency metrics from raw data...');
            this.recalculateCurrencyMetrics();
        }

        // ENHANCEMENT: Always log current currency state for debugging
        this.logDebug('Currency breakdown update - Current currency amounts:',
            Object.fromEntries(supportedCurrencies.map(c => [c, this.summaryMetrics.currencyAmounts[c] || 0])));
        this.logDebug(`Raw data available: ${this.rawData ? this.rawData.length : 0} transactions`);

        // Debug logging for currency amounts (only when in development mode)
        if (this.isDevelopmentMode) {
            this.logDebug('Currency breakdown update - Current metrics:');
            supportedCurrencies.forEach(currency => {
                const amount = this.summaryMetrics.currencyAmounts[currency] || 0;
                const creditAmount = this.summaryMetrics.currencyCreditAmounts[currency] || 0;
                const debitAmount = this.summaryMetrics.currencyDebitAmounts[currency] || 0;

                // Also check HOC/IBD/WU specific amounts for debugging
                const hocCredit = this.summaryMetrics[`hocCreditAmount${currency}`] || 0;
                const hocDebit = this.summaryMetrics[`hocDebitAmount${currency}`] || 0;
                const ibdCredit = this.summaryMetrics[`ibdCreditAmount${currency}`] || 0;
                const ibdDebit = this.summaryMetrics[`ibdDebitAmount${currency}`] || 0;
                const wuCredit = this.summaryMetrics[`wuCreditAmount${currency}`] || 0;
                const wuDebit = this.summaryMetrics[`wuDebitAmount${currency}`] || 0;

                if (amount > 0 || creditAmount > 0 || debitAmount > 0 || hocCredit > 0 || hocDebit > 0 || ibdCredit > 0 || ibdDebit > 0 || wuCredit > 0 || wuDebit > 0) {
                    this.logDebug(`  ${currency}: Total=${amount.toLocaleString()}, Credit=${creditAmount.toLocaleString()}, Debit=${debitAmount.toLocaleString()}`);
                    this.logDebug(`    HOC: Credit=${hocCredit.toLocaleString()}, Debit=${hocDebit.toLocaleString()}`);
                    this.logDebug(`    IBD: Credit=${ibdCredit.toLocaleString()}, Debit=${ibdDebit.toLocaleString()}`);
                    this.logDebug(`    WU: Credit=${wuCredit.toLocaleString()}, Debit=${wuDebit.toLocaleString()}`);
                }
            });
        }

        // Create currency breakdown data - show all currencies, even with 0 values
        const currencyData = [];
        supportedCurrencies.forEach(currency => {
            const amount = this.summaryMetrics.currencyAmounts[currency] || 0;
            const creditAmount = this.summaryMetrics.currencyCreditAmounts[currency] || 0;
            const debitAmount = this.summaryMetrics.currencyDebitAmounts[currency] || 0;

            // Calculate the total amount (credit + debit) for dashboard display
            const calculatedAmount = creditAmount + debitAmount;

            // Update currency amount to show total transaction volume
            if (calculatedAmount > 0) {
                this.summaryMetrics.currencyAmounts[currency] = calculatedAmount;
            }

            // Always add currency to the breakdown, even if amounts are 0
            currencyData.push({
                currency: currency,
                amount: amount,
                creditAmount: creditAmount,
                debitAmount: debitAmount
            });
        });

        // Sort currencies: first by whether they have data (non-zero amounts), then by amount descending, then alphabetically
        currencyData.sort((a, b) => {
            const aHasData = a.amount > 0 || a.creditAmount > 0 || a.debitAmount > 0;
            const bHasData = b.amount > 0 || b.creditAmount > 0 || b.debitAmount > 0;

            // Currencies with data come first
            if (aHasData !== bHasData) {
                return bHasData ? 1 : -1;
            }

            // Among currencies with data, sort by amount descending
            if (aHasData && bHasData && b.amount !== a.amount) {
                return b.amount - a.amount;
            }

            // For currencies with equal amounts (including 0), sort alphabetically
            return a.currency.localeCompare(b.currency);
        });

        // Clear existing content
        tableBody.innerHTML = '';

        // Check if there's any actual data (non-zero amounts)
        const hasAnyData = currencyData.some(item =>
            item.amount > 0 || item.creditAmount > 0 || item.debitAmount > 0
        );

        if (!hasAnyData) {
            // Show empty message when no data exists
            const emptyRow = document.createElement('tr');
            emptyRow.className = 'empty-table-message';
            emptyRow.innerHTML = '<td colspan="4">No data available. Please upload CSV files to view currency breakdown.</td>';
            tableBody.appendChild(emptyRow);
        } else {
            // Populate table with currency data (including zeros)
            currencyData.forEach(item => {
                const row = document.createElement('tr');
                row.className = 'currency-breakdown-row';
                row.setAttribute('data-currency', item.currency);

                // Add a class to distinguish zero-value rows for styling
                const hasData = item.amount > 0 || item.creditAmount > 0 || item.debitAmount > 0;
                if (!hasData) {
                    row.classList.add('zero-value-currency');
                }

                const currencyCell = document.createElement('td');
                currencyCell.className = 'currency-type';
                currencyCell.textContent = item.currency;

                const creditCell = document.createElement('td');
                creditCell.className = 'currency-credit-amount';
                creditCell.textContent = this.formatCurrencySpecific(item.creditAmount, item.currency);

                const debitCell = document.createElement('td');
                debitCell.className = 'currency-debit-amount';
                debitCell.textContent = this.formatCurrencySpecific(item.debitAmount, item.currency);

                const amountCell = document.createElement('td');
                amountCell.className = 'currency-amount';
                amountCell.textContent = this.formatCurrencySpecific(item.amount, item.currency);

                row.appendChild(currencyCell);
                row.appendChild(creditCell);
                row.appendChild(debitCell);
                row.appendChild(amountCell);
                tableBody.appendChild(row);
            });

            // Add animation to new rows
            const rows = tableBody.querySelectorAll('.currency-breakdown-row');
            rows.forEach((row, index) => {
                setTimeout(() => {
                    row.classList.add('animate-fade-in');
                }, index * 50); // Stagger animation
            });
        }

        this.logDebug(`Currency breakdown updated with ${currencyData.length} currencies`);
    }

    // Update high-value file metrics based on current state
    updateHighValueFileMetrics() {
        // Calculate active files with high-value transactions
        this.highValueFileMetrics.activeFiles = this.fileHighValueCounts.size;

        // Note: filesProcessed and filesRemoved are incremented when files are processed/removed
        // They represent cumulative counts, not current state

        this.logDebug(`High-value file metrics updated: Active: ${this.highValueFileMetrics.activeFiles}, Processed: ${this.highValueFileMetrics.filesProcessed}, Removed: ${this.highValueFileMetrics.filesRemoved}`);
    }

    // Format number with commas
    formatNumber(number) {
        // Ensure number is actually a number
        if (isNaN(number) || number === null || number === undefined) {
            return '0';
        }
        return Number(number).toLocaleString('en-US');
    }

    // Format currency with proper symbol and decimal places
    formatCurrency(amount, currency = 'MMK') {
        // Use the centralized currency utilities if available
        if (window.CurrencyUtils) {
            return window.CurrencyUtils.formatCurrency(amount, currency);
        }

        // Fallback formatting
        if (isNaN(amount) || amount === null || amount === undefined) {
            return `0.00 ${currency}`;
        }
        return Number(amount).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ` ${currency}`;
    }

    // Format currency with specific currency symbol
    formatCurrencySpecific(amount, currency = 'MMK') {
        // Use the centralized currency utilities if available
        if (window.CurrencyUtils) {
            return window.CurrencyUtils.formatCurrency(amount, currency);
        }

        // Fallback formatting
        if (isNaN(amount) || amount === null || amount === undefined) {
            amount = 0;
        }

        // Get currency symbol from configuration
        const currencyConfig = window.FIELD_MAPPINGS?.CURRENCY_CONFIG?.[currency];
        const symbol = currencyConfig?.symbol || currency;

        // For zero amounts, show "0" with proper symbol
        if (amount === 0) {
            return currency === 'CNY' ? `CN¥0` : `0 ${currency}`;
        }

        // Format with proper decimals based on currency
        const decimals = currencyConfig?.decimals ?? 2;
        const formatted = Number(amount).toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });

        // Use proper currency symbol
        if (currency === 'CNY') {
            return `CN¥${formatted}`;
        } else if (currency === 'USD') {
            return `$${formatted}`;
        } else if (currency === 'EUR') {
            return `€${formatted}`;
        } else if (currency === 'JPY') {
            return `¥${formatted}`;
        } else {
            return `${formatted} ${currency}`;
        }
    }

    // Check for significant changes in high-value transaction count and show notifications
    checkHighValueTransactionChanges() {
        const currentCount = this.summaryMetrics.highValueTransactionCount;
        const previousCount = this.previousHighValueCount;

        // Show notifications for any increase in high-value transactions (lowered threshold)
        const difference = currentCount - previousCount;
        const percentageChange = previousCount > 0 ? (difference / previousCount) * 100 : (currentCount > 0 ? 100 : 0);

        // Show alert for any positive change (1 or more transactions) or significant decreases
        if (difference > 0 || (difference < 0 && Math.abs(difference) >= 3)) {
            let message = '';
            let notificationType = 'info';

            if (difference > 0) {
                // Increase in high-value transactions - show for ANY increase
                if (difference === 1) {
                    message = `🚨 High-value transaction alert: ${difference} new transaction exceeding 1B MMK detected (total: ${this.formatNumber(currentCount)})`;
                } else {
                    message = `🚨 High-value transaction alert: ${difference} new transactions exceeding 1B MMK detected (total: ${this.formatNumber(currentCount)})`;
                }
                notificationType = 'warning';
            } else if (difference < 0) {
                // Decrease in high-value transactions (file removal)
                message = `High-value transactions updated: ${Math.abs(difference)} transactions removed (remaining: ${this.formatNumber(currentCount)})`;
                notificationType = 'info';
            }

            // Show notification if we have a change
            if (message && window.app) {
                window.app.showNotification(message, notificationType, 6000); // Increased duration
                console.log(`High-value transaction change: ${previousCount} → ${currentCount} (${difference > 0 ? '+' : ''}${difference})`);
            }
        }

        // Also show initial detection alert when going from 0 to any number
        if (previousCount === 0 && currentCount > 0) {
            const message = `🎯 High-value transactions detected: ${currentCount} transaction${currentCount > 1 ? 's' : ''} exceeding 1B MMK found in uploaded files`;
            if (window.app) {
                window.app.showNotification(message, 'success', 6000);
                console.log(`Initial high-value transaction detection: ${currentCount} transactions found`);
            }
        }

        // Update the previous count for next comparison
        this.previousHighValueCount = currentCount;
    }

    // Check for significant changes in USD high-value transaction count and show notifications
    checkHighValueTransactionChangesUSD() {
        const currentCount = this.summaryMetrics.highValueTransactionCountUSD;
        const previousCount = this.previousHighValueCountUSD;

        // Show notifications for any increase in high-value transactions (lowered threshold)
        const difference = currentCount - previousCount;
        const percentageChange = previousCount > 0 ? (difference / previousCount) * 100 : (currentCount > 0 ? 100 : 0);

        // Show alert for any positive change (1 or more transactions) or significant decreases
        if (difference > 0 || (difference < 0 && Math.abs(difference) >= 3)) {
            let message = '';
            let notificationType = 'info';

            if (difference > 0) {
                // Increase in high-value transactions - show for ANY increase
                if (difference === 1) {
                    message = `💰 High-value USD transaction alert: ${difference} new transaction exceeding $10,000 USD detected (total: ${this.formatNumber(currentCount)})`;
                } else {
                    message = `💰 High-value USD transaction alert: ${difference} new transactions exceeding $10,000 USD detected (total: ${this.formatNumber(currentCount)})`;
                }
                notificationType = 'warning';
            } else if (difference < 0) {
                // Decrease in high-value transactions (file removal)
                message = `High-value USD transactions updated: ${Math.abs(difference)} transactions removed (remaining: ${this.formatNumber(currentCount)})`;
                notificationType = 'info';
            }

            // Show notification if we have a change
            if (message && window.app) {
                window.app.showNotification(message, notificationType, 6000); // Increased duration
                console.log(`High-value USD transaction change: ${previousCount} → ${currentCount} (${difference > 0 ? '+' : ''}${difference})`);
            }
        }

        // Also show initial detection alert when going from 0 to any number
        if (previousCount === 0 && currentCount > 0) {
            const message = `🎯 High-value USD transactions detected: ${currentCount} transaction${currentCount > 1 ? 's' : ''} exceeding $10,000 USD found in uploaded files`;
            if (window.app) {
                window.app.showNotification(message, 'success', 6000);
                console.log(`Initial high-value USD transaction detection: ${currentCount} transactions found`);
            }
        }

        // Update the previous count for next comparison
        this.previousHighValueCountUSD = currentCount;
    }

    // Manual trigger for high-value transaction alerts (for testing/debugging)
    triggerHighValueAlerts() {
        console.log('Manually triggering high-value transaction alerts...');
        console.log(`Current MMK high-value count: ${this.summaryMetrics.highValueTransactionCount}`);
        console.log(`Current USD high-value count: ${this.summaryMetrics.highValueTransactionCountUSD}`);

        // Force check both types of alerts
        this.checkHighValueTransactionChanges();
        this.checkHighValueTransactionChangesUSD();

        // Also show a summary notification
        const totalHighValue = this.summaryMetrics.highValueTransactionCount + this.summaryMetrics.highValueTransactionCountUSD;
        if (totalHighValue > 0 && window.app) {
            window.app.showNotification(
                `📊 High-Value Analytics Summary: ${this.summaryMetrics.highValueTransactionCount} MMK transactions (≥1B) + ${this.summaryMetrics.highValueTransactionCountUSD} USD transactions (≥$10K) = ${totalHighValue} total`,
                'info',
                8000
            );
        } else if (window.app) {
            window.app.showNotification(
                '📊 No high-value transactions detected in current data',
                'info',
                4000
            );
        }
    }

    // Update date-level high-value transaction count
    updateDateHighValueCount(date, count) {
        if (count > 0) {
            this.dateHighValueCounts.set(date, count);
        } else {
            this.dateHighValueCounts.delete(date);
        }
        console.log(`Updated high-value count for ${date}: ${count}`);
    }

    // Remove date from high-value tracking
    removeDateHighValueCount(date) {
        const removed = this.dateHighValueCounts.delete(date);
        if (removed) {
            console.log(`Removed high-value tracking for date: ${date}`);
        }
        return removed;
    }

    // Get high-value count for a specific date
    getDateHighValueCount(date) {
        return this.dateHighValueCounts.get(date) || 0;
    }

    // Get detailed high-value transactions for a specific date
    getDateHighValueTransactions(date) {
        const transactions = [];

        // Iterate through all file high-value transactions
        this.fileHighValueTransactions.forEach((fileTransactions, fileId) => {
            fileTransactions.forEach(transaction => {
                const transactionDate = this.extractAndFormatDate(transaction.TRANSACTION_DATE);
                if (transactionDate === date) {
                    // Ensure amount is a number for sorting
                    const amount = typeof transaction.TRANSACTION_AMOUNT === 'string' ?
                        parseFloat(transaction.TRANSACTION_AMOUNT) || 0 :
                        (typeof transaction.TRANSACTION_AMOUNT === 'number' ? transaction.TRANSACTION_AMOUNT : 0);

                    transactions.push({
                        ...transaction,
                        TRANSACTION_AMOUNT_NUMERIC: amount,
                        fileId: fileId
                    });
                }
            });
        });

        // Sort by transaction amount (descending)
        transactions.sort((a, b) => b.TRANSACTION_AMOUNT_NUMERIC - a.TRANSACTION_AMOUNT_NUMERIC);

        return transactions;
    }

    // Toggle transaction details display for a specific date
    toggleTransactionDetails(date, toggleButton) {
        const detailsContainer = document.getElementById(`details-${date}`);
        const toggleIcon = toggleButton.querySelector('.toggle-icon');

        if (!detailsContainer) {
            console.error(`Details container not found for date: ${date}`);
            return;
        }

        const isCurrentlyVisible = detailsContainer.style.display !== 'none';

        if (isCurrentlyVisible) {
            // Hide details
            detailsContainer.style.display = 'none';
            toggleIcon.textContent = '▼';
            toggleButton.setAttribute('title', 'View transaction details');
        } else {
            // Show details
            detailsContainer.style.display = 'block';
            toggleIcon.textContent = '▲';
            toggleButton.setAttribute('title', 'Hide transaction details');

            // Load transaction details if not already loaded
            this.loadTransactionDetails(date, detailsContainer);
        }
    }

    // Load and display transaction details for a specific date
    loadTransactionDetails(date, container) {
        const transactions = this.getDateHighValueTransactions(date);

        if (transactions.length === 0) {
            container.innerHTML = '<div class="transaction-details-empty">No transaction details available for this date.</div>';
            return;
        }

        // Create transaction details table
        let tableHTML = `
            <div class="transaction-details-table">
                <div class="transaction-details-header">
                    <h5>Transaction Details for ${this.formatDateForDisplay(date)}</h5>
                    <span class="transaction-count">${transactions.length} transaction${transactions.length !== 1 ? 's' : ''}</span>
                </div>
                <table class="transaction-table">
                    <thead>
                        <tr>
                            <th>Participant Name</th>
                            <th>Counterparty</th>
                            <th>Transaction Amount</th>
                            <th>Report Type</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        transactions.forEach(transaction => {
            const participantName = transaction.PARTICIPANT_NAME_CONDUCTOR || transaction.CUSTOMER_NAME || 'Unknown';
            const counterpartyName = transaction.PARTICIPANT_NAME_COUNTERPARTY || 'N/A';
            const formattedAmount = this.formatNumber(transaction.TRANSACTION_AMOUNT_NUMERIC) + ' ' + transaction.TRANSACTION_CURRENCY;
            const reportType = transaction.REPORTTYPE || 'Unknown';

            tableHTML += `
                <tr>
                    <td class="participant-name">${participantName}</td>
                    <td class="counterparty-name">${counterpartyName}</td>
                    <td class="transaction-amount">${formattedAmount}</td>
                    <td class="report-type">${reportType}</td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHTML;
    }

    // Add file-specific high-value transaction data
    addFileHighValueTransactions(fileId, fileName, transactionData) {
        // Validate input data
        if (!transactionData || !Array.isArray(transactionData)) {
            console.warn('Invalid transaction data provided to addFileHighValueTransactions');
            return;
        }

        // Store the high-value transactions for this file
        this.fileHighValueTransactions.set(fileId, transactionData);

        // Calculate date-based counts for this file
        const fileDateCounts = new Map();
        let fileHighValueCount = 0;

        transactionData.forEach(transaction => {
            // Validate transaction object
            if (!transaction || typeof transaction !== 'object') {
                return;
            }

            // Check if this is a high-value transaction (>=1B MMK)
            const amount = typeof transaction.TRANSACTION_AMOUNT === 'string' ?
                parseFloat(transaction.TRANSACTION_AMOUNT) || 0 :
                (typeof transaction.TRANSACTION_AMOUNT === 'number' ? transaction.TRANSACTION_AMOUNT : 0);

            if (transaction.TRANSACTION_CURRENCY === 'MMK' && amount >= this.HIGH_VALUE_THRESHOLD) {
                fileHighValueCount++;
                const date = this.extractAndFormatDate(transaction.TRANSACTION_DATE);
                const currentCount = fileDateCounts.get(date) || 0;
                fileDateCounts.set(date, currentCount + 1);
            }
        });

        // Store file-specific counts
        this.fileHighValueCounts.set(fileId, {
            dateCountMap: fileDateCounts,
            totalCount: fileHighValueCount,
            fileName: fileName
        });

        // Update global date counts by adding this file's contributions
        fileDateCounts.forEach((count, date) => {
            const currentGlobalCount = this.dateHighValueCounts.get(date) || 0;
            this.dateHighValueCounts.set(date, currentGlobalCount + count);
        });

        // Increment processed files count if this file has high-value transactions
        if (fileHighValueCount > 0) {
            this.highValueFileMetrics.filesProcessed++;
            this.logDebug(`Incremented high-value files processed count to: ${this.highValueFileMetrics.filesProcessed}`);
        }

        this.logInfo(`Added high-value transactions for file ${fileName}: ${fileHighValueCount} transactions across ${fileDateCounts.size} dates`);
        this.logDebug('File date breakdown:', Array.from(fileDateCounts.entries()));
        this.logDebug('Updated global date counts:', Array.from(this.dateHighValueCounts.entries()));

        // Dispatch event to notify other components that high-value data has been updated
        if (fileHighValueCount > 0) {
            document.dispatchEvent(new CustomEvent('dataProcessorUpdated', {
                detail: {
                    type: 'highValueTransactionsAdded',
                    fileId: fileId,
                    fileName: fileName,
                    transactionCount: fileHighValueCount
                }
            }));
        }
    }

    // Remove file-specific high-value transaction data
    removeFileHighValueTransactions(fileId) {
        const fileData = this.fileHighValueCounts.get(fileId);
        if (!fileData) {
            this.logWarn(`No high-value transaction data found for file ID: ${fileId}`);
            return { removedDates: [], removedTransactions: 0 };
        }

        const { dateCountMap, totalCount, fileName } = fileData;
        const removedDates = [];
        const dateDetails = [];

        // Remove this file's contributions from global date counts
        dateCountMap.forEach((count, date) => {
            const currentGlobalCount = this.dateHighValueCounts.get(date) || 0;
            const newCount = currentGlobalCount - count;
            const displayDate = this.formatDateForDisplay(date);

            if (newCount <= 0) {
                this.dateHighValueCounts.delete(date);
                removedDates.push(date);
                dateDetails.push(`${displayDate}: ${count} transactions (date completely removed)`);
            } else {
                this.dateHighValueCounts.set(date, newCount);
                dateDetails.push(`${displayDate}: ${count} transactions (${newCount} remaining)`);
            }
        });

        // Remove file-specific data
        this.fileHighValueTransactions.delete(fileId);
        this.fileHighValueCounts.delete(fileId);

        // Increment removed files count if this file had high-value transactions
        if (totalCount > 0) {
            this.highValueFileMetrics.filesRemoved++;
            this.logDebug(`Incremented high-value files removed count to: ${this.highValueFileMetrics.filesRemoved}`);
        }

        this.logInfo(`Removed high-value transactions for file ${fileName}: ${totalCount} transactions across ${dateCountMap.size} dates`);
        this.logDebug('Date-specific removal details:', dateDetails);
        this.logDebug('Updated global date counts after removal:', Array.from(this.dateHighValueCounts.entries()));

        // Show detailed notification about what was removed
        if (window.app && totalCount > 0) {
            const removedDatesText = removedDates.length > 0 ?
                ` (${removedDates.length} date${removedDates.length > 1 ? 's' : ''} completely cleared)` : '';

            window.app.showNotification(
                `Removed ${totalCount} high-value transaction${totalCount > 1 ? 's' : ''} from ${dateCountMap.size} date${dateCountMap.size > 1 ? 's' : ''}${removedDatesText}`,
                'info',
                5000
            );
        }

        // Update the breakdown display
        this.updateHighValueFileBreakdownCombined();

        return {
            removedDates: removedDates,
            removedTransactions: totalCount,
            dateDetails: dateDetails
        };
    }

    // Legacy method - now rebuilds from file-specific data
    updateHighValueCountsByDate(transactionData) {
        // This method is now primarily used for backward compatibility
        // The preferred approach is to use addFileHighValueTransactions for file-specific tracking
        this.logWarn('updateHighValueCountsByDate called - consider using addFileHighValueTransactions for better file tracking');

        // Validate input data
        if (!transactionData || !Array.isArray(transactionData)) {
            this.logWarn('Invalid transaction data provided to updateHighValueCountsByDate');
            return;
        }

        // Clear existing date counts and rebuild from all active files
        this.rebuildDateCountsFromActiveFiles();
    }

    // Rebuild date counts from all active files
    rebuildDateCountsFromActiveFiles() {
        // Clear global counts
        this.dateHighValueCounts.clear();

        // Rebuild from all file-specific data
        this.fileHighValueCounts.forEach((fileData, fileId) => {
            const { dateCountMap } = fileData;
            dateCountMap.forEach((count, date) => {
                const currentGlobalCount = this.dateHighValueCounts.get(date) || 0;
                this.dateHighValueCounts.set(date, currentGlobalCount + count);
            });
        });

        this.logDebug('Rebuilt global date counts from active files:', Array.from(this.dateHighValueCounts.entries()));
    }

    // Add file-specific high-value USD transaction data
    addFileHighValueTransactionsUSD(fileId, fileName, transactionData) {
        // Validate input data
        if (!transactionData || !Array.isArray(transactionData)) {
            console.warn('Invalid transaction data provided to addFileHighValueTransactionsUSD');
            return;
        }

        // Store the high-value USD transactions for this file
        this.fileHighValueTransactionsUSD.set(fileId, transactionData);

        // Calculate date-based counts for this file
        const fileDateCounts = new Map();
        let fileHighValueCount = 0;

        transactionData.forEach(transaction => {
            // Validate transaction object
            if (!transaction || typeof transaction !== 'object') {
                return;
            }

            // Check if this is a high-value USD transaction (>=10K USD)
            const amount = typeof transaction.TRANSACTION_AMOUNT === 'string' ?
                parseFloat(transaction.TRANSACTION_AMOUNT) || 0 :
                (typeof transaction.TRANSACTION_AMOUNT === 'number' ? transaction.TRANSACTION_AMOUNT : 0);

            if (transaction.TRANSACTION_CURRENCY === 'USD' && amount >= this.HIGH_VALUE_THRESHOLD_USD) {
                fileHighValueCount++;
                const date = this.extractAndFormatDate(transaction.TRANSACTION_DATE);
                const currentCount = fileDateCounts.get(date) || 0;
                fileDateCounts.set(date, currentCount + 1);
            }
        });

        // Store file-specific counts
        this.fileHighValueCountsUSD.set(fileId, {
            dateCountMap: fileDateCounts,
            totalCount: fileHighValueCount,
            fileName: fileName
        });

        // Update global date counts by adding this file's contributions
        fileDateCounts.forEach((count, date) => {
            const currentGlobalCount = this.dateHighValueCountsUSD.get(date) || 0;
            this.dateHighValueCountsUSD.set(date, currentGlobalCount + count);
        });

        this.logInfo(`Added high-value USD transactions for file ${fileName}: ${fileHighValueCount} transactions across ${fileDateCounts.size} dates`);
        this.logDebug('USD File date breakdown:', Array.from(fileDateCounts.entries()));
        this.logDebug('Updated global USD date counts:', Array.from(this.dateHighValueCountsUSD.entries()));

        // Dispatch event to notify other components that high-value USD data has been updated
        if (fileHighValueCount > 0) {
            document.dispatchEvent(new CustomEvent('dataProcessorUpdated', {
                detail: {
                    type: 'highValueTransactionsUSDAdded',
                    fileId: fileId,
                    fileName: fileName,
                    transactionCount: fileHighValueCount
                }
            }));
        }
    }

    // Remove file-specific high-value USD transaction data
    removeFileHighValueTransactionsUSD(fileId) {
        const fileData = this.fileHighValueCountsUSD.get(fileId);
        if (!fileData) {
            this.logWarn(`No high-value USD transaction data found for file ID: ${fileId}`);
            return { removedDates: [], removedTransactions: 0 };
        }

        const { dateCountMap, totalCount, fileName } = fileData;
        const removedDates = [];
        const dateDetails = [];

        // Remove this file's contributions from global date counts
        dateCountMap.forEach((count, date) => {
            const currentGlobalCount = this.dateHighValueCountsUSD.get(date) || 0;
            const newCount = currentGlobalCount - count;
            const displayDate = this.formatDateForDisplay(date);

            if (newCount <= 0) {
                this.dateHighValueCountsUSD.delete(date);
                removedDates.push(date);
                dateDetails.push(`${displayDate}: ${count} transactions (date completely removed)`);
            } else {
                this.dateHighValueCountsUSD.set(date, newCount);
                dateDetails.push(`${displayDate}: ${count} transactions (${newCount} remaining)`);
            }
        });

        // Remove file-specific data
        this.fileHighValueTransactionsUSD.delete(fileId);
        this.fileHighValueCountsUSD.delete(fileId);

        this.logInfo(`Removed high-value USD transactions for file ${fileName}: ${totalCount} transactions across ${dateCountMap.size} dates`);
        this.logDebug('USD Date-specific removal details:', dateDetails);
        this.logDebug('Updated global USD date counts after removal:', Array.from(this.dateHighValueCountsUSD.entries()));

        // Show detailed notification about what was removed
        if (window.app && totalCount > 0) {
            const removedDatesText = removedDates.length > 0 ?
                ` (${removedDates.length} date${removedDates.length > 1 ? 's' : ''} completely cleared)` : '';

            window.app.showNotification(
                `Removed ${totalCount} high-value USD transaction${totalCount > 1 ? 's' : ''} from ${dateCountMap.size} date${dateCountMap.size > 1 ? 's' : ''}${removedDatesText}`,
                'info',
                5000
            );
        }

        // Update the breakdown display
        this.updateHighValueFileBreakdownCombined();

        return {
            removedDates: removedDates,
            removedTransactions: totalCount,
            dateDetails: dateDetails
        };
    }

    // Extract and format date from transaction date string
    extractAndFormatDate(transactionDate) {
        if (!transactionDate) return 'Unknown';

        try {
            // Extract just the date part if there's a space (time component)
            let dateStr = transactionDate;
            if (dateStr.includes(' ')) {
                dateStr = dateStr.split(' ')[0];
            }

            // Format: "26-JAN-25" (DD-MMM-YY)
            if (/^\d{2}-[A-Z]{3}-\d{2}$/.test(dateStr)) {
                return dateStr;
            }
            // Format: "2023-01-01" (ISO format YYYY-MM-DD)
            else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                // Convert to DD-MMM-YY format for consistency
                const dateObj = new Date(dateStr);
                if (!isNaN(dateObj.getTime())) {
                    const formatted = dateObj.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: '2-digit'
                    }).replace(/ /g, '-').toUpperCase();
                    return formatted;
                } else {
                    return dateStr;
                }
            }
            // Try to parse as date if it's in another format
            else {
                const dateObj = new Date(dateStr);
                if (!isNaN(dateObj.getTime())) {
                    const formatted = dateObj.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: '2-digit'
                    }).replace(/ /g, '-').toUpperCase();
                    return formatted;
                } else {
                    return dateStr;
                }
            }
        } catch (error) {
            this.logError('Error parsing date:', error, 'Input:', transactionDate);
            return 'Unknown';
        }
    }

    // Format date for user-friendly display
    formatDateForDisplay(dateStr) {
        if (!dateStr || dateStr === 'Unknown') return 'Unknown Date';

        try {
            // Handle DD-MMM-YY format (e.g., "26-JAN-25")
            if (/^\d{2}-[A-Z]{3}-\d{2}$/.test(dateStr)) {
                const [day, month, year] = dateStr.split('-');
                const monthNames = {
                    'JAN': 'January', 'FEB': 'February', 'MAR': 'March', 'APR': 'April',
                    'MAY': 'May', 'JUN': 'June', 'JUL': 'July', 'AUG': 'August',
                    'SEP': 'September', 'OCT': 'October', 'NOV': 'November', 'DEC': 'December'
                };
                const fullYear = year.length === 2 ? `20${year}` : year;
                return `${monthNames[month] || month} ${parseInt(day)}, ${fullYear}`;
            }

            // Try to parse as a regular date
            const dateObj = new Date(dateStr);
            if (!isNaN(dateObj.getTime())) {
                return dateObj.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }

            return dateStr;
        } catch (error) {
            this.logError('Error formatting date for display:', error);
            return dateStr;
        }
    }

    // Update the high-value date breakdown display
    updateHighValueFileBreakdown() {
        const breakdownSection = document.getElementById('highValueBreakdown');
        const fileList = document.getElementById('highValueFileList');
        const breakdownToggle = document.getElementById('breakdownToggle');
        const breakdownContent = document.getElementById('breakdownContent');

        if (!breakdownSection || !fileList) {
            this.logError('Required DOM elements not found for breakdown display');
            return;
        }

        // Preserve current toggle state before updating
        if (breakdownToggle && this.breakdownToggleState.isInitialized) {
            this.breakdownToggleState.isExpanded = breakdownToggle.getAttribute('aria-expanded') === 'true';
            this.logDebug('Preserving toggle state:', this.breakdownToggleState.isExpanded);
        }

        // Get dates with high-value transactions
        const datesWithHighValue = Array.from(this.dateHighValueCounts.entries())
            .filter(([date, count]) => count > 0)
            .sort((a, b) => {
                // Sort by count descending, then by date alphabetically
                if (b[1] !== a[1]) {
                    return b[1] - a[1];
                }
                return a[0].localeCompare(b[0]);
            });

        // Show/hide breakdown section based on whether there are dates with high-value transactions
        if (datesWithHighValue.length > 0) {
            breakdownSection.style.display = 'block';

            // Calculate total transactions and get file details
            const totalTransactions = datesWithHighValue.reduce((sum, [, count]) => sum + count, 0);
            const activeFileCount = this.fileHighValueCounts.size;

            // Update date list with enhanced details
            fileList.innerHTML = '';

            // Add summary header
            const summaryHeader = document.createElement('div');
            summaryHeader.className = 'breakdown-summary';
            summaryHeader.innerHTML = `
                <div class="summary-stats">
                    <span class="stat-item">
                        <strong>${this.formatNumber(totalTransactions)}</strong> total high-value transactions
                    </span>
                    <span class="stat-item">
                        <strong>${datesWithHighValue.length}</strong> dates with activity
                    </span>
                    <span class="stat-item">
                        <strong>${activeFileCount}</strong> active files contributing
                    </span>
                </div>
            `;
            fileList.appendChild(summaryHeader);

            // Add date breakdown items
            datesWithHighValue.forEach(([date, count], index) => {
                const item = document.createElement('div');
                item.className = 'file-breakdown-item';
                const displayDate = this.formatDateForDisplay(date);
                const percentage = ((count / totalTransactions) * 100).toFixed(1);

                item.innerHTML = `
                    <div class="breakdown-item-main" data-date="${date}">
                        <span class="file-breakdown-name" title="${displayDate}">${displayDate}</span>
                        <span class="file-breakdown-count">${this.formatNumber(count)}</span>
                        <button class="transaction-details-toggle" data-date="${date}" title="View transaction details">
                            <span class="toggle-icon">▼</span>
                        </button>
                    </div>
                    <div class="breakdown-item-details">
                        <span class="breakdown-percentage">${percentage}% of total</span>
                        <span class="breakdown-rank">#${index + 1} by volume</span>
                    </div>
                    <div class="transaction-details-container" id="details-${date}" style="display: none;">
                        <div class="transaction-details-loading">Loading transaction details...</div>
                    </div>
                `;

                // Add click handler for transaction details toggle
                const toggleButton = item.querySelector('.transaction-details-toggle');
                toggleButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleTransactionDetails(date, toggleButton);
                });

                fileList.appendChild(item);
            });

            // Always ensure toggle functionality is properly initialized and state is restored
            this.ensureBreakdownToggleFunctionality();
        } else {
            // Reset toggle state when hiding section
            this.breakdownToggleState.isExpanded = false;
            this.breakdownToggleState.isInitialized = false;
            breakdownSection.style.display = 'none';
        }
    }

    // Ensure breakdown toggle functionality is properly initialized and state is preserved
    ensureBreakdownToggleFunctionality() {
        const breakdownToggle = document.getElementById('breakdownToggle');
        const breakdownContent = document.getElementById('breakdownContent');
        const breakdownHeader = document.querySelector('.breakdown-header');

        if (!breakdownToggle || !breakdownContent || !breakdownHeader) {
            this.logError('Required toggle elements not found for breakdown functionality');
            return;
        }

        // Initialize toggle functionality if not already done
        if (!this.breakdownToggleState.isInitialized) {
            this.initializeBreakdownToggle();
        }

        // Restore toggle state
        this.restoreToggleState();

        this.logDebug('Toggle functionality ensured, state restored:', this.breakdownToggleState.isExpanded);
    }

    // Initialize the breakdown toggle functionality
    initializeBreakdownToggle() {
        const breakdownToggle = document.getElementById('breakdownToggle');
        const breakdownContent = document.getElementById('breakdownContent');
        const breakdownHeader = document.querySelector('.breakdown-header');

        if (!breakdownToggle || !breakdownContent || !breakdownHeader) {
            this.logError('Required toggle elements not found');
            return;
        }

        // Create toggle function that updates our state tracking
        const toggleBreakdown = () => {
            const currentState = this.breakdownToggleState.isExpanded;
            const newState = !currentState;

            // Update our state tracking
            this.breakdownToggleState.isExpanded = newState;

            // Update DOM attributes
            breakdownToggle.setAttribute('aria-expanded', newState.toString());

            // Update content visibility with animation
            if (newState) {
                breakdownContent.style.display = 'block';
                breakdownContent.classList.add('expanded');
            } else {
                breakdownContent.classList.remove('expanded');
                // Delay hiding to allow animation
                setTimeout(() => {
                    if (!this.breakdownToggleState.isExpanded) {
                        breakdownContent.style.display = 'none';
                    }
                }, 300);
            }

            // Update toggle text
            const toggleText = breakdownToggle.querySelector('.toggle-text');
            if (toggleText) {
                toggleText.textContent = newState ? 'Hide Details' : 'Show Details';
            }

            this.logDebug('Toggle state changed to:', newState);
        };

        // Remove any existing event listeners to prevent duplicates
        this.removeToggleEventListeners();

        // Add fresh event listeners
        this.toggleClickHandler = (e) => {
            e.stopPropagation();
            toggleBreakdown();
        };

        this.headerClickHandler = (e) => {
            // Only toggle if clicking on the header itself, not the button
            if (e.target === breakdownHeader || e.target === breakdownHeader.querySelector('h4')) {
                toggleBreakdown();
            }
        };

        breakdownToggle.addEventListener('click', this.toggleClickHandler);
        breakdownHeader.addEventListener('click', this.headerClickHandler);

        // Mark as initialized
        this.breakdownToggleState.isInitialized = true;
        breakdownToggle.setAttribute('data-listeners-attached', 'true');

        this.logDebug('Toggle functionality initialized');
    }

    // Remove existing toggle event listeners
    removeToggleEventListeners() {
        const breakdownToggle = document.getElementById('breakdownToggle');
        const breakdownHeader = document.querySelector('.breakdown-header');

        if (breakdownToggle && this.toggleClickHandler) {
            breakdownToggle.removeEventListener('click', this.toggleClickHandler);
        }

        if (breakdownHeader && this.headerClickHandler) {
            breakdownHeader.removeEventListener('click', this.headerClickHandler);
        }
    }

    // Restore toggle state after DOM updates
    restoreToggleState() {
        const breakdownToggle = document.getElementById('breakdownToggle');
        const breakdownContent = document.getElementById('breakdownContent');

        if (!breakdownToggle || !breakdownContent) {
            return;
        }

        const targetState = this.breakdownToggleState.isExpanded;

        // Set DOM attributes to match our state
        breakdownToggle.setAttribute('aria-expanded', targetState.toString());

        // Set content visibility
        if (targetState) {
            breakdownContent.style.display = 'block';
            breakdownContent.classList.add('expanded');
        } else {
            breakdownContent.style.display = 'none';
            breakdownContent.classList.remove('expanded');
        }

        // Update toggle text
        const toggleText = breakdownToggle.querySelector('.toggle-text');
        if (toggleText) {
            toggleText.textContent = targetState ? 'Hide Details' : 'Show Details';
        }

        this.logDebug('Toggle state restored to:', targetState);
    }

    // Update the high-value USD date breakdown display
    updateHighValueFileBreakdownUSD() {
        const breakdownSection = document.getElementById('highValueBreakdownUSD');
        const fileList = document.getElementById('highValueFileListUSD');
        const breakdownToggle = document.getElementById('breakdownToggleUSD');
        const breakdownContent = document.getElementById('breakdownContentUSD');

        if (!breakdownSection || !fileList) {
            this.logError('Required DOM elements not found for USD breakdown display');
            return;
        }

        // Get dates with high-value USD transactions, sorted by date
        const datesWithHighValue = Array.from(this.dateHighValueCountsUSD.entries())
            .sort((a, b) => {
                const dateA = new Date(a[0]);
                const dateB = new Date(b[0]);
                return dateB - dateA; // Most recent first
            });

        // Show/hide breakdown section based on whether there are dates with high-value transactions
        if (datesWithHighValue.length > 0) {
            breakdownSection.style.display = 'block';

            // Calculate total transactions and get file details
            const totalTransactions = datesWithHighValue.reduce((sum, [, count]) => sum + count, 0);
            const activeFileCount = this.fileHighValueCountsUSD.size;

            // Update date list with enhanced details
            fileList.innerHTML = '';

            datesWithHighValue.forEach(([date, count], index) => {
                const displayDate = this.formatDateForDisplay(date);
                const percentage = totalTransactions > 0 ? ((count / totalTransactions) * 100).toFixed(1) : '0.0';

                // Get detailed transactions for this date
                const dateTransactions = this.getDateHighValueTransactionsUSD(date);
                const uniqueCustomers = new Set(dateTransactions.map(t => t.CUSTOMER_NAME || 'Unknown')).size;

                // Create breakdown item
                const breakdownItem = document.createElement('div');
                breakdownItem.className = 'breakdown-item';
                breakdownItem.innerHTML = `
                    <div class="breakdown-item-header">
                        <div class="breakdown-item-info">
                            <span class="breakdown-date">${displayDate}</span>
                            <span class="breakdown-count">${this.formatNumber(count)} transaction${count !== 1 ? 's' : ''}</span>
                        </div>
                        <div class="breakdown-item-details">
                            <span class="breakdown-percentage">${percentage}%</span>
                            <span class="breakdown-rank">#${index + 1}</span>
                        </div>
                    </div>
                    <div class="breakdown-item-summary">
                        <div class="summary-stats">
                            <span class="stat-item">
                                <span class="stat-label">Customers:</span>
                                <span class="stat-value">${uniqueCustomers}</span>
                            </span>
                            <span class="stat-item">
                                <span class="stat-label">Avg per Customer:</span>
                                <span class="stat-value">${(count / uniqueCustomers).toFixed(1)}</span>
                            </span>
                        </div>
                    </div>
                `;

                // Add click handler to show transaction details
                breakdownItem.addEventListener('click', () => {
                    this.showDateTransactionDetailsUSD(date, displayDate, dateTransactions);
                });

                fileList.appendChild(breakdownItem);
            });

            // Set up toggle functionality if not already done
            if (breakdownToggle && !breakdownToggle.hasAttribute('data-usd-initialized')) {
                breakdownToggle.setAttribute('data-usd-initialized', 'true');
                breakdownToggle.addEventListener('click', () => {
                    const isExpanded = breakdownToggle.getAttribute('aria-expanded') === 'true';
                    const newState = !isExpanded;

                    breakdownToggle.setAttribute('aria-expanded', newState.toString());

                    if (newState) {
                        breakdownContent.style.display = 'block';
                        breakdownContent.classList.add('expanded');
                    } else {
                        breakdownContent.style.display = 'none';
                        breakdownContent.classList.remove('expanded');
                    }

                    // Update toggle text
                    const toggleText = breakdownToggle.querySelector('.toggle-text');
                    if (toggleText) {
                        toggleText.textContent = newState ? 'Hide Details' : 'Show Details';
                    }

                    // Update toggle icon
                    const toggleIcon = breakdownToggle.querySelector('.toggle-icon');
                    if (toggleIcon) {
                        toggleIcon.textContent = newState ? '▲' : '▼';
                    }
                });
            }

            this.logDebug(`USD breakdown updated: ${datesWithHighValue.length} dates, ${totalTransactions} total transactions, ${activeFileCount} active files`);
        } else {
            breakdownSection.style.display = 'none';
            this.logDebug('USD breakdown hidden - no high-value transactions');
        }
    }

    // Get detailed high-value USD transactions for a specific date
    getDateHighValueTransactionsUSD(date) {
        const transactions = [];

        // Iterate through all file high-value USD transactions
        this.fileHighValueTransactionsUSD.forEach((fileTransactions, fileId) => {
            fileTransactions.forEach(transaction => {
                const transactionDate = this.extractAndFormatDate(transaction.TRANSACTION_DATE);
                if (transactionDate === date) {
                    // Ensure amount is a number for sorting
                    const amount = typeof transaction.TRANSACTION_AMOUNT === 'string' ?
                        parseFloat(transaction.TRANSACTION_AMOUNT) || 0 :
                        (typeof transaction.TRANSACTION_AMOUNT === 'number' ? transaction.TRANSACTION_AMOUNT : 0);

                    transactions.push({
                        ...transaction,
                        TRANSACTION_AMOUNT_NUMERIC: amount,
                        fileId: fileId
                    });
                }
            });
        });

        // Sort by amount (highest first)
        transactions.sort((a, b) => b.TRANSACTION_AMOUNT_NUMERIC - a.TRANSACTION_AMOUNT_NUMERIC);

        return transactions;
    }

    // Show detailed transaction information for a specific date (USD)
    showDateTransactionDetailsUSD(date, displayDate, transactions) {
        if (!transactions || transactions.length === 0) {
            if (window.app) {
                window.app.showNotification(`No transaction details available for ${displayDate}`, 'info', 3000);
            }
            return;
        }

        // Use the new table display method instead of popup
        this.displayTransactionsInTable(transactions, `USD Transactions for ${displayDate}`, 'USD');
        this.logInfo(`Showed USD transaction details for ${displayDate}: ${transactions.length} transactions in table`);
    }

    // Show detailed transaction information for a specific date (MMK)
    showDateTransactionDetails(date, displayDate, transactions) {
        if (!transactions || transactions.length === 0) {
            if (window.app) {
                window.app.showNotification(`No transaction details available for ${displayDate}`, 'info', 3000);
            }
            return;
        }

        // Use the new table display method instead of popup
        this.displayTransactionsInTable(transactions, `MMK Transactions for ${displayDate}`, 'MMK');
        this.logInfo(`Showed MMK transaction details for ${displayDate}: ${transactions.length} transactions in table`);
    }

    // Wrapper function for USD transactions (called from HTML onclick)
    showTransactionDetailsUSD(date) {
        const displayDate = this.formatDateForDisplay(date);
        const transactions = this.getDateHighValueTransactionsUSD(date);
        this.showDateTransactionDetailsUSD(date, displayDate, transactions);
    }

    // Wrapper function for MMK transactions (called from HTML onclick)
    showTransactionDetails(date) {
        const displayDate = this.formatDateForDisplay(date);
        const transactions = this.getDateHighValueTransactions(date);
        this.showDateTransactionDetails(date, displayDate, transactions);
    }

    // Update the combined high-value date breakdown display (MMK and USD)
    updateHighValueFileBreakdownCombined() {
        const breakdownSection = document.getElementById('highValueBreakdownCombined');
        const fileList = document.getElementById('highValueFileListCombined');
        const breakdownToggle = document.getElementById('breakdownToggleCombined');
        const breakdownContent = document.getElementById('breakdownContentCombined');

        if (!breakdownSection || !fileList) {
            this.logError('Required DOM elements not found for combined breakdown display');
            return;
        }

        // Combine dates from both MMK and USD high-value transactions
        const allDates = new Set([
            ...this.dateHighValueCounts.keys(),
            ...this.dateHighValueCountsUSD.keys()
        ]);

        // Create combined date data
        const combinedDateData = Array.from(allDates).map(date => {
            const mmkCount = this.dateHighValueCounts.get(date) || 0;
            const usdCount = this.dateHighValueCountsUSD.get(date) || 0;
            const totalCount = mmkCount + usdCount;

            return {
                date,
                mmkCount,
                usdCount,
                totalCount
            };
        }).filter(item => item.totalCount > 0)
          .sort((a, b) => {
              const dateA = new Date(a.date);
              const dateB = new Date(b.date);
              return dateB - dateA; // Most recent first
          });

        // Show/hide breakdown section based on whether there are dates with high-value transactions
        if (combinedDateData.length > 0) {
            breakdownSection.style.display = 'block';

            // Calculate total transactions and get file details
            const totalTransactions = combinedDateData.reduce((sum, item) => sum + item.totalCount, 0);
            const totalMMKTransactions = combinedDateData.reduce((sum, item) => sum + item.mmkCount, 0);
            const totalUSDTransactions = combinedDateData.reduce((sum, item) => sum + item.usdCount, 0);
            const activeFileCountMMK = this.fileHighValueCounts.size;
            const activeFileCountUSD = this.fileHighValueCountsUSD.size;

            // Update date list with combined details
            fileList.innerHTML = '';

            // Add summary section
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'breakdown-summary';
            summaryDiv.innerHTML = `
                <div class="summary-stats">
                    <div class="stat-item">
                        <span>Total Dates:</span>
                        <strong>${this.formatNumber(combinedDateData.length)}</strong>
                    </div>
                    <div class="stat-item">
                        <span>Total Transactions:</span>
                        <strong>${this.formatNumber(totalTransactions)}</strong>
                    </div>
                    <div class="stat-item">
                        <span>MMK Transactions:</span>
                        <strong>${this.formatNumber(totalMMKTransactions)}</strong>
                    </div>
                    <div class="stat-item">
                        <span>USD Transactions:</span>
                        <strong>${this.formatNumber(totalUSDTransactions)}</strong>
                    </div>
                    <div class="stat-item">
                        <span>Active Files:</span>
                        <strong>MMK: ${activeFileCountMMK}, USD: ${activeFileCountUSD}</strong>
                    </div>
                </div>
            `;
            fileList.appendChild(summaryDiv);

            // Add individual date items
            combinedDateData.forEach((dateItem, index) => {
                const displayDate = this.formatDateForDisplay(dateItem.date);
                const percentage = ((dateItem.totalCount / totalTransactions) * 100).toFixed(1);
                const rank = index + 1;

                const itemDiv = document.createElement('div');
                itemDiv.className = 'combined-breakdown-item';

                itemDiv.innerHTML = `
                    <div class="combined-breakdown-header">
                        <div class="combined-date-title">${displayDate}</div>
                        <div class="combined-total-count">${this.formatNumber(dateItem.totalCount)}</div>
                    </div>
                    <div class="currency-breakdown">
                        <div class="currency-item mmk" ${dateItem.mmkCount > 0 ? `onclick="window.dataProcessor.showTransactionDetails('${dateItem.date}')"` : ''} style="${dateItem.mmkCount === 0 ? 'opacity: 0.5; cursor: default;' : 'cursor: pointer;'}">
                            <div class="currency-label">MMK (≥1B)</div>
                            <div class="currency-count">${this.formatNumber(dateItem.mmkCount)}</div>
                            <div class="currency-details">
                                ${dateItem.mmkCount > 0 ? `${((dateItem.mmkCount / totalMMKTransactions) * 100).toFixed(1)}% of MMK total` : 'No transactions'}
                            </div>
                        </div>
                        <div class="currency-item usd" ${dateItem.usdCount > 0 ? `onclick="window.dataProcessor.showTransactionDetailsUSD('${dateItem.date}')"` : ''} style="${dateItem.usdCount === 0 ? 'opacity: 0.5; cursor: default;' : 'cursor: pointer;'}">
                            <div class="currency-label">USD (≥10K)</div>
                            <div class="currency-count">${this.formatNumber(dateItem.usdCount)}</div>
                            <div class="currency-details">
                                ${dateItem.usdCount > 0 ? `${((dateItem.usdCount / totalUSDTransactions) * 100).toFixed(1)}% of USD total` : 'No transactions'}
                            </div>
                        </div>
                    </div>
                `;

                fileList.appendChild(itemDiv);
            });

            // Set up toggle functionality if not already set
            if (breakdownToggle && !breakdownToggle.hasAttribute('data-combined-listener')) {
                breakdownToggle.setAttribute('data-combined-listener', 'true');

                const toggleBreakdown = () => {
                    const isExpanded = breakdownToggle.getAttribute('aria-expanded') === 'true';
                    const newState = !isExpanded;

                    breakdownToggle.setAttribute('aria-expanded', newState);
                    breakdownContent.style.display = newState ? 'block' : 'none';

                    // Update toggle icon
                    const toggleIcon = breakdownToggle.querySelector('.toggle-icon');
                    if (toggleIcon) {
                        toggleIcon.textContent = newState ? '▲' : '▼';
                    }

                    // Update toggle text
                    const toggleText = breakdownToggle.querySelector('.toggle-text');
                    if (toggleText) {
                        toggleText.textContent = newState ? 'Hide Details' : 'Show Details';
                    }
                };

                breakdownToggle.addEventListener('click', (e) => {
                    e.stopPropagation();
                    toggleBreakdown();
                });
            }

            this.logDebug(`Combined breakdown updated: ${combinedDateData.length} dates, ${totalTransactions} total transactions (${totalMMKTransactions} MMK, ${totalUSDTransactions} USD)`);
        } else {
            breakdownSection.style.display = 'none';
            this.logDebug('Combined breakdown hidden - no high-value transactions');
        }
    }

    // Get the current filtered data
    getFilteredData() {
        return this.filteredData;
    }

    // Get the summary metrics
    getSummaryMetrics() {
        return this.summaryMetrics;
    }

    // Generate a unique file identifier
    generateFileId(file) {
        // Use filename and size as a unique identifier
        return `${file.name}_${file.size}`;
    }

    // Parse date from filename (e.g., "TTR_28Feb2025" -> "28 Feb 2025")
    parseFilenameDate(filename) {
        try {
            // Extract date pattern from filename like "TTR_28Feb2025"
            const dateMatch = filename.match(/TTR_(\d{1,2})([A-Za-z]{3})(\d{4})/i);

            if (dateMatch) {
                const [, day, month, year] = dateMatch;

                // Month name mapping
                const monthNames = {
                    'jan': 'Jan', 'feb': 'Feb', 'mar': 'Mar', 'apr': 'Apr',
                    'may': 'May', 'jun': 'Jun', 'jul': 'Jul', 'aug': 'Aug',
                    'sep': 'Sep', 'oct': 'Oct', 'nov': 'Nov', 'dec': 'Dec'
                };

                const monthName = monthNames[month.toLowerCase()];
                if (monthName) {
                    return `${parseInt(day)} ${monthName} ${year}`;
                }
            }

            // Fallback: try to extract any date-like pattern
            const fallbackMatch = filename.match(/(\d{1,2})([A-Za-z]{3})(\d{4})/i);
            if (fallbackMatch) {
                const [, day, month, year] = fallbackMatch;
                const monthNames = {
                    'jan': 'Jan', 'feb': 'Feb', 'mar': 'Mar', 'apr': 'Apr',
                    'may': 'May', 'jun': 'Jun', 'jul': 'Jul', 'aug': 'Aug',
                    'sep': 'Sep', 'oct': 'Oct', 'nov': 'Nov', 'dec': 'Dec'
                };

                const monthName = monthNames[month.toLowerCase()];
                if (monthName) {
                    return `${parseInt(day)} ${monthName} ${year}`;
                }
            }

            this.logWarn(`Could not parse date from filename: ${filename}`);
            return 'Unknown Date';
        } catch (error) {
            this.logError(`Error parsing filename date: ${error.message}`);
            return 'Unknown Date';
        }
    }

    // Calculate TTR Summary metrics
    calculateTTRSummary() {
        try {
            const ttrData = [];

            // Get included files from TTR File Manager if available, otherwise use all processed files
            let processedFiles = [];
            if (window.ttrFileManager && typeof window.ttrFileManager.getIncludedFiles === 'function') {
                processedFiles = window.ttrFileManager.getIncludedFiles();
                this.logInfo(`TTR Summary using ${processedFiles.length} files from TTR File Manager`);

                // Debug: Log the files being used
                processedFiles.forEach((file, index) => {
                    this.logDebug(`TTR File ${index + 1}: ${file.fileName}`);
                });

                // If TTR File Manager returns empty, check if this is expected
                if (processedFiles.length === 0 && window.fileHandler && window.fileHandler.processedData && window.fileHandler.processedData.length > 0) {
                    // Check if all files were explicitly excluded
                    const excludedCount = window.ttrFileManager.excludedFiles?.size || 0;
                    const includedCount = window.ttrFileManager.includedFiles?.size || 0;
                    const totalProcessed = window.fileHandler.processedData.length;

                    this.logInfo(`TTR File Manager state: ${includedCount} included, ${excludedCount} excluded, ${totalProcessed} total processed`);

                    if (excludedCount === 0 && includedCount === 0) {
                        // This might be a timing issue during file removal, use fallback
                        this.logWarn(`TTR File Manager returned empty due to timing issue, using all ${totalProcessed} processed files as fallback`);
                        processedFiles = window.fileHandler.processedData;

                        // Debug: Log the fallback files being used
                        processedFiles.forEach((file, index) => {
                            this.logDebug(`Fallback File ${index + 1}: ${file.fileName}`);
                        });
                    } else {
                        // This is expected - all files were excluded or removed
                        this.logInfo('TTR File Manager returned empty - all files excluded or no files included');
                        // Don't use fallback in this case, return empty array
                        return [];
                    }
                }
            } else if (window.fileHandler && window.fileHandler.processedData) {
                processedFiles = window.fileHandler.processedData;
                this.logInfo(`TTR Summary using ${processedFiles.length} files from File Handler (no TTR manager)`);

                // Debug: Log the files being used
                processedFiles.forEach((file, index) => {
                    this.logDebug(`Direct File ${index + 1}: ${file.fileName}`);
                });
            }

            if (processedFiles.length === 0) {
                // Enhanced debugging and fallback logic
                const allProcessedFiles = window.fileHandler?.processedData || [];
                const fileMetricsSize = window.fileHandler?.fileMetrics?.size || 0;
                const fileTransactionDataSize = window.fileHandler?.fileTransactionData?.size || 0;

                this.logWarn(`No processed file data available for TTR summary. Debug info:`);
                this.logDebug(`- processedData length: ${allProcessedFiles.length}`);
                this.logDebug(`- fileMetrics size: ${fileMetricsSize}`);
                this.logDebug(`- fileTransactionData size: ${fileTransactionDataSize}`);
                this.logDebug(`- TTR File Manager available: ${!!window.ttrFileManager}`);
                this.logDebug(`- TTR File Manager getIncludedFiles: ${typeof window.ttrFileManager?.getIncludedFiles}`);

                if (allProcessedFiles.length > 0) {
                    this.logDebug('Available files in processedData:', allProcessedFiles.map(f => f.fileName));

                    // Try to use all processed files as a fallback if TTR manager is not working properly
                    if (fileMetricsSize > 0) {
                        this.logInfo('Using all processed files as fallback for TTR summary');
                        processedFiles = allProcessedFiles;
                        // Continue with processing instead of returning empty
                    } else {
                        this.logError('File metrics not available - cannot generate TTR summary');
                        return [];
                    }
                } else {
                    this.logError('No processed files available at all - TTR summary cannot be generated');
                    return [];
                }
            }

            // Ensure cumulative serial ranges are calculated
            if (window.ttrFileManager && typeof window.ttrFileManager.calculateCumulativeSerialRanges === 'function') {
                window.ttrFileManager.calculateCumulativeSerialRanges();
            }

            // Create summary rows - one row per file
            processedFiles.forEach((fileData, index) => {
                const filename = fileData.fileName;
                const reportDate = this.parseFilenameDate(filename);

                // Get metrics for this file
                const fileMetrics = window.fileHandler.fileMetrics?.get(filename) || {};

                // Debug: Log file metrics availability
                const hasMetrics = window.fileHandler.fileMetrics?.has(filename);
                this.logDebug(`TTR File ${index + 1} (${filename}): hasMetrics=${hasMetrics}, metricsKeys=${Object.keys(fileMetrics).length}`);

                // Get unique serial counts
                const hocCount = fileMetrics.hocUniqueSerialCount || 0;
                const ibdUniqueCount = fileMetrics.ibdUniqueSerialCount || 0;
                const wuCount = fileMetrics.wuUniqueSerialCount || 0;

                // TTR Summary Report specific logic: IBD Count = IBD unique serials + WU unique serials
                const ibdCount = ibdUniqueCount + wuCount;

                // Calculate grand total (unique serials across all types)
                // Note: Using ibdUniqueCount + wuCount separately to avoid double counting
                const grandTotal = hocCount + ibdUniqueCount + wuCount;

                // Get 1B+ count from high-value transactions
                const oneBillionPlusCount = fileMetrics.highValueTransactionCount || 0;

                // Debug: Log the counts being used
                this.logDebug(`TTR Counts for ${filename}: HOC=${hocCount}, IBD=${ibdCount} (${ibdUniqueCount}+${wuCount}), WU=${wuCount}, 1B+=${oneBillionPlusCount}, Total=${grandTotal}`);

                // Get cumulative serial ranges from TTR File Manager
                let hocSerial = '-';
                let ibdSerial = '-';
                let wuSerial = '-';

                if (window.ttrFileManager && typeof window.ttrFileManager.getSerialRangeForFile === 'function') {
                    hocSerial = window.ttrFileManager.getSerialRangeForFile(filename, 'hoc');
                    ibdSerial = window.ttrFileManager.getSerialRangeForFile(filename, 'ibd');
                    // For WU Serial, get actual serial numbers instead of range
                    wuSerial = this.getActualWUSerialNumbers(filename);

                    this.logDebug(`TTR Serial Ranges for ${filename}: HOC=${hocSerial}, IBD=${ibdSerial}, WU=${wuSerial}`);
                }

                // Create TTR summary row
                const ttrRow = {
                    reportDate: reportDate,
                    filename: filename,
                    hocSerial: hocSerial,
                    hocCount: hocCount,
                    ibdSerial: ibdSerial,
                    ibdCount: ibdCount,
                    wuSerial: wuSerial,
                    wuCount: wuCount,
                    oneBillionPlusCount: oneBillionPlusCount,
                    grandTotal: grandTotal
                };

                ttrData.push(ttrRow);
                this.logDebug(`TTR Row created for ${filename}:`, ttrRow);
            });

            this.logInfo(`TTR Summary calculated for ${ttrData.length} files`);
            return ttrData;
        } catch (error) {
            this.logError(`Error calculating TTR summary: ${error.message}`);
            return [];
        }
    }

    // Get actual WU serial numbers for a file (for TTR Summary Report)
    getActualWUSerialNumbers(filename) {
        try {
            // Get the file's WU serial numbers from fileHandler
            if (!window.fileHandler || !window.fileHandler.fileSerialNumbers) {
                this.logDebug(`No fileHandler or fileSerialNumbers available for ${filename}`);
                return '-';
            }

            const fileSerialNumbers = window.fileHandler.fileSerialNumbers.get(filename);
            if (!fileSerialNumbers || !fileSerialNumbers.wu || fileSerialNumbers.wu.size === 0) {
                this.logDebug(`No WU serial numbers found for ${filename}`);
                return '-';
            }

            // CRITICAL FIX: Remove truncation limits to show ALL WU serial numbers
            // Keep memory optimization for extremely large sets (>5000) but show all for normal cases
            const maxProcess = 5000; // Increased limit for better user experience

            if (fileSerialNumbers.wu.size > maxProcess) {
                // For extremely large sets, show count with warning
                this.logWarn(`Very large WU serial set detected: ${fileSerialNumbers.wu.size} serials. Showing count only.`);
                return `${fileSerialNumbers.wu.size} WU serials (extremely large set - contact support if full list needed)`;
            }

            // Convert Set to sorted array - process ALL serials (no artificial limit)
            const wuSerials = Array.from(fileSerialNumbers.wu);

            // Sort all serials for consistent display
            wuSerials.sort((a, b) => {
                // Try to sort numerically if possible, otherwise alphabetically
                const numA = parseInt(a);
                const numB = parseInt(b);
                if (!isNaN(numA) && !isNaN(numB)) {
                    return numA - numB;
                }
                return a.localeCompare(b);
            });

            // CRITICAL FIX: Return complete comma-separated list without truncation
            const result = wuSerials.join(',');
            this.logDebug(`WU serials for ${filename}: showing all ${wuSerials.length} serials`);
            return result;

        } catch (error) {
            this.logError(`Error getting WU serial numbers for ${filename}: ${error.message}`);
            return '-';
        }
    }

    // Update TTR Summary Report table
    updateTTRSummaryReport() {
        try {
            const tableBody = document.getElementById('ttrSummaryTableBody');
            if (!tableBody) {
                this.logError('TTR Summary table body not found');
                return;
            }

            this.logInfo('TTR Summary table body found, proceeding with update');

            // Pre-flight check: Ensure data is available
            const fileHandlerExists = window.fileHandler && window.fileHandler.processedData;
            const hasProcessedData = fileHandlerExists && window.fileHandler.processedData.length > 0;
            const hasActiveFiles = window.fileHandler && window.fileHandler.files &&
                                 window.fileHandler.files.some(file => !file.isRemoved);

            // CRITICAL FIX: Stop infinite retry loop when no files are available
            if (!fileHandlerExists) {
                this.logWarn('FileHandler not available for TTR update');
                return;
            }

            if (!hasProcessedData && !hasActiveFiles) {
                // No processed data and no active files - show empty state instead of retrying
                this.logInfo('No processed data or active files available - showing empty TTR table');
                this.showEmptyTTRTable();
                return;
            }

            if (!hasProcessedData && hasActiveFiles) {
                // Files exist but not processed yet - retry with limit
                if (!this.ttrRetryCount) this.ttrRetryCount = 0;
                this.ttrRetryCount++;

                if (this.ttrRetryCount > 10) {
                    this.logWarn('TTR update retry limit reached - showing empty table');
                    this.ttrRetryCount = 0;
                    this.showEmptyTTRTable();
                    return;
                }

                this.logWarn(`FileHandler data not ready for TTR update, scheduling retry (${this.ttrRetryCount}/10)...`);
                setTimeout(() => {
                    this.updateTTRSummaryReport();
                }, 500);
                return;
            }

            // Reset retry count on successful data availability
            this.ttrRetryCount = 0;

            // Calculate TTR summary data
            const ttrData = this.calculateTTRSummary();
            this.logInfo(`TTR Summary data calculated: ${ttrData.length} rows`);

            // Debug: Log the table body before clearing
            this.logDebug(`Table body before clearing - children: ${tableBody.children.length}, innerHTML length: ${tableBody.innerHTML.length}`);

            // Clear existing content
            tableBody.innerHTML = '';
            this.logDebug('Table body cleared');

            if (ttrData.length === 0) {
                // Show empty message when no data exists
                const emptyRow = document.createElement('tr');
                emptyRow.className = 'empty-table-message';
                emptyRow.innerHTML = '<td colspan="10">No data available. Please upload CSV files to view TTR summary report.</td>';
                tableBody.appendChild(emptyRow);
                this.logInfo('TTR Summary table updated with empty message');
                return;
            }

            // Populate table with TTR data
            ttrData.forEach((item, index) => {
                this.logDebug(`Creating row ${index + 1} for file: ${item.filename}`);

                const row = document.createElement('tr');
                row.className = 'ttr-summary-row';
                row.setAttribute('data-filename', item.filename);

                const rowHTML = `
                    <td class="ttr-report-date">${item.reportDate}</td>
                    <td class="ttr-serial-range ${item.hocSerial === '-' ? 'ttr-serial-placeholder' : 'ttr-serial-value'}">${item.hocSerial}</td>
                    <td class="ttr-count-value">${this.formatNumber(item.hocCount)}</td>
                    <td class="ttr-serial-range ${item.ibdSerial === '-' ? 'ttr-serial-placeholder' : 'ttr-serial-value'}">${item.ibdSerial}</td>
                    <td class="ttr-count-value">${this.formatNumber(item.ibdCount)}</td>
                    <td class="${item.wuSerial === '-' ? 'ttr-serial-placeholder' : 'ttr-wu-serial-list'}">${item.wuSerial}</td>
                    <td class="ttr-count-value">${this.formatNumber(item.wuCount)}</td>
                    <td class="ttr-count-value">${this.formatNumber(item.oneBillionPlusCount)}</td>
                    <td class="ttr-grand-total">${this.formatNumber(item.grandTotal)}</td>
                    <td class="ttr-actions">
                        <button class="btn btn-warning ttr-remove-file-btn" data-filename="${item.filename}" title="Remove file from TTR Summary Report">
                            Remove
                        </button>
                    </td>
                `;

                row.innerHTML = rowHTML;
                this.logDebug(`Row HTML for ${item.filename}: ${rowHTML.substring(0, 100)}...`);

                tableBody.appendChild(row);
                this.logDebug(`Row ${index + 1} appended to table body. Table now has ${tableBody.children.length} children`);
            });

            // Add event listeners to remove buttons
            tableBody.querySelectorAll('.ttr-remove-file-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const fileName = e.target.getAttribute('data-filename');
                    if (window.ttrFileManager && typeof window.ttrFileManager.removeFileFromTable === 'function') {
                        window.ttrFileManager.removeFileFromTable(fileName);
                    } else {
                        // Fallback confirmation
                        if (confirm(`Remove "${fileName}" from TTR Summary Report?`)) {
                            // Remove the row from the table
                            const row = e.target.closest('tr');
                            if (row) {
                                row.remove();
                            }
                        }
                    }
                });
            });

            // Final verification
            this.logInfo(`TTR Summary table updated with ${ttrData.length} rows`);
            this.logDebug(`Final table body state - children: ${tableBody.children.length}, visible: ${tableBody.style.display !== 'none'}`);

            // Check if the table container is visible
            const ttrSection = document.getElementById('ttrSummaryReport');
            const ttrTable = tableBody.closest('table');
            this.logDebug(`TTR Section visible: ${ttrSection ? ttrSection.style.display !== 'none' : 'not found'}`);
            this.logDebug(`TTR Table visible: ${ttrTable ? ttrTable.style.display !== 'none' : 'not found'}`);

            // Force a visual update
            if (tableBody.parentElement) {
                tableBody.parentElement.style.display = 'table';
            }

            // Dispatch event to notify that TTR summary has been updated
            document.dispatchEvent(new CustomEvent('ttrSummaryUpdated', {
                detail: {
                    recordCount: ttrData.length,
                    timestamp: Date.now()
                }
            }));

        } catch (error) {
            this.logError(`Error updating TTR Summary report: ${error.message}`);
            console.error('TTR Update Error:', error);
        }
    }

    // Reset TTR retry counter (called when files are added/removed)
    resetTTRRetryCounter() {
        this.ttrRetryCount = 0;
        this.logDebug('TTR retry counter reset');
    }

    // Show empty TTR table when no data is available
    showEmptyTTRTable() {
        try {
            const tableBody = document.getElementById('ttrSummaryTableBody');
            if (!tableBody) {
                this.logError('TTR Summary table body not found for empty state');
                return;
            }

            // Clear existing content
            tableBody.innerHTML = '';

            // Show empty message
            const emptyRow = document.createElement('tr');
            emptyRow.className = 'empty-table-message';
            emptyRow.innerHTML = '<td colspan="10">No data available. Please upload CSV files to view TTR summary report.</td>';
            tableBody.appendChild(emptyRow);

            this.logInfo('TTR Summary table updated with empty message');

            // Dispatch event to notify that TTR summary has been cleared
            document.dispatchEvent(new CustomEvent('ttrSummaryCleared', {
                detail: {
                    timestamp: Date.now()
                }
            }));

        } catch (error) {
            this.logError(`Error showing empty TTR table: ${error.message}`);
        }
    }

    // Force TTR Summary Report update with enhanced error handling
    forceTTRSummaryUpdate() {
        this.logInfo('Forcing TTR Summary Report update...');

        try {
            // Check if required components are available
            if (!window.fileHandler) {
                this.logError('FileHandler not available for TTR update');
                return false;
            }

            if (!window.fileHandler.processedData || window.fileHandler.processedData.length === 0) {
                this.logWarn('No processed data available for TTR update');
                return false;
            }

            // Force update
            this.updateTTRSummaryReport();
            this.logInfo('TTR Summary Report force update completed');
            return true;
        } catch (error) {
            this.logError(`Error in force TTR update: ${error.message}`);
            return false;
        }
    }

    // Check if a file has already been processed
    isFileProcessed(file) {
        const fileId = this.generateFileId(file);
        return this.processedFiles.has(fileId);
    }

    // Mark a file as processed
    markFileAsProcessed(file) {
        const fileId = this.generateFileId(file);
        this.processedFiles.add(fileId);
        this.logDebug(`File marked as processed: ${file.name} (ID: ${fileId})`);
        return fileId;
    }

    // Remove file data (serial numbers and high-value transactions)
    removeFileData(fileId, serialNumbers = null) {
        this.logDebug(`Removing file data for file ID: ${fileId}`);

        // Remove high-value transactions for this file and capture removal details
        const highValueRemoval = this.removeFileHighValueTransactions(fileId);
        const highValueRemovalUSD = this.removeFileHighValueTransactionsUSD(fileId);

        // Remove serial numbers if provided
        if (serialNumbers && serialNumbers.hoc && serialNumbers.ibd) {
            this.removeSerialNumbers(serialNumbers);
        }

        // Remove from processed files set
        this.processedFiles.delete(fileId);

        // CRITICAL FIX: Force currency metrics recalculation after file removal
        // This ensures currency breakdown reflects the removal immediately
        if (this.rawData && this.rawData.length > 0) {
            this.logDebug('Triggering currency metrics recalculation after file removal');
            setTimeout(() => {
                this.recalculateCurrencyMetrics();
                this.updateCurrencyBreakdown();
            }, 50);
        }

        this.logDebug(`File data removal completed for file ID: ${fileId}`);

        // Return removal details for logging and notifications
        return {
            fileId: fileId,
            highValueRemoval: highValueRemoval,
            highValueRemovalUSD: highValueRemovalUSD,
            serialNumbersRemoved: serialNumbers ? {
                hoc: serialNumbers.hoc.size,
                ibd: serialNumbers.ibd.size
            } : null
        };
    }

    // Remove serial numbers associated with a file
    removeSerialNumbers(serialNumbers) {
        if (!serialNumbers || !serialNumbers.hoc || !serialNumbers.ibd) {
            this.logWarn('Invalid serial numbers provided for removal');
            return;
        }

        // Log the removal operation
        this.logDebug(`Removing serial numbers - HOC: ${serialNumbers.hoc.size}, IBD: ${serialNumbers.ibd.size}`);

        // Store the counts before removal for logging
        const beforeHocCount = this.hocSerialNumbers.size;
        const beforeIbdCount = this.ibdSerialNumbers.size;
        const beforeTotalCount = this.allSerialNumbers.size;

        // Remove HOC serial numbers
        let hocRemoved = 0;
        serialNumbers.hoc.forEach(serialNo => {
            if (this.hocSerialNumbers.has(serialNo)) {
                this.hocSerialNumbers.delete(serialNo);
                hocRemoved++;
            }
            // Only remove from allSerialNumbers if it's not also in IBD
            if (!this.ibdSerialNumbers.has(serialNo)) {
                this.allSerialNumbers.delete(serialNo);
            }
        });

        // Remove IBD serial numbers
        let ibdRemoved = 0;
        serialNumbers.ibd.forEach(serialNo => {
            if (this.ibdSerialNumbers.has(serialNo)) {
                this.ibdSerialNumbers.delete(serialNo);
                ibdRemoved++;
            }
            // Only remove from allSerialNumbers if it's not also in HOC
            if (!this.hocSerialNumbers.has(serialNo)) {
                this.allSerialNumbers.delete(serialNo);
            }
        });

        // Update the unique serial counts in the metrics
        this.summaryMetrics.hocUniqueSerialCount = this.hocSerialNumbers.size;
        this.summaryMetrics.ibdUniqueSerialCount = this.ibdSerialNumbers.size;
        this.summaryMetrics.totalUniqueSerialCount = this.allSerialNumbers.size;

        this.logDebug(`Serial numbers removed - HOC: ${hocRemoved}, IBD: ${ibdRemoved}`);
        this.logDebug(`Updated unique serial counts - Before: HOC: ${beforeHocCount}, IBD: ${beforeIbdCount}, Total: ${beforeTotalCount}`);
        this.logDebug(`Updated unique serial counts - After: HOC: ${this.summaryMetrics.hocUniqueSerialCount}, IBD: ${this.summaryMetrics.ibdUniqueSerialCount}, Total: ${this.summaryMetrics.totalUniqueSerialCount}`);

        // Update the UI with the updated metrics
        this.updateUI();


    }

    // Get all processed file IDs
    getProcessedFileIds() {
        return Array.from(this.processedFiles);
    }

    // Get count of processed files
    getProcessedFilesCount() {
        return this.processedFiles.size;
    }

    // Update serial counts directly
    updateSerialCounts(hocCount, ibdCount, totalCount, wuCount = null) {
        this.summaryMetrics.hocUniqueSerialCount = hocCount;
        this.summaryMetrics.ibdUniqueSerialCount = ibdCount;
        this.summaryMetrics.totalUniqueSerialCount = totalCount;

        // Update WU count if provided, otherwise use current WU serial numbers size
        if (wuCount !== null) {
            this.summaryMetrics.wuUniqueSerialCount = wuCount;
        } else if (this.wuSerialNumbers) {
            this.summaryMetrics.wuUniqueSerialCount = this.wuSerialNumbers.size;
        }

        this.logDebug(`Updated unique serial counts - HOC: ${hocCount}, IBD: ${ibdCount}, WU: ${this.summaryMetrics.wuUniqueSerialCount}, Total: ${totalCount}`);

        // Update the UI with the updated metrics
        this.updateUI();


    }

    // Check if all required DOM elements exist
    checkRequiredDOMElements() {
        const requiredElements = [
            'highValueTransactionCount',
            'highValueBreakdown',
            'breakdownToggle',
            'breakdownContent',
            'highValueFileList'
        ];

        const missingElements = [];
        const foundElements = [];

        requiredElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                foundElements.push(id);
            } else {
                missingElements.push(id);
            }
        });

        this.logDebug('DOM Elements Check:', {
            found: foundElements.length,
            missing: missingElements.length,
            foundElements: foundElements,
            missingElements: missingElements
        });

        return {
            allFound: missingElements.length === 0,
            found: foundElements,
            missing: missingElements
        };
    }

    // Fix transaction count without resetting accumulated metrics
    fixTransactionCount(totalTransactions, metricsUpdate = null) {
        // If we have specific metrics updates, apply them
        if (metricsUpdate) {
            // Log the fix operation
            this.logDebug(`Fixing transaction count to ${totalTransactions} with metric updates`);

            // Update the transaction count
            this.summaryMetrics.totalTransactions = totalTransactions;

            // Apply any other metric updates if provided
            if (metricsUpdate.totalAmount !== undefined) {
                this.summaryMetrics.totalAmount = metricsUpdate.totalAmount;
            }
            if (metricsUpdate.hocCount !== undefined) {
                this.summaryMetrics.hocCount = metricsUpdate.hocCount;
            }
            if (metricsUpdate.ibdCount !== undefined) {
                this.summaryMetrics.ibdCount = metricsUpdate.ibdCount;
            }
            if (metricsUpdate.hocUniqueSerialCount !== undefined) {
                this.summaryMetrics.hocUniqueSerialCount = metricsUpdate.hocUniqueSerialCount;
            }
            if (metricsUpdate.ibdUniqueSerialCount !== undefined) {
                this.summaryMetrics.ibdUniqueSerialCount = metricsUpdate.ibdUniqueSerialCount;
            }
            if (metricsUpdate.totalUniqueSerialCount !== undefined) {
                this.summaryMetrics.totalUniqueSerialCount = metricsUpdate.totalUniqueSerialCount;
            }
        } else {
            // Just fix the transaction count
            this.logDebug(`Fixing transaction count to ${totalTransactions}`);
            this.summaryMetrics.totalTransactions = totalTransactions;
        }

        // Update the UI with the fixed metrics
        this.updateUI();



        // Log the fixed metrics
        this.logDebug(`Transaction count fixed. Current count: ${this.summaryMetrics.totalTransactions}`);
    }

    // Display transactions in the table instead of popup alerts
    displayTransactionsInTable(transactions, title, currency = 'all') {
        const tableContainer = document.getElementById('highValueTransactionDetails');
        const tableTitle = document.getElementById('transactionDetailsTitle');
        const tableBody = document.getElementById('highValueTransactionTableBody');
        const summaryContainer = document.getElementById('transactionSummary');

        if (!tableContainer || !tableTitle || !tableBody || !summaryContainer) {
            this.logError('Transaction details table elements not found');
            return;
        }

        // Show the table container
        tableContainer.style.display = 'block';

        // Update title
        tableTitle.textContent = title;

        // Clear existing table content
        tableBody.innerHTML = '';

        // Sort transactions by amount (descending) by default
        const sortedTransactions = [...transactions].sort((a, b) => b.TRANSACTION_AMOUNT_NUMERIC - a.TRANSACTION_AMOUNT_NUMERIC);

        // Populate table rows
        sortedTransactions.forEach(transaction => {
            const row = document.createElement('tr');

            // Get transaction data with fallbacks - using correct field names
            const date = transaction.TRANSACTION_DATE || 'N/A';
            const customerName = transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || transaction.PARTICIPANT_NAME || 'Unknown';
            const conductorId = transaction.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A';
            const counterparty = transaction.PARTICIPANT_NAME_COUNTERPARTY || transaction.COUNTERPARTY_NAME || transaction.COUNTERPARTY || 'N/A';
            const counterpartyId = transaction.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A';
            const amount = transaction.TRANSACTION_AMOUNT_NUMERIC || 0;
            const role = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || 'N/A';
            const transactionCurrency = transaction.TRANSACTION_CURRENCY || currency;
            const reportType = transaction.REPORTTYPE || transaction.REPORT_TYPE || 'N/A';
            const fileSource = transaction.fileName || transaction.FILE_SOURCE || 'N/A';

            // Format amount with proper styling
            const formattedAmount = this.formatCurrencySpecific(amount, transactionCurrency);
            const amountClass = transactionCurrency === 'USD' ? 'usd' : 'mmk';

            row.innerHTML = `
                <td class="date-cell">${date}</td>
                <td>${customerName}</td>
                <td>${conductorId}</td>
                <td>${counterparty}</td>
                <td>${counterpartyId}</td>
                <td class="amount-cell ${amountClass}">${formattedAmount}</td>
                <td>${role}</td>
                <td class="currency-cell">
                    <span class="currency-badge ${transactionCurrency.toLowerCase()}">${transactionCurrency}</span>
                </td>
                <td>${reportType}</td>
                <td>${fileSource}</td>
            `;

            tableBody.appendChild(row);
        });

        // Update summary statistics
        this.updateTransactionSummary(transactions, currency);

        // Set up table controls
        this.setupTableControls(transactions, currency);

        // Scroll to table
        tableContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });

        this.logInfo(`Displayed ${transactions.length} transactions in table`);
    }

    // Update transaction summary statistics
    updateTransactionSummary(transactions, currency) {
        const summaryContainer = document.getElementById('transactionSummary');
        const totalCountElement = document.getElementById('summaryTotalCount');
        const totalAmountElement = document.getElementById('summaryTotalAmount');
        const avgAmountElement = document.getElementById('summaryAvgAmount');
        const uniqueCustomersElement = document.getElementById('summaryUniqueCustomers');

        if (!summaryContainer || !totalCountElement || !totalAmountElement || !avgAmountElement || !uniqueCustomersElement) {
            return;
        }

        // Calculate statistics
        const totalCount = transactions.length;
        const totalAmount = transactions.reduce((sum, t) => sum + (t.TRANSACTION_AMOUNT_NUMERIC || 0), 0);
        const avgAmount = totalCount > 0 ? totalAmount / totalCount : 0;

        // Count unique customers
        const uniqueCustomers = new Set();
        transactions.forEach(t => {
            const customer = t.CUSTOMER_NAME || t.PARTICIPANT_NAME || 'Unknown';
            uniqueCustomers.add(customer);
        });

        // Update summary display
        totalCountElement.textContent = this.formatNumber(totalCount);
        totalAmountElement.textContent = this.formatCurrencySpecific(totalAmount, currency);
        avgAmountElement.textContent = this.formatCurrencySpecific(avgAmount, currency);
        uniqueCustomersElement.textContent = this.formatNumber(uniqueCustomers.size);

        // Show summary
        summaryContainer.style.display = 'block';
    }

    // Set up table controls (filtering and sorting)
    setupTableControls(originalTransactions, defaultCurrency) {
        const currencyFilter = document.getElementById('currencyFilter');
        const sortBy = document.getElementById('sortBy');
        const clearButton = document.getElementById('clearTransactionDetails');

        if (!currencyFilter || !sortBy || !clearButton) {
            return;
        }

        // Store original transactions for filtering/sorting
        this.currentTableTransactions = originalTransactions;
        this.currentTableCurrency = defaultCurrency;

        // Remove existing event listeners
        const newCurrencyFilter = currencyFilter.cloneNode(true);
        const newSortBy = sortBy.cloneNode(true);
        const newClearButton = clearButton.cloneNode(true);

        currencyFilter.parentNode.replaceChild(newCurrencyFilter, currencyFilter);
        sortBy.parentNode.replaceChild(newSortBy, sortBy);
        clearButton.parentNode.replaceChild(newClearButton, clearButton);

        // Set up currency filter
        newCurrencyFilter.addEventListener('change', () => {
            this.filterAndSortTable();
        });

        // Set up sort control
        newSortBy.addEventListener('change', () => {
            this.filterAndSortTable();
        });

        // Set up clear button
        newClearButton.addEventListener('click', () => {
            this.clearTransactionTable();
        });

        // Set default currency filter if specific currency
        if (defaultCurrency !== 'all') {
            newCurrencyFilter.value = defaultCurrency;
        }
    }

    // Filter and sort table based on current controls
    filterAndSortTable() {
        const currencyFilter = document.getElementById('currencyFilter');
        const sortBy = document.getElementById('sortBy');

        if (!currencyFilter || !sortBy || !this.currentTableTransactions) {
            return;
        }

        let filteredTransactions = [...this.currentTableTransactions];

        // Apply currency filter
        const selectedCurrency = currencyFilter.value;
        if (selectedCurrency !== 'all') {
            filteredTransactions = filteredTransactions.filter(t =>
                (t.TRANSACTION_CURRENCY || this.currentTableCurrency) === selectedCurrency
            );
        }

        // Apply sorting
        const sortOption = sortBy.value;
        switch (sortOption) {
            case 'amount-desc':
                filteredTransactions.sort((a, b) => (b.TRANSACTION_AMOUNT_NUMERIC || 0) - (a.TRANSACTION_AMOUNT_NUMERIC || 0));
                break;
            case 'amount-asc':
                filteredTransactions.sort((a, b) => (a.TRANSACTION_AMOUNT_NUMERIC || 0) - (b.TRANSACTION_AMOUNT_NUMERIC || 0));
                break;
            case 'date-desc':
                filteredTransactions.sort((a, b) => new Date(b.TRANSACTION_DATE || 0) - new Date(a.TRANSACTION_DATE || 0));
                break;
            case 'date-asc':
                filteredTransactions.sort((a, b) => new Date(a.TRANSACTION_DATE || 0) - new Date(b.TRANSACTION_DATE || 0));
                break;
            case 'customer':
                filteredTransactions.sort((a, b) => {
                    const nameA = (a.CUSTOMER_NAME || a.PARTICIPANT_NAME || 'Unknown').toLowerCase();
                    const nameB = (b.CUSTOMER_NAME || b.PARTICIPANT_NAME || 'Unknown').toLowerCase();
                    return nameA.localeCompare(nameB);
                });
                break;
        }

        // Update table with filtered/sorted data
        this.updateTableRows(filteredTransactions);
        this.updateTransactionSummary(filteredTransactions, selectedCurrency === 'all' ? this.currentTableCurrency : selectedCurrency);
    }

    // Update table rows with new data
    updateTableRows(transactions) {
        const tableBody = document.getElementById('highValueTransactionTableBody');
        if (!tableBody) {
            return;
        }

        // Clear existing rows
        tableBody.innerHTML = '';

        if (transactions.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.className = 'empty-table-message';
            emptyRow.innerHTML = '<td colspan="10">No transactions match the current filter criteria.</td>';
            tableBody.appendChild(emptyRow);
            return;
        }

        // Add filtered transactions
        transactions.forEach(transaction => {
            const row = document.createElement('tr');

            const date = transaction.TRANSACTION_DATE || 'N/A';
            const customerName = transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || transaction.PARTICIPANT_NAME || 'Unknown';
            const conductorId = transaction.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A';
            const counterparty = transaction.PARTICIPANT_NAME_COUNTERPARTY || transaction.COUNTERPARTY_NAME || transaction.COUNTERPARTY || 'N/A';
            const counterpartyId = transaction.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A';
            const amount = transaction.TRANSACTION_AMOUNT_NUMERIC || 0;
            const role = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || 'N/A';
            const transactionCurrency = transaction.TRANSACTION_CURRENCY || this.currentTableCurrency;
            const reportType = transaction.REPORTTYPE || transaction.REPORT_TYPE || 'N/A';
            const fileSource = transaction.fileName || transaction.FILE_SOURCE || 'N/A';

            const formattedAmount = this.formatCurrencySpecific(amount, transactionCurrency);
            const amountClass = transactionCurrency === 'USD' ? 'usd' : 'mmk';

            row.innerHTML = `
                <td class="date-cell">${date}</td>
                <td>${customerName}</td>
                <td>${conductorId}</td>
                <td>${counterparty}</td>
                <td>${counterpartyId}</td>
                <td class="amount-cell ${amountClass}">${formattedAmount}</td>
                <td>${role}</td>
                <td class="currency-cell">
                    <span class="currency-badge ${transactionCurrency.toLowerCase()}">${transactionCurrency}</span>
                </td>
                <td>${reportType}</td>
                <td>${fileSource}</td>
            `;

            tableBody.appendChild(row);
        });
    }

    // Clear the transaction table
    clearTransactionTable() {
        const tableContainer = document.getElementById('highValueTransactionDetails');
        const tableBody = document.getElementById('highValueTransactionTableBody');
        const summaryContainer = document.getElementById('transactionSummary');

        if (tableContainer) {
            tableContainer.style.display = 'none';
        }

        if (tableBody) {
            tableBody.innerHTML = '<tr class="empty-table-message"><td colspan="7">Click on a high-value transaction card above to view detailed transaction information.</td></tr>';
        }

        if (summaryContainer) {
            summaryContainer.style.display = 'none';
        }

        // Clear stored data
        this.currentTableTransactions = null;
        this.currentTableCurrency = null;

        // Remove selected state from cards
        this.clearCardSelection();

        this.logInfo('Transaction table cleared');
    }

    // Clear card selection styling
    clearCardSelection() {
        const mmkCard = document.querySelector('.high-value-card');
        const usdCard = document.querySelector('.high-value-card-usd');

        if (mmkCard) {
            mmkCard.classList.remove('selected');
        }
        if (usdCard) {
            usdCard.classList.remove('selected');
        }
    }

    // Show all high-value transactions for a specific currency
    showAllHighValueTransactions(currency) {
        let allTransactions = [];
        let title = '';

        if (currency === 'MMK') {
            // Collect all MMK high-value transactions
            this.fileHighValueTransactions.forEach((transactions, fileId) => {
                transactions.forEach(transaction => {
                    // Ensure amount is a number for sorting and display
                    const amount = typeof transaction.TRANSACTION_AMOUNT === 'string' ?
                        parseFloat(transaction.TRANSACTION_AMOUNT) || 0 :
                        (typeof transaction.TRANSACTION_AMOUNT === 'number' ? transaction.TRANSACTION_AMOUNT : 0);

                    allTransactions.push({
                        ...transaction,
                        TRANSACTION_AMOUNT_NUMERIC: amount,
                        fileId: fileId
                    });
                });
            });
            title = 'All High-Value MMK Transactions (≥1B MMK)';
        } else if (currency === 'USD') {
            // Collect all USD high-value transactions
            this.fileHighValueTransactionsUSD.forEach((transactions, fileId) => {
                transactions.forEach(transaction => {
                    // Ensure amount is a number for sorting and display
                    const amount = typeof transaction.TRANSACTION_AMOUNT === 'string' ?
                        parseFloat(transaction.TRANSACTION_AMOUNT) || 0 :
                        (typeof transaction.TRANSACTION_AMOUNT === 'number' ? transaction.TRANSACTION_AMOUNT : 0);

                    allTransactions.push({
                        ...transaction,
                        TRANSACTION_AMOUNT_NUMERIC: amount,
                        fileId: fileId
                    });
                });
            });
            title = 'All High-Value USD Transactions (≥10K USD)';
        }

        if (allTransactions.length === 0) {
            if (window.app) {
                window.app.showNotification(`No high-value ${currency} transactions found`, 'info', 3000);
            }
            return;
        }

        // Display in table
        this.displayTransactionsInTable(allTransactions, title, currency);

        // Add selected styling to the clicked card
        this.clearCardSelection();
        const cardSelector = currency === 'MMK' ? '.high-value-card' : '.high-value-card-usd';
        const card = document.querySelector(cardSelector);
        if (card) {
            card.classList.add('selected');
        }
    }

    // Get all transactions for external modules (like customer analytics)
    getAllTransactions() {
        return this.rawData || [];
    }

    // Get filtered transactions for external modules
    getFilteredTransactions() {
        return this.filteredData || [];
    }

    // Get summary metrics for external modules
    getSummaryMetrics() {
        return this.summaryMetrics;
    }
}

// Create a global instance of the DataProcessor (with duplicate prevention)
if (!window.dataProcessor) {
    window.dataProcessor = new DataProcessor();
    console.log('DataProcessor instance created and assigned to window.dataProcessor');
} else {
    console.log('DataProcessor instance already exists, skipping creation');
}

// Expose currency calculation fix functions to global scope for debugging
window.fixCurrencyCalculations = () => {
    if (window.dataProcessor) {
        window.dataProcessor.fixCurrencyCalculations();
    } else {
        console.error('DataProcessor not initialized');
    }
};

window.testCurrencyCalculations = () => {
    if (window.dataProcessor) {
        return window.dataProcessor.testCurrencyCalculations();
    } else {
        console.error('DataProcessor not initialized');
        return false;
    }
};

window.validateCurrencyCalculations = (silent = false) => {
    if (window.dataProcessor) {
        return window.dataProcessor.validateAndFixCurrencyCalculations(silent);
    } else {
        console.error('DataProcessor not initialized');
        return 0;
    }
};

window.fixCurrencyNetAmounts = () => {
    if (window.dataProcessor) {
        return window.dataProcessor.fixCurrencyNetAmounts();
    } else {
        console.error('DataProcessor not initialized');
        return 0;
    }
};

window.diagnoseWUTransactions = () => {
    if (window.dataProcessor) {
        return window.dataProcessor.diagnoseWUTransactions();
    } else {
        console.error('DataProcessor not initialized');
        return null;
    }
};

window.fixWUTransactionProcessing = () => {
    if (window.dataProcessor) {
        return window.dataProcessor.fixWUTransactionProcessing();
    } else {
        console.error('DataProcessor not initialized');
        return false;
    }
};

console.log('Currency calculation fix functions exposed to global scope:');
console.log('- fixCurrencyCalculations(): Manually fix all HOC/IBD/WU currency calculations');
console.log('- testCurrencyCalculations(): Test if calculations are correct');
console.log('- validateCurrencyCalculations(): Validate and auto-fix any discrepancies');
console.log('- fixCurrencyNetAmounts(): Fix currency net amounts (credit - debit calculation)');
console.log('- diagnoseWUTransactions(): Diagnose WU transaction processing issues');
console.log('- fixWUTransactionProcessing(): Fix WU transaction processing from raw data');

// Initialize DataProcessor instance
if (typeof window !== 'undefined') {
    window.dataProcessor = new DataProcessor();
    console.log('DataProcessor initialized and available as window.dataProcessor');
} else {
    console.error('Window object not available - DataProcessor not initialized');
}

