/**
 * Application Constants
 * Centralized configuration for the Financial Transaction Dashboard V2.5.7 Production
 */

// Application Configuration
const APP_CONFIG = {
    VERSION: '2.5.7',
    NAME: 'Financial Transaction Dashboard',
    DEBUG_MODE: false, // Production mode - no debug logging

    // Performance Settings
    PERFORMANCE: {
        MAX_MEMORY_USAGE: 100 * 1024 * 1024, // 100MB
        GC_THRESHOLD: 0.8, // 80% memory usage triggers GC
        CHUNK_SIZE: 64 * 1024, // 64KB chunks for file reading
        BATCH_SIZE: 10000, // Records per batch
        SAMPLE_RATE: 0.001, // 0.1% sampling rate
        ANIMATION_FRAME_BUDGET: 16, // 16ms per animation frame
        WORKER_COUNT: 8, // Number of parallel workers
        WORKER_MEMORY_LIMIT: 25 * 1024 * 1024 // 25MB per worker
    },

    // File Processing
    FILE_PROCESSING: {
        MAX_FILE_SIZE: 3 * 1024 * 1024 * 1024, // 3GB
        LARGE_FILE_THRESHOLD: 50 * 1024 * 1024, // 50MB
        SUPPORTED_FORMATS: ['.csv'],
        MAX_FILES: 100,
        STREAMING_THRESHOLD: 50 * 1024 * 1024 // 50MB
    },

    // TTR Configuration
    TTR: {
        SINGLE_THRESHOLD: **********, // 1B MMK
        DAILY_THRESHOLD: 5000000000, // 5B MMK
        HIGH_VALUE_THRESHOLD: **********, // 1B MMK
        HIGH_VALUE_THRESHOLD_USD: 10000, // 10K USD
        STRUCTURING_WINDOW: 24, // hours
        STRUCTURING_THRESHOLD: 90, // percentage
        VELOCITY_COUNT: 10, // max transactions per hour
        BURST_WINDOW: 5, // minutes
        RISK_SCORE_THRESHOLDS: {
            HIGH: 80,
            MEDIUM: 50,
            LOW: 20
        }
    },

    // UI Configuration
    UI: {
        ROWS_PER_PAGE: 20,
        PROGRESS_UPDATE_INTERVAL: 100, // ms
        NOTIFICATION_DURATION: 3000, // ms
        CHART_ANIMATION_DURATION: 300, // ms
        DEBOUNCE_DELAY: 300, // ms
        TOOLTIP_DELAY: 500 // ms
    },

    // Security Settings
    SECURITY: {
        SANITIZE_HTML: true,
        VALIDATE_INPUT: true,
        LOG_SENSITIVE_DATA: false,
        MAX_LOG_ENTRIES: 1000,
        SENSITIVE_FIELDS: [
            'TRANSACTION_AMOUNT',
            'CUSTOMER_ID',
            'CUSTOMER_NAME',
            'ACCOUNT_NUMBER',
            'SERIAL_NO',
            'TRANSACTION_REF_NUMBER'
        ]
    },

    // Export Settings
    EXPORT: {
        MAX_RECORDS_MEMORY: 10000, // Use streaming above this
        CSV_DELIMITER: ',',
        DATE_FORMAT: 'YYYY-MM-DD',
        CURRENCY_FORMAT: 'en-US',
        EXCEL_SHEET_NAME: 'Transactions'
    }
};

// Field Mappings
const FIELD_MAPPINGS = {
    REQUIRED_FIELDS: [
        'REPORTTYPE',
        'TRANSACTION_AMOUNT',
        'TRANSACTION_CURRENCY',
        'ACCOUNT_HOLDER_ACCOUNT_ROLE',
        'TRANSACTION_DATE',
        'SERIAL_NUMBER',
        'SERIAL_NO',
        'CUSTOMER_NAME',
        'PARTICIPANT_NAME_CONDUCTOR',
        'PARTICIPANT_NAME_COUNTERPARTY',
        'PARTICIPANT_ID_NUMBER_CONDUCTOR',
        'PARTICIPANT_ID_NUMBER_COUNTERPARTY',
        'TRANSACTION_ID',
        'CUSTOMER_ID',
        'ACCOUNT_NUMBER',
        'TRANSACTION_TYPE',
        'CHANNEL',
        'LOCATION',
        'BUSINESS_TYPE'
    ],

    REPORT_TYPES: {
        HOC: 'HOC',
        IBD: 'IBD',
        WU: 'WU',
        IBU: 'IBU'
    },

    CURRENCIES: {
        MMK: 'MMK',
        USD: 'USD',
        SGD: 'SGD',
        EUR: 'EUR',
        JPY: 'JPY',
        CNY: 'CNY',
        THB: 'THB',
        INR: 'INR'
    },

    // Currency configuration with formatting rules
    CURRENCY_CONFIG: {
        MMK: {
            code: 'MMK',
            symbol: 'MMK',
            name: 'Myanmar Kyat',
            decimals: 0,
            highValueThreshold: **********, // 1 billion MMK
            useIntlFormat: false
        },
        USD: {
            code: 'USD',
            symbol: '$',
            name: 'US Dollar',
            decimals: 2,
            highValueThreshold: 10000, // 10,000 USD
            useIntlFormat: true
        },
        SGD: {
            code: 'SGD',
            symbol: 'S$',
            name: 'Singapore Dollar',
            decimals: 2,
            highValueThreshold: 15000, // 15,000 SGD
            useIntlFormat: true
        },
        EUR: {
            code: 'EUR',
            symbol: '€',
            name: 'Euro',
            decimals: 2,
            highValueThreshold: 9000, // 9,000 EUR
            useIntlFormat: true
        },
        JPY: {
            code: 'JPY',
            symbol: '¥',
            name: 'Japanese Yen',
            decimals: 0,
            highValueThreshold: 1100000, // 1,100,000 JPY
            useIntlFormat: true
        },
        CNY: {
            code: 'CNY',
            symbol: '¥',
            name: 'Chinese Yuan',
            decimals: 2,
            highValueThreshold: 70000, // 70,000 CNY
            useIntlFormat: true
        },
        THB: {
            code: 'THB',
            symbol: '฿',
            name: 'Thai Baht',
            decimals: 2,
            highValueThreshold: 350000, // 350,000 THB
            useIntlFormat: true
        },
        INR: {
            code: 'INR',
            symbol: '₹',
            name: 'Indian Rupee',
            decimals: 2,
            highValueThreshold: 800000, // 800,000 INR
            useIntlFormat: true
        }
    },

    // Supported currencies list for validation
    SUPPORTED_CURRENCIES: ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'],

    TRANSACTION_ROLES: {
        CREDIT: 'Credit',
        DEBIT: 'Debit'
    }
};

// Error Messages
const ERROR_MESSAGES = {
    FILE_TOO_LARGE: 'File size exceeds maximum limit',
    UNSUPPORTED_FORMAT: 'Unsupported file format',
    PROCESSING_FAILED: 'File processing failed',
    MEMORY_LIMIT_EXCEEDED: 'Memory limit exceeded',
    WORKER_INITIALIZATION_FAILED: 'Worker initialization failed',
    INVALID_DATA: 'Invalid data format',
    NETWORK_ERROR: 'Network connection error',
    EXPORT_FAILED: 'Export operation failed',
    VALIDATION_FAILED: 'Data validation failed'
};

// Success Messages
const SUCCESS_MESSAGES = {
    FILE_PROCESSED: 'File processed successfully',
    DATA_EXPORTED: 'Data exported successfully',
    SETTINGS_SAVED: 'Settings saved successfully',
    ALERT_DISMISSED: 'Alert dismissed successfully'
};

// CSS Classes
const CSS_CLASSES = {
    PRIORITY: {
        HIGH: 'priority-high',
        MEDIUM: 'priority-medium',
        LOW: 'priority-low'
    },
    STATUS: {
        SUCCESS: 'status-success',
        ERROR: 'status-error',
        WARNING: 'status-warning',
        INFO: 'status-info'
    },
    LOADING: 'loading',
    HIDDEN: 'hidden',
    ACTIVE: 'active',
    DISABLED: 'disabled'
};

// Browser Compatibility
const BROWSER_SUPPORT = {
    WEB_WORKERS: typeof Worker !== 'undefined',
    PERFORMANCE_API: typeof performance !== 'undefined' && typeof performance.memory !== 'undefined',
    FILE_API: typeof File !== 'undefined' && typeof FileReader !== 'undefined',
    BLOB_API: typeof Blob !== 'undefined',
    URL_API: typeof URL !== 'undefined' && typeof URL.createObjectURL !== 'undefined',
    INTL_API: typeof Intl !== 'undefined',
    PROMISE_API: typeof Promise !== 'undefined',
    FETCH_API: typeof fetch !== 'undefined'
};

// Export constants for use in other modules
if (typeof window !== 'undefined') {
    window.APP_CONFIG = APP_CONFIG;
    window.FIELD_MAPPINGS = FIELD_MAPPINGS;
    window.ERROR_MESSAGES = ERROR_MESSAGES;
    window.SUCCESS_MESSAGES = SUCCESS_MESSAGES;
    window.CSS_CLASSES = CSS_CLASSES;
    window.BROWSER_SUPPORT = BROWSER_SUPPORT;
}

// For Web Workers
if (typeof self !== 'undefined' && typeof window === 'undefined') {
    self.APP_CONFIG = APP_CONFIG;
    self.FIELD_MAPPINGS = FIELD_MAPPINGS;
    self.ERROR_MESSAGES = ERROR_MESSAGES;
    self.SUCCESS_MESSAGES = SUCCESS_MESSAGES;
    self.CSS_CLASSES = CSS_CLASSES;
    self.BROWSER_SUPPORT = BROWSER_SUPPORT;
}
