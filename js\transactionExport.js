/**
 * Transaction Export Module
 * Handles Excel export functionality for Transaction Breakdown data
 */

class TransactionExport {
    constructor() {
        this.exportFormats = ['excel'];
        this.isExporting = false;
    }

    // Main export function for transaction breakdown data
    async exportTransactionBreakdownToExcel(filename = null) {
        if (this.isExporting) {
            console.warn('Export already in progress');
            return;
        }

        try {
            this.isExporting = true;
            this.showLoadingIndicator();

            // Check if XLSX library is available
            if (typeof XLSX === 'undefined') {
                throw new Error('XLSX library not available. Cannot export to Excel format.');
            }

            // Get transaction data from DataProcessor
            const transactionData = this.getTransactionBreakdownData();

            if (!transactionData || !this.hasTransactionData(transactionData)) {
                throw new Error('No transaction data available to export');
            }

            // Create Excel workbook with multiple sheets
            const workbook = this.createTransactionBreakdownWorkbook(transactionData);
            
            // Generate filename with timestamp
            const finalFilename = filename || this.generateFilename();

            // Write and download the Excel file
            XLSX.writeFile(workbook, finalFilename);

            // Show success notification
            this.showSuccessNotification(finalFilename, this.getTransactionCount(transactionData));

            return {
                success: true,
                filename: finalFilename,
                recordCount: this.getTransactionCount(transactionData),
                format: 'Excel'
            };

        } catch (error) {
            console.error('Transaction export error:', error);
            this.showErrorNotification(error.message);
            throw error;
        } finally {
            this.isExporting = false;
            this.hideLoadingIndicator();
        }
    }

    // Get comprehensive transaction breakdown data
    getTransactionBreakdownData() {
        if (!window.dataProcessor) {
            throw new Error('DataProcessor not available');
        }

        const summaryMetrics = window.dataProcessor.getSummaryMetrics();
        const rawData = window.dataProcessor.rawData || [];

        console.log('Transaction Export: Getting data...', {
            summaryMetrics: summaryMetrics,
            rawDataLength: rawData.length
        });

        return {
            summaryMetrics: summaryMetrics,
            rawTransactions: rawData,
            hocTransactions: this.filterTransactionsByType(rawData, 'HOC'),
            ibdTransactions: this.filterTransactionsByType(rawData, 'IBD'),
            wuTransactions: this.filterTransactionsByType(rawData, 'WU'),
            currencyBreakdown: this.getCurrencyBreakdownData(summaryMetrics),
            exportTimestamp: new Date().toISOString()
        };
    }

    // Filter transactions by report type
    filterTransactionsByType(transactions, reportType) {
        const filtered = transactions.filter(transaction =>
            (transaction.REPORTTYPE || transaction.REPORT_TYPE) === reportType
        );
        console.log(`Transaction Export: Filtered ${reportType} transactions:`, filtered.length);
        return filtered;
    }

    // Get currency breakdown data
    getCurrencyBreakdownData(summaryMetrics) {
        const currencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        return currencies.map(currency => ({
            currency: currency,
            creditAmount: summaryMetrics.currencyCreditAmounts?.[currency] || 0,
            debitAmount: summaryMetrics.currencyDebitAmounts?.[currency] || 0,
            totalAmount: (summaryMetrics.currencyCreditAmounts?.[currency] || 0) + 
                        (summaryMetrics.currencyDebitAmounts?.[currency] || 0),
            transactionCount: summaryMetrics.currencyCounts?.[currency] || 0
        }));
    }

    // Check if there's any transaction data to export
    hasTransactionData(data) {
        return data.rawTransactions && data.rawTransactions.length > 0;
    }

    // Get total transaction count
    getTransactionCount(data) {
        return data.rawTransactions ? data.rawTransactions.length : 0;
    }

    // Create Excel workbook with multiple worksheets
    createTransactionBreakdownWorkbook(data) {
        const workbook = XLSX.utils.book_new();

        // Add Summary worksheet
        const summarySheet = this.createSummaryWorksheet(data);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

        // Add Dashboard Report worksheet (replicates UI structure)
        const dashboardSheet = this.createDashboardReportWorksheet(data);
        XLSX.utils.book_append_sheet(workbook, dashboardSheet, 'Dashboard Report');

        // Add HOC Transactions worksheet
        if (data.hocTransactions.length > 0) {
            const hocSheet = this.createTransactionWorksheet(data.hocTransactions, 'HOC');
            XLSX.utils.book_append_sheet(workbook, hocSheet, 'HOC Transactions');
        }

        // Add IBD Transactions worksheet
        if (data.ibdTransactions.length > 0) {
            const ibdSheet = this.createTransactionWorksheet(data.ibdTransactions, 'IBD');
            XLSX.utils.book_append_sheet(workbook, ibdSheet, 'IBD Transactions');
        }

        // Add WU Transactions worksheet
        if (data.wuTransactions.length > 0) {
            const wuSheet = this.createTransactionWorksheet(data.wuTransactions, 'WU');
            XLSX.utils.book_append_sheet(workbook, wuSheet, 'WU Transactions');
        }

        // Add Currency Breakdown worksheet
        const currencySheet = this.createCurrencyBreakdownWorksheet(data.currencyBreakdown);
        XLSX.utils.book_append_sheet(workbook, currencySheet, 'Currency Breakdown');

        return workbook;
    }

    // Create summary worksheet
    createSummaryWorksheet(data) {
        const summaryData = [
            ['Financial Transaction Dashboard - Export Summary'],
            [''],
            ['Export Date:', new Date().toLocaleString()],
            ['Total Transactions:', data.rawTransactions.length],
            [''],
            ['Transaction Breakdown by Type:'],
            ['HOC Transactions:', data.hocTransactions.length],
            ['IBD Transactions:', data.ibdTransactions.length],
            ['WU Transactions:', data.wuTransactions.length],
            [''],
            ['Summary Metrics:'],
            ['Total Amount (MMK):', this.formatCurrency(data.summaryMetrics.currencyAmounts?.MMK || 0, 'MMK')],
            ['Total Amount (USD):', this.formatCurrency(data.summaryMetrics.currencyAmounts?.USD || 0, 'USD')],
            ['High-Value Transactions (≥1B MMK):', data.summaryMetrics.highValueTransactionCount || 0],
            ['High-Value Transactions (≥10K USD):', data.summaryMetrics.highValueTransactionCountUSD || 0],
            [''],
            ['Credit/Debit Breakdown:'],
            ['Total Credit Amount (MMK):', this.formatCurrency(data.summaryMetrics.currencyCreditAmounts?.MMK || 0, 'MMK')],
            ['Total Debit Amount (MMK):', this.formatCurrency(data.summaryMetrics.currencyDebitAmounts?.MMK || 0, 'MMK')],
            ['Total Credit Amount (USD):', this.formatCurrency(data.summaryMetrics.currencyCreditAmounts?.USD || 0, 'USD')],
            ['Total Debit Amount (USD):', this.formatCurrency(data.summaryMetrics.currencyDebitAmounts?.USD || 0, 'USD')],
            [''],
            ['Note: This report includes all transaction data currently loaded in the dashboard.']
        ];

        return XLSX.utils.aoa_to_sheet(summaryData);
    }

    // Create Dashboard Report worksheet that replicates the UI structure
    createDashboardReportWorksheet(data) {
        const metrics = data.summaryMetrics;

        console.log('Creating Dashboard Report worksheet with metrics:', {
            hocCount: metrics.hocCount,
            ibdCount: metrics.ibdCount,
            wuCount: metrics.wuCount,
            hocCreditAmountMMK: metrics.hocCreditAmountMMK,
            hocDebitAmountMMK: metrics.hocDebitAmountMMK
        });

        const dashboardData = [
            ['TRANSACTION BREAKDOWN DASHBOARD REPORT'],
            ['Generated on:', new Date().toLocaleString()],
            [''],
            ['This report replicates the exact data structure displayed in the Transaction Breakdown section of the dashboard UI.'],
            [''],

            // HOC Transactions Section
            ['=== HOC TRANSACTIONS ==='],
            [''],
            ['Total Count:', metrics.hocCount || 0],
            ['Unique Serial Count:', metrics.hocUniqueSerialCount || 0],
            ['Total Amount MMK:', this.formatCurrency(metrics.hocAmountMMK || 0, 'MMK')],
            ['Total Amount USD:', this.formatCurrency(metrics.hocAmountUSD || 0, 'USD')],
            [''],
            ['Credit Breakdown:'],
            ['  Count:', metrics.hocCreditCount || 0],
            ['  Amount MMK:', this.formatCurrency(metrics.hocCreditAmountMMK || 0, 'MMK')],
            ['  Amount USD:', this.formatCurrency(metrics.hocCreditAmountUSD || 0, 'USD')],
            ['  Amount CNY:', this.formatCurrency(metrics.hocCreditAmountCNY || 0, 'CNY')],
            ['  Amount EUR:', this.formatCurrency(metrics.hocCreditAmountEUR || 0, 'EUR')],
            ['  Amount INR:', this.formatCurrency(metrics.hocCreditAmountINR || 0, 'INR')],
            ['  Amount JPY:', this.formatCurrency(metrics.hocCreditAmountJPY || 0, 'JPY')],
            ['  Amount SGD:', this.formatCurrency(metrics.hocCreditAmountSGD || 0, 'SGD')],
            ['  Amount THB:', this.formatCurrency(metrics.hocCreditAmountTHB || 0, 'THB')],
            [''],
            ['Debit Breakdown:'],
            ['  Count:', metrics.hocDebitCount || 0],
            ['  Amount MMK:', this.formatCurrency(metrics.hocDebitAmountMMK || 0, 'MMK')],
            ['  Amount USD:', this.formatCurrency(metrics.hocDebitAmountUSD || 0, 'USD')],
            ['  Amount CNY:', this.formatCurrency(metrics.hocDebitAmountCNY || 0, 'CNY')],
            ['  Amount EUR:', this.formatCurrency(metrics.hocDebitAmountEUR || 0, 'EUR')],
            ['  Amount INR:', this.formatCurrency(metrics.hocDebitAmountINR || 0, 'INR')],
            ['  Amount JPY:', this.formatCurrency(metrics.hocDebitAmountJPY || 0, 'JPY')],
            ['  Amount SGD:', this.formatCurrency(metrics.hocDebitAmountSGD || 0, 'SGD')],
            ['  Amount THB:', this.formatCurrency(metrics.hocDebitAmountTHB || 0, 'THB')],
            [''],
            [''],

            // IBD Transactions Section
            ['=== IBD TRANSACTIONS ==='],
            [''],
            ['Total Count:', metrics.ibdCount || 0],
            ['Unique Serial Count:', metrics.ibdUniqueSerialCount || 0],
            ['Total Amount MMK:', this.formatCurrency(metrics.ibdAmountMMK || 0, 'MMK')],
            ['Total Amount USD:', this.formatCurrency(metrics.ibdAmountUSD || 0, 'USD')],
            [''],
            ['Credit Breakdown:'],
            ['  Count:', metrics.ibdCreditCount || 0],
            ['  Amount MMK:', this.formatCurrency(metrics.ibdCreditAmountMMK || 0, 'MMK')],
            ['  Amount USD:', this.formatCurrency(metrics.ibdCreditAmountUSD || 0, 'USD')],
            ['  Amount CNY:', this.formatCurrency(metrics.ibdCreditAmountCNY || 0, 'CNY')],
            ['  Amount EUR:', this.formatCurrency(metrics.ibdCreditAmountEUR || 0, 'EUR')],
            ['  Amount INR:', this.formatCurrency(metrics.ibdCreditAmountINR || 0, 'INR')],
            ['  Amount JPY:', this.formatCurrency(metrics.ibdCreditAmountJPY || 0, 'JPY')],
            ['  Amount SGD:', this.formatCurrency(metrics.ibdCreditAmountSGD || 0, 'SGD')],
            ['  Amount THB:', this.formatCurrency(metrics.ibdCreditAmountTHB || 0, 'THB')],
            [''],
            ['Debit Breakdown:'],
            ['  Count:', metrics.ibdDebitCount || 0],
            ['  Amount MMK:', this.formatCurrency(metrics.ibdDebitAmountMMK || 0, 'MMK')],
            ['  Amount USD:', this.formatCurrency(metrics.ibdDebitAmountUSD || 0, 'USD')],
            ['  Amount CNY:', this.formatCurrency(metrics.ibdDebitAmountCNY || 0, 'CNY')],
            ['  Amount EUR:', this.formatCurrency(metrics.ibdDebitAmountEUR || 0, 'EUR')],
            ['  Amount INR:', this.formatCurrency(metrics.ibdDebitAmountINR || 0, 'INR')],
            ['  Amount JPY:', this.formatCurrency(metrics.ibdDebitAmountJPY || 0, 'JPY')],
            ['  Amount SGD:', this.formatCurrency(metrics.ibdDebitAmountSGD || 0, 'SGD')],
            ['  Amount THB:', this.formatCurrency(metrics.ibdDebitAmountTHB || 0, 'THB')],
            [''],
            [''],

            // WU Transactions Section
            ['=== WU TRANSACTIONS ==='],
            [''],
            ['Total Count:', metrics.wuCount || 0],
            ['Unique Serial Count:', metrics.wuUniqueSerialCount || 0],
            ['Total Amount MMK:', this.formatCurrency(metrics.wuAmountMMK || 0, 'MMK')],
            ['Total Amount USD:', this.formatCurrency(metrics.wuAmountUSD || 0, 'USD')],
            [''],
            ['Credit Breakdown:'],
            ['  Count:', metrics.wuCreditCount || 0],
            ['  Amount MMK:', this.formatCurrency(metrics.wuCreditAmountMMK || 0, 'MMK')],
            ['  Amount USD:', this.formatCurrency(metrics.wuCreditAmountUSD || 0, 'USD')],
            ['  Amount CNY:', this.formatCurrency(metrics.wuCreditAmountCNY || 0, 'CNY')],
            ['  Amount EUR:', this.formatCurrency(metrics.wuCreditAmountEUR || 0, 'EUR')],
            ['  Amount INR:', this.formatCurrency(metrics.wuCreditAmountINR || 0, 'INR')],
            ['  Amount JPY:', this.formatCurrency(metrics.wuCreditAmountJPY || 0, 'JPY')],
            ['  Amount SGD:', this.formatCurrency(metrics.wuCreditAmountSGD || 0, 'SGD')],
            ['  Amount THB:', this.formatCurrency(metrics.wuCreditAmountTHB || 0, 'THB')],
            [''],
            ['Debit Breakdown:'],
            ['  Count:', metrics.wuDebitCount || 0],
            ['  Amount MMK:', this.formatCurrency(metrics.wuDebitAmountMMK || 0, 'MMK')],
            ['  Amount USD:', this.formatCurrency(metrics.wuDebitAmountUSD || 0, 'USD')],
            ['  Amount CNY:', this.formatCurrency(metrics.wuDebitAmountCNY || 0, 'CNY')],
            ['  Amount EUR:', this.formatCurrency(metrics.wuDebitAmountEUR || 0, 'EUR')],
            ['  Amount INR:', this.formatCurrency(metrics.wuDebitAmountINR || 0, 'INR')],
            ['  Amount JPY:', this.formatCurrency(metrics.wuDebitAmountJPY || 0, 'JPY')],
            ['  Amount SGD:', this.formatCurrency(metrics.wuDebitAmountSGD || 0, 'SGD')],
            ['  Amount THB:', this.formatCurrency(metrics.wuDebitAmountTHB || 0, 'THB')],
            [''],
            ['=== SUMMARY ==='],
            [''],
            ['Total Transactions Processed:', (metrics.hocCount || 0) + (metrics.ibdCount || 0) + (metrics.wuCount || 0)],
            ['Total Unique Serial Numbers:', (metrics.hocUniqueSerialCount || 0) + (metrics.ibdUniqueSerialCount || 0) + (metrics.wuUniqueSerialCount || 0)],
            [''],
            ['Note: This report replicates the exact data structure displayed in the Transaction Breakdown section of the dashboard UI.']
        ];

        return XLSX.utils.aoa_to_sheet(dashboardData);
    }

    // Create transaction worksheet for specific type
    createTransactionWorksheet(transactions, reportType) {
        const headers = [
            'Transaction Date',
            'CONDUCTOR',
            'CONDUCTOR ID',
            'Counterparty',
            'COUNTERPARTY ID',
            'Transaction Amount',
            'ROLE',
            'Transaction Currency',
            'Report Type',
            'File Name',
            'File ID'
        ];

        const rows = transactions.map(transaction => [
            transaction.TRANSACTION_DATE || '',
            transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || '',
            transaction.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A',
            transaction.PARTICIPANT_NAME_COUNTERPARTY || transaction.COUNTERPARTY_NAME || '',
            transaction.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A',
            parseFloat(transaction.TRANSACTION_AMOUNT) || 0,
            transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || 'N/A',
            transaction.TRANSACTION_CURRENCY || '',
            transaction.REPORTTYPE || transaction.REPORT_TYPE || '',
            transaction.fileName || '',
            transaction.fileId || ''
        ]);

        return XLSX.utils.aoa_to_sheet([headers, ...rows]);
    }

    // Create currency breakdown worksheet
    createCurrencyBreakdownWorksheet(currencyData) {
        const headers = [
            'Currency Type',
            'Credit Amount',
            'Debit Amount',
            'Total Amount',
            'Transaction Count'
        ];

        const rows = currencyData.map(currency => [
            currency.currency,
            currency.creditAmount,
            currency.debitAmount,
            currency.totalAmount,
            currency.transactionCount
        ]);

        return XLSX.utils.aoa_to_sheet([headers, ...rows]);
    }

    // Generate filename with timestamp
    generateFilename() {
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0];
        const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
        return `Transaction_Breakdown_Export_${dateStr}_${timeStr}.xlsx`;
    }

    // Format currency values
    formatCurrency(amount, currency) {
        try {
            const numAmount = parseFloat(amount) || 0;
            return `${numAmount.toLocaleString()} ${currency}`;
        } catch (error) {
            console.warn('Error formatting currency:', error);
            return `0 ${currency}`;
        }
    }

    // Show loading indicator
    showLoadingIndicator() {
        const exportBtn = document.getElementById('exportTransactionBreakdownBtn');
        if (exportBtn) {
            exportBtn.disabled = true;
            exportBtn.innerHTML = '<span class="loading-spinner"></span>Exporting...';
        }
    }

    // Hide loading indicator
    hideLoadingIndicator() {
        const exportBtn = document.getElementById('exportTransactionBreakdownBtn');
        if (exportBtn) {
            exportBtn.disabled = false;
            exportBtn.innerHTML = '<span class="export-icon"></span>Export to Excel';
        }
    }

    // Show success notification
    showSuccessNotification(filename, recordCount) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(
                `Successfully exported ${recordCount} transactions to ${filename}`,
                'success',
                5000
            );
        }
    }

    // Show error notification
    showErrorNotification(message) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(
                `Export failed: ${message}`,
                'error',
                5000
            );
        }
    }
}

// Create global instance
if (!window.transactionExport) {
    window.transactionExport = new TransactionExport();
    console.log('TransactionExport module initialized');
}
