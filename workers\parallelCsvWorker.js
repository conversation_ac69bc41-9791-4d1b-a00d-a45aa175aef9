/**
 * Parallel CSV Processing Web Worker
 * Handles distributed CSV parsing and data processing for improved performance
 */

// Worker configuration
const WORKER_CONFIG = {
    batchSize: 10000,
    memoryLimit: 25 * 1024 * 1024, // 25MB per worker
    gcThreshold: 0.8,
    sampleRate: 0.001
};

// Worker state
let workerId = null;
let isProcessing = false;
let processedBatches = 0;

// Enhanced message handling with parallel processing support
self.onmessage = function(e) {
    try {
        console.log(`Worker ${workerId || 'uninitialized'} received message:`, e.data);

        // Validate message structure
        if (!e.data || typeof e.data !== 'object') {
            throw new Error('Invalid message format received');
        }

        const { action, data, workerId: msgWorkerId } = e.data;

        if (!action) {
            throw new Error('No action specified in message');
        }

        // Set worker ID if provided
        if (msgWorkerId !== undefined) {
            workerId = msgWorkerId;
        }

        console.log(`Worker ${workerId} processing action: ${action}`);

        switch (action) {
            case 'initWorker':
                initializeWorker(data);
                break;
            case 'processBatch':
                processBatch(data);
                break;
            case 'processChunk':
                processChunk(data);
                break;
            case 'parseCSVLine':
                parseCSVLineAction(data);
                break;
            case 'getWorkerStatus':
                getWorkerStatus();
                break;
            case 'cleanup':
                cleanup();
                break;
            default:
                throw new Error(`Unknown action: ${action}`);
        }
    } catch (error) {
        console.error(`Worker ${workerId || 'uninitialized'} error processing message:`, error);

        // Enhanced error reporting with context
        self.postMessage({
            action: 'error',
            workerId: workerId,
            error: {
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
                context: e.data ? e.data.action : 'unknown',
                isProcessing: isProcessing,
                receivedData: e.data ? JSON.stringify(e.data).substring(0, 200) : 'none'
            }
        });
    }
};

// Initialize worker with configuration
function initializeWorker(config) {
    try {
        console.log(`Initializing Parallel CSV Worker with config:`, config);

        workerId = config.workerId;

        // Merge configuration
        if (config.workerConfig) {
            Object.assign(WORKER_CONFIG, config.workerConfig);
        }

        console.log(`Parallel CSV Worker ${workerId} initialized with config:`, WORKER_CONFIG);

        // Send initialization confirmation
        self.postMessage({
            action: 'workerInitialized',
            workerId: workerId,
            result: {
                config: WORKER_CONFIG,
                timestamp: new Date().toISOString(),
                status: 'ready'
            }
        });

        console.log(`Worker ${workerId} initialization message sent`);
    } catch (error) {
        console.error(`Worker ${workerId} initialization failed:`, error);
        self.postMessage({
            action: 'error',
            workerId: workerId,
            error: {
                message: `Worker initialization failed: ${error.message}`,
                stack: error.stack,
                timestamp: new Date().toISOString(),
                context: 'initializeWorker'
            }
        });
    }
}

// Process a batch of records with parallel processing optimization
function processBatch(data) {
    const startTime = performance.now();
    isProcessing = true;

    try {
        const { batchData, batchIndex, header, fileInfo } = data;

        // Enhanced input validation
        if (!batchData || !Array.isArray(batchData)) {
            throw new Error('Invalid batch data: batchData must be a non-empty array');
        }

        if (!header || !Array.isArray(header) || header.length === 0) {
            throw new Error('Invalid batch data: header must be a non-empty array');
        }

        if (typeof batchIndex !== 'number' || batchIndex < 0) {
            throw new Error('Invalid batch data: batchIndex must be a non-negative number');
        }

        // Processing batch silently for production

        // Initialize batch metrics
        const batchMetrics = initializeBatchMetrics();
        const batchDateData = {};
        const highValueTransactions = [];
        const highValueTransactionsUSD = [];
        const sampleTransactions = [];

        // Process each record in the batch
        let processedRecords = 0;
        for (const record of batchData) {
            if (!record.REPORTTYPE) continue;

            processedRecords++;

            // Update metrics incrementally
            updateMetricsWithRow(record, batchMetrics, {
                highValueTransactions,
                highValueTransactionsUSD
            });
            updateDateData(record, batchDateData);

            // Sample collection (keep only a small percentage)
            if (Math.random() < WORKER_CONFIG.sampleRate) {
                sampleTransactions.push(record);
            }
        }

        // Update unique serial counts
        batchMetrics.hocUniqueSerialCount = batchMetrics.hocSerialNumbers.size;
        batchMetrics.ibdUniqueSerialCount = batchMetrics.ibdSerialNumbers.size;

        const processingTime = performance.now() - startTime;
        processedBatches++;

        // Report batch completion
        self.postMessage({
            action: 'batchProcessed',
            workerId: workerId,
            result: {
                batchIndex: batchIndex,
                metrics: batchMetrics,
                dateData: batchDateData,
                highValueTransactions: highValueTransactions,
                highValueTransactionsUSD: highValueTransactionsUSD,
                sampleTransactions: sampleTransactions.slice(0, 50), // Limit samples
                processedRecords: processedRecords,
                processingTime: processingTime,
                totalBatchesProcessed: processedBatches
            }
        });

        // Trigger garbage collection if needed
        if (checkMemoryPressure()) {
            triggerGarbageCollection();
        }

    } catch (error) {
        throw new Error(`Batch processing failed: ${error.message}`);
    } finally {
        isProcessing = false;
    }
}

// Legacy chunk processing for backward compatibility
function processChunk(data) {
    try {
        const { chunk, header, isFirstChunk } = data;

        if (!chunk || !header) {
            throw new Error('Invalid chunk data: missing chunk or header');
        }

        const result = processChunkData(chunk, header, isFirstChunk);

        self.postMessage({
            action: 'chunkProcessed',
            workerId: workerId,
            result: result
        });
    } catch (error) {
        throw new Error(`Chunk processing failed: ${error.message}`);
    }
}

// Parse CSV line action
function parseCSVLineAction(data) {
    try {
        if (!data || typeof data.line !== 'string') {
            throw new Error('Invalid line data: line must be a string');
        }

        const result = parseCSVLine(data.line);

        self.postMessage({
            action: 'lineProcessed',
            workerId: workerId,
            result: result
        });
    } catch (error) {
        throw new Error(`CSV line parsing failed: ${error.message}`);
    }
}

// Get worker status
function getWorkerStatus() {
    self.postMessage({
        action: 'workerStatus',
        workerId: workerId,
        status: {
            isProcessing: isProcessing,
            processedBatches: processedBatches,
            memoryUsage: getMemoryUsage(),
            uptime: performance.now()
        }
    });
}

// Cleanup worker resources
function cleanup() {
    isProcessing = false;
    processedBatches = 0;

    // Force garbage collection
    triggerGarbageCollection();

    self.postMessage({
        action: 'workerCleaned',
        workerId: workerId
    });
}

// Initialize batch metrics structure
function initializeBatchMetrics() {
    return {
        totalTransactions: 0,
        totalAmount: 0,
        hocCount: 0,
        hocAmount: 0,
        hocAmountMMK: 0,
        hocAmountUSD: 0,
        hocCreditCount: 0,
        hocCreditAmount: 0,
        hocCreditAmountMMK: 0,
        hocCreditAmountUSD: 0,
        hocCreditAmountCNY: 0,
        hocCreditAmountEUR: 0,
        hocCreditAmountINR: 0,
        hocCreditAmountJPY: 0,
        hocCreditAmountSGD: 0,
        hocCreditAmountTHB: 0,
        hocDebitCount: 0,
        hocDebitAmount: 0,
        hocDebitAmountMMK: 0,
        hocDebitAmountUSD: 0,
        hocDebitAmountCNY: 0,
        hocDebitAmountEUR: 0,
        hocDebitAmountINR: 0,
        hocDebitAmountJPY: 0,
        hocDebitAmountSGD: 0,
        hocDebitAmountTHB: 0,
        ibdCount: 0,
        ibdAmount: 0,
        ibdAmountMMK: 0,
        ibdAmountUSD: 0,
        ibdCreditCount: 0,
        ibdCreditAmount: 0,
        ibdCreditAmountMMK: 0,
        ibdCreditAmountUSD: 0,
        ibdCreditAmountCNY: 0,
        ibdCreditAmountEUR: 0,
        ibdCreditAmountINR: 0,
        ibdCreditAmountJPY: 0,
        ibdCreditAmountSGD: 0,
        ibdCreditAmountTHB: 0,
        ibdDebitCount: 0,
        ibdDebitAmount: 0,
        ibdDebitAmountMMK: 0,
        ibdDebitAmountUSD: 0,
        ibdDebitAmountCNY: 0,
        ibdDebitAmountEUR: 0,
        ibdDebitAmountINR: 0,
        ibdDebitAmountJPY: 0,
        ibdDebitAmountSGD: 0,
        ibdDebitAmountTHB: 0,
        wuCount: 0,
        wuAmount: 0,
        wuAmountMMK: 0,
        wuAmountUSD: 0,
        wuCreditCount: 0,
        wuCreditAmount: 0,
        wuCreditAmountMMK: 0,
        wuCreditAmountUSD: 0,
        wuCreditAmountCNY: 0,
        wuCreditAmountEUR: 0,
        wuCreditAmountINR: 0,
        wuCreditAmountJPY: 0,
        wuCreditAmountSGD: 0,
        wuCreditAmountTHB: 0,
        wuDebitCount: 0,
        wuDebitAmount: 0,
        wuDebitAmountMMK: 0,
        wuDebitAmountUSD: 0,
        wuDebitAmountCNY: 0,
        wuDebitAmountEUR: 0,
        wuDebitAmountINR: 0,
        wuDebitAmountJPY: 0,
        wuDebitAmountSGD: 0,
        wuDebitAmountTHB: 0,
        hocUniqueSerialCount: 0,
        ibdUniqueSerialCount: 0,
        wuUniqueSerialCount: 0,
        currencyCounts: {
            MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
        },
        currencyAmounts: {
            MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
        },
        currencyCreditAmounts: {
            MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
        },
        currencyDebitAmounts: {
            MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
        },
        // High-value transaction counts by currency
        highValueTransactionCounts: {
            MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
        },
        highValueTransactionCount: 0,
        highValueTransactionCountUSD: 0,
        hocSerialNumbers: new Set(),
        ibdSerialNumbers: new Set(),
        wuSerialNumbers: new Set()
    };
}

// Process chunk data (legacy support)
function processChunkData(chunk, header, isFirstChunk) {
    // Split the chunk into lines
    const lines = chunk.split('\n');

    // Pre-allocate arrays for better performance
    const processedLines = [];
    processedLines.length = lines.length;
    let processedCount = 0;

    let startIndex = isFirstChunk ? 1 : 0; // Skip header if this is the first chunk

    // Create field index map for faster access
    const fieldIndexMap = new Map();
    const requiredFields = [
        'REPORTTYPE', 'TRANSACTION_AMOUNT', 'TRANSACTION_CURRENCY',
        'ACCOUNT_HOLDER_ACCOUNT_ROLE', 'SERIAL_NO', 'TRANSACTION_DATE'
    ];

    requiredFields.forEach(field => {
        const index = header.indexOf(field);
        if (index !== -1) {
            fieldIndexMap.set(field, index);
        }
    });

    for (let i = startIndex; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line === '') continue;

        const rowData = parseCSVLine(line);

        // Only process rows that have enough data
        if (rowData.length >= header.length) {
            // Create a minimal object with only the fields we need using the index map
            const rowObject = {};
            let hasReportType = false;

            for (const [field, index] of fieldIndexMap) {
                const value = rowData[index] || '';
                rowObject[field] = value;
                if (field === 'REPORTTYPE' && value) {
                    hasReportType = true;
                }
            }

            // Only add rows that have a REPORTTYPE value
            if (hasReportType) {
                processedLines[processedCount++] = rowObject;
            }
        }
    }

    // Trim array to actual size
    processedLines.length = processedCount;

    return {
        processedLines: processedLines,
        metrics: calculateMetrics(processedLines),
        dateData: aggregateByDate(processedLines)
    };
}

// Parse a CSV line into an array of values
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    // Add the last field
    result.push(current.trim());

    return result;
}

// Update metrics with a single row
function updateMetricsWithRow(row, metrics, options = {}) {
    const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);
    const amount = parseFloat(row.TRANSACTION_AMOUNT) || 0;

    metrics.totalTransactions++;
    metrics.totalAmount = safeAdd(metrics.totalAmount, amount);

    // Check if this is a high-value transaction for any currency
    const currencyThresholds = {
        MMK: 1000000000,  // 1 billion MMK
        USD: 10000,       // 10,000 USD
        SGD: 15000,       // 15,000 SGD
        EUR: 9000,        // 9,000 EUR
        JPY: 1100000,     // 1,100,000 JPY
        CNY: 70000,       // 70,000 CNY
        THB: 350000,      // 350,000 THB
        INR: 800000       // 800,000 INR
    };

    const currency = row.TRANSACTION_CURRENCY || 'MMK';
    const threshold = currencyThresholds[currency];
    if (threshold && amount >= threshold) {
        // Update currency-specific high-value counts
        if (metrics.highValueTransactionCounts && metrics.highValueTransactionCounts[currency] !== undefined) {
            metrics.highValueTransactionCounts[currency] = safeAdd(metrics.highValueTransactionCounts[currency], 1);
        }

        // Maintain backward compatibility with legacy counters
        if (currency === 'MMK') {
            metrics.highValueTransactionCount = safeAdd(metrics.highValueTransactionCount || 0, 1);

            // Collect high-value MMK transaction for export
            if (options.highValueTransactions) {
                options.highValueTransactions.push({
                TRANSACTION_DATE: row.TRANSACTION_DATE,
                TRANSACTION_AMOUNT: row.TRANSACTION_AMOUNT,
                TRANSACTION_CURRENCY: row.TRANSACTION_CURRENCY,
                REPORTTYPE: row.REPORTTYPE,
                ACCOUNT_HOLDER_ACCOUNT_ROLE: row.ACCOUNT_HOLDER_ACCOUNT_ROLE,
                CUSTOMER_ID: row.CUSTOMER_ID || row.PARTICIPANT_ID || 'Unknown',
                CUSTOMER_NAME: row.CUSTOMER_NAME || row.PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                PARTICIPANT_NAME_CONDUCTOR: row.PARTICIPANT_NAME_CONDUCTOR || row.CUSTOMER_NAME || 'Unknown',
                PARTICIPANT_NAME_COUNTERPARTY: row.PARTICIPANT_NAME_COUNTERPARTY || 'N/A',
                PARTICIPANT_ID_NUMBER_CONDUCTOR: row.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A',
                PARTICIPANT_ID_NUMBER_COUNTERPARTY: row.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A',
                ACCOUNT_NUMBER: row.ACCOUNT_NUMBER || row.ACCOUNT_NO || 'Unknown',
                TRANSACTION_TYPE: row.TRANSACTION_TYPE || 'Unknown',
                TRANSACTION_DESCRIPTION: row.TRANSACTION_DESCRIPTION || row.DESCRIPTION || 'N/A',
                SERIAL_NO: row.SERIAL_NO || '',
                BRANCH_CODE: row.BRANCH_CODE || '',
                REFERENCE_NUMBER: row.REFERENCE_NUMBER || row.REF_NO || ''
            });
        }
        } else if (currency === 'USD') {
            metrics.highValueTransactionCountUSD = safeAdd(metrics.highValueTransactionCountUSD || 0, 1);

            // Collect high-value USD transaction for export
            if (options.highValueTransactionsUSD) {
                options.highValueTransactionsUSD.push({
                TRANSACTION_DATE: row.TRANSACTION_DATE,
                TRANSACTION_AMOUNT: row.TRANSACTION_AMOUNT,
                TRANSACTION_CURRENCY: row.TRANSACTION_CURRENCY,
                REPORTTYPE: row.REPORTTYPE,
                ACCOUNT_HOLDER_ACCOUNT_ROLE: row.ACCOUNT_HOLDER_ACCOUNT_ROLE,
                CUSTOMER_ID: row.CUSTOMER_ID || row.PARTICIPANT_ID || 'Unknown',
                CUSTOMER_NAME: row.CUSTOMER_NAME || row.PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                PARTICIPANT_NAME_CONDUCTOR: row.PARTICIPANT_NAME_CONDUCTOR || row.CUSTOMER_NAME || 'Unknown',
                PARTICIPANT_NAME_COUNTERPARTY: row.PARTICIPANT_NAME_COUNTERPARTY || 'N/A',
                PARTICIPANT_ID_NUMBER_CONDUCTOR: row.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A',
                PARTICIPANT_ID_NUMBER_COUNTERPARTY: row.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A',
                ACCOUNT_NUMBER: row.ACCOUNT_NUMBER || row.ACCOUNT_NO || 'Unknown',
                TRANSACTION_TYPE: row.TRANSACTION_TYPE || 'Unknown',
                TRANSACTION_DESCRIPTION: row.TRANSACTION_DESCRIPTION || row.DESCRIPTION || 'N/A',
                SERIAL_NO: row.SERIAL_NO || '',
                BRANCH_CODE: row.BRANCH_CODE || '',
                REFERENCE_NUMBER: row.REFERENCE_NUMBER || row.REF_NO || ''
                });
            }
        }
    }

    const isCredit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
    const isDebit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
    const serialNo = row.SERIAL_NO || '';

    // Update currency counts and amounts for all supported currencies
    const currency = row.TRANSACTION_CURRENCY || 'MMK';
    const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

    if (supportedCurrencies.includes(currency)) {
        metrics.currencyCounts[currency]++;
        // REMOVED: metrics.currencyAmounts[currency] = safeAdd(metrics.currencyAmounts[currency], amount);
        // Currency amounts are calculated in report-type specific sections below to prevent double counting

        // Track credit/debit amounts by currency
        if (isCredit) {
            metrics.currencyCreditAmounts[currency] = safeAdd(metrics.currencyCreditAmounts[currency], amount);
        } else if (isDebit) {
            metrics.currencyDebitAmounts[currency] = safeAdd(metrics.currencyDebitAmounts[currency], amount);
        }
    } else {
        // Default to MMK for unsupported currencies
        metrics.currencyCounts.MMK++;
        // REMOVED: metrics.currencyAmounts.MMK = safeAdd(metrics.currencyAmounts.MMK, amount);
        // Currency amounts are calculated in report-type specific sections below to prevent double counting

        // Track credit/debit amounts by currency for unsupported currencies (default to MMK)
        if (isCredit) {
            metrics.currencyCreditAmounts.MMK = safeAdd(metrics.currencyCreditAmounts.MMK, amount);
        } else if (isDebit) {
            metrics.currencyDebitAmounts.MMK = safeAdd(metrics.currencyDebitAmounts.MMK, amount);
        }
    }

    if (row.REPORTTYPE === 'HOC') {
        metrics.hocCount++;
        metrics.hocAmount = safeAdd(metrics.hocAmount, amount);

        const currency = row.TRANSACTION_CURRENCY;
        if (currency === 'MMK') {
            metrics.hocAmountMMK = safeAdd(metrics.hocAmountMMK, amount);
        } else if (currency === 'USD') {
            metrics.hocAmountUSD = safeAdd(metrics.hocAmountUSD, amount);
        }
        // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

        if (serialNo) {
            metrics.hocSerialNumbers.add(serialNo);
        }

        if (isCredit) {
            metrics.hocCreditCount++;
            metrics.hocCreditAmount = safeAdd(metrics.hocCreditAmount, amount);
            const currency = row.TRANSACTION_CURRENCY;
            if (currency === 'MMK') {
                metrics.hocCreditAmountMMK = safeAdd(metrics.hocCreditAmountMMK, amount);
            } else if (currency === 'USD') {
                metrics.hocCreditAmountUSD = safeAdd(metrics.hocCreditAmountUSD, amount);
            } else if (currency === 'CNY') {
                metrics.hocCreditAmountCNY = safeAdd(metrics.hocCreditAmountCNY, amount);
            } else if (currency === 'EUR') {
                metrics.hocCreditAmountEUR = safeAdd(metrics.hocCreditAmountEUR, amount);
            } else if (currency === 'INR') {
                metrics.hocCreditAmountINR = safeAdd(metrics.hocCreditAmountINR, amount);
            } else if (currency === 'JPY') {
                metrics.hocCreditAmountJPY = safeAdd(metrics.hocCreditAmountJPY, amount);
            } else if (currency === 'SGD') {
                metrics.hocCreditAmountSGD = safeAdd(metrics.hocCreditAmountSGD, amount);
            } else if (currency === 'THB') {
                metrics.hocCreditAmountTHB = safeAdd(metrics.hocCreditAmountTHB, amount);
            }
        } else if (isDebit) {
            metrics.hocDebitCount++;
            metrics.hocDebitAmount = safeAdd(metrics.hocDebitAmount, amount);
            const currency = row.TRANSACTION_CURRENCY;
            if (currency === 'MMK') {
                metrics.hocDebitAmountMMK = safeAdd(metrics.hocDebitAmountMMK, amount);
            } else if (currency === 'USD') {
                metrics.hocDebitAmountUSD = safeAdd(metrics.hocDebitAmountUSD, amount);
            } else if (currency === 'CNY') {
                metrics.hocDebitAmountCNY = safeAdd(metrics.hocDebitAmountCNY, amount);
            } else if (currency === 'EUR') {
                metrics.hocDebitAmountEUR = safeAdd(metrics.hocDebitAmountEUR, amount);
            } else if (currency === 'INR') {
                metrics.hocDebitAmountINR = safeAdd(metrics.hocDebitAmountINR, amount);
            } else if (currency === 'JPY') {
                metrics.hocDebitAmountJPY = safeAdd(metrics.hocDebitAmountJPY, amount);
            } else if (currency === 'SGD') {
                metrics.hocDebitAmountSGD = safeAdd(metrics.hocDebitAmountSGD, amount);
            } else if (currency === 'THB') {
                metrics.hocDebitAmountTHB = safeAdd(metrics.hocDebitAmountTHB, amount);
            }
        }
    } else if (row.REPORTTYPE === 'IBD') {
        metrics.ibdCount++;
        metrics.ibdAmount = safeAdd(metrics.ibdAmount, amount);

        const currency = row.TRANSACTION_CURRENCY;
        if (currency === 'MMK') {
            metrics.ibdAmountMMK = safeAdd(metrics.ibdAmountMMK, amount);
        } else if (currency === 'USD') {
            metrics.ibdAmountUSD = safeAdd(metrics.ibdAmountUSD, amount);
        }
        // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

        if (serialNo) {
            metrics.ibdSerialNumbers.add(serialNo);
        }

        if (isCredit) {
            metrics.ibdCreditCount++;
            metrics.ibdCreditAmount = safeAdd(metrics.ibdCreditAmount, amount);
            const currency = row.TRANSACTION_CURRENCY;
            if (currency === 'MMK') {
                metrics.ibdCreditAmountMMK = safeAdd(metrics.ibdCreditAmountMMK, amount);
            } else if (currency === 'USD') {
                metrics.ibdCreditAmountUSD = safeAdd(metrics.ibdCreditAmountUSD, amount);
            } else if (currency === 'CNY') {
                metrics.ibdCreditAmountCNY = safeAdd(metrics.ibdCreditAmountCNY, amount);
            } else if (currency === 'EUR') {
                metrics.ibdCreditAmountEUR = safeAdd(metrics.ibdCreditAmountEUR, amount);
            } else if (currency === 'INR') {
                metrics.ibdCreditAmountINR = safeAdd(metrics.ibdCreditAmountINR, amount);
            } else if (currency === 'JPY') {
                metrics.ibdCreditAmountJPY = safeAdd(metrics.ibdCreditAmountJPY, amount);
            } else if (currency === 'SGD') {
                metrics.ibdCreditAmountSGD = safeAdd(metrics.ibdCreditAmountSGD, amount);
            } else if (currency === 'THB') {
                metrics.ibdCreditAmountTHB = safeAdd(metrics.ibdCreditAmountTHB, amount);
            }
        } else if (isDebit) {
            metrics.ibdDebitCount++;
            metrics.ibdDebitAmount = safeAdd(metrics.ibdDebitAmount, amount);
            const currency = row.TRANSACTION_CURRENCY;
            if (currency === 'MMK') {
                metrics.ibdDebitAmountMMK = safeAdd(metrics.ibdDebitAmountMMK, amount);
            } else if (currency === 'USD') {
                metrics.ibdDebitAmountUSD = safeAdd(metrics.ibdDebitAmountUSD, amount);
            } else if (currency === 'CNY') {
                metrics.ibdDebitAmountCNY = safeAdd(metrics.ibdDebitAmountCNY, amount);
            } else if (currency === 'EUR') {
                metrics.ibdDebitAmountEUR = safeAdd(metrics.ibdDebitAmountEUR, amount);
            } else if (currency === 'INR') {
                metrics.ibdDebitAmountINR = safeAdd(metrics.ibdDebitAmountINR, amount);
            } else if (currency === 'JPY') {
                metrics.ibdDebitAmountJPY = safeAdd(metrics.ibdDebitAmountJPY, amount);
            } else if (currency === 'SGD') {
                metrics.ibdDebitAmountSGD = safeAdd(metrics.ibdDebitAmountSGD, amount);
            } else if (currency === 'THB') {
                metrics.ibdDebitAmountTHB = safeAdd(metrics.ibdDebitAmountTHB, amount);
            }
        }
    } else if (row.REPORTTYPE === 'WU') {
        metrics.wuCount++;
        metrics.wuAmount = safeAdd(metrics.wuAmount, amount);

        // Update currency-specific WU amounts
        if (currency === 'MMK') {
            metrics.wuAmountMMK = safeAdd(metrics.wuAmountMMK, amount);
        } else if (currency === 'USD') {
            metrics.wuAmountUSD = safeAdd(metrics.wuAmountUSD, amount);
        }
        // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

        if (isCredit) {
            metrics.wuCreditCount++;
            metrics.wuCreditAmount = safeAdd(metrics.wuCreditAmount, amount);
            if (currency === 'MMK') {
                metrics.wuCreditAmountMMK = safeAdd(metrics.wuCreditAmountMMK, amount);
            } else if (currency === 'USD') {
                metrics.wuCreditAmountUSD = safeAdd(metrics.wuCreditAmountUSD, amount);
            } else if (currency === 'CNY') {
                metrics.wuCreditAmountCNY = safeAdd(metrics.wuCreditAmountCNY, amount);
            } else if (currency === 'EUR') {
                metrics.wuCreditAmountEUR = safeAdd(metrics.wuCreditAmountEUR, amount);
            } else if (currency === 'INR') {
                metrics.wuCreditAmountINR = safeAdd(metrics.wuCreditAmountINR, amount);
            } else if (currency === 'JPY') {
                metrics.wuCreditAmountJPY = safeAdd(metrics.wuCreditAmountJPY, amount);
            } else if (currency === 'SGD') {
                metrics.wuCreditAmountSGD = safeAdd(metrics.wuCreditAmountSGD, amount);
            } else if (currency === 'THB') {
                metrics.wuCreditAmountTHB = safeAdd(metrics.wuCreditAmountTHB, amount);
            }
        } else if (isDebit) {
            metrics.wuDebitCount++;
            metrics.wuDebitAmount = safeAdd(metrics.wuDebitAmount, amount);
            if (currency === 'MMK') {
                metrics.wuDebitAmountMMK = safeAdd(metrics.wuDebitAmountMMK, amount);
            } else if (currency === 'USD') {
                metrics.wuDebitAmountUSD = safeAdd(metrics.wuDebitAmountUSD, amount);
            } else if (currency === 'CNY') {
                metrics.wuDebitAmountCNY = safeAdd(metrics.wuDebitAmountCNY, amount);
            } else if (currency === 'EUR') {
                metrics.wuDebitAmountEUR = safeAdd(metrics.wuDebitAmountEUR, amount);
            } else if (currency === 'INR') {
                metrics.wuDebitAmountINR = safeAdd(metrics.wuDebitAmountINR, amount);
            } else if (currency === 'JPY') {
                metrics.wuDebitAmountJPY = safeAdd(metrics.wuDebitAmountJPY, amount);
            } else if (currency === 'SGD') {
                metrics.wuDebitAmountSGD = safeAdd(metrics.wuDebitAmountSGD, amount);
            } else if (currency === 'THB') {
                metrics.wuDebitAmountTHB = safeAdd(metrics.wuDebitAmountTHB, amount);
            }
        }
    }

    // Calculate total currency amounts from report-type specific amounts to prevent double counting
    metrics.currencyAmounts.MMK = safeAdd(
        safeAdd(metrics.hocAmountMMK || 0, metrics.ibdAmountMMK || 0),
        metrics.wuAmountMMK || 0
    );
    metrics.currencyAmounts.USD = safeAdd(
        safeAdd(metrics.hocAmountUSD || 0, metrics.ibdAmountUSD || 0),
        metrics.wuAmountUSD || 0
    );

    // For other currencies (SGD, EUR, JPY, CNY, THB, INR), calculate from credit/debit amounts
    // since they don't have report-type specific tracking
    const otherCurrencies = ['SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
    otherCurrencies.forEach(currency => {
        metrics.currencyAmounts[currency] = safeAdd(
            metrics.currencyCreditAmounts[currency] || 0,
            metrics.currencyDebitAmounts[currency] || 0
        );
    });
}

// Update date-based data aggregation
function updateDateData(row, dateData) {
    const date = row.TRANSACTION_DATE;
    if (!date) return;

    if (!dateData[date]) {
        dateData[date] = {
            hocCreditCount: 0, hocCreditAmountMMK: 0, hocCreditAmountUSD: 0,
            hocDebitCount: 0, hocDebitAmountMMK: 0, hocDebitAmountUSD: 0,
            ibdCreditCount: 0, ibdCreditAmountMMK: 0, ibdCreditAmountUSD: 0,
            ibdDebitCount: 0, ibdDebitAmountMMK: 0, ibdDebitAmountUSD: 0,
            wuCreditCount: 0, wuCreditAmountMMK: 0, wuCreditAmountUSD: 0,
            wuDebitCount: 0, wuDebitAmountMMK: 0, wuDebitAmountUSD: 0,
            // Legacy fields for backward compatibility
            hocCreditAmount: 0, hocDebitAmount: 0, ibdCreditAmount: 0, ibdDebitAmount: 0
        };
    }

    const amount = parseFloat(row.TRANSACTION_AMOUNT) || 0;
    const isCredit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
    const isDebit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
    const currency = row.TRANSACTION_CURRENCY;

    if (row.REPORTTYPE === 'HOC') {
        if (isCredit) {
            dateData[date].hocCreditCount++;
            dateData[date].hocCreditAmount += amount;
            if (currency === 'MMK') {
                dateData[date].hocCreditAmountMMK += amount;
            } else if (currency === 'USD') {
                dateData[date].hocCreditAmountUSD += amount;
            }
        } else if (isDebit) {
            dateData[date].hocDebitCount++;
            dateData[date].hocDebitAmount += amount;
            if (currency === 'MMK') {
                dateData[date].hocDebitAmountMMK += amount;
            } else if (currency === 'USD') {
                dateData[date].hocDebitAmountUSD += amount;
            }
        }
    } else if (row.REPORTTYPE === 'IBD') {
        if (isCredit) {
            dateData[date].ibdCreditCount++;
            dateData[date].ibdCreditAmount += amount;
            if (currency === 'MMK') {
                dateData[date].ibdCreditAmountMMK += amount;
            } else if (currency === 'USD') {
                dateData[date].ibdCreditAmountUSD += amount;
            }
        } else if (isDebit) {
            dateData[date].ibdDebitCount++;
            dateData[date].ibdDebitAmount += amount;
            if (currency === 'MMK') {
                dateData[date].ibdDebitAmountMMK += amount;
            } else if (currency === 'USD') {
                dateData[date].ibdDebitAmountUSD += amount;
            }
        }
    } else if (row.REPORTTYPE === 'WU') {
        if (isCredit) {
            dateData[date].wuCreditCount++;
            if (currency === 'MMK') {
                dateData[date].wuCreditAmountMMK += amount;
            } else if (currency === 'USD') {
                dateData[date].wuCreditAmountUSD += amount;
            }
        } else if (isDebit) {
            dateData[date].wuDebitCount++;
            if (currency === 'MMK') {
                dateData[date].wuDebitAmountMMK += amount;
            } else if (currency === 'USD') {
                dateData[date].wuDebitAmountUSD += amount;
            }
        }
    }
}

// Calculate metrics for processed lines (legacy support)
function calculateMetrics(processedLines) {
    const metrics = initializeBatchMetrics();

    for (const row of processedLines) {
        updateMetricsWithRow(row, metrics);
    }

    return metrics;
}

// Aggregate data by date (legacy support)
function aggregateByDate(processedLines) {
    const dateData = {};

    for (const row of processedLines) {
        updateDateData(row, dateData);
    }

    return dateData;
}

// Check memory pressure
function checkMemoryPressure() {
    if (typeof performance !== 'undefined' && performance.memory) {
        const memInfo = performance.memory;
        const usageRatio = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;
        return usageRatio > WORKER_CONFIG.gcThreshold;
    }
    return false;
}

// Get memory usage information
function getMemoryUsage() {
    if (typeof performance !== 'undefined' && performance.memory) {
        return {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        };
    }
    return { used: 0, total: 0, limit: 0 };
}

// Trigger garbage collection
function triggerGarbageCollection() {
    // Create and destroy temporary objects to encourage GC
    const temp = new Array(1000).fill(null).map(() => ({
        data: new Array(1000).join('x')
    }));
    temp.length = 0;

    console.log(`Worker ${workerId}: Triggered garbage collection due to memory pressure`);
}

// Report worker performance metrics
function reportPerformanceMetrics() {
    const memoryUsage = getMemoryUsage();

    self.postMessage({
        action: 'performanceMetrics',
        workerId: workerId,
        metrics: {
            processedBatches: processedBatches,
            memoryUsage: memoryUsage,
            isProcessing: isProcessing,
            uptime: performance.now()
        }
    });
}