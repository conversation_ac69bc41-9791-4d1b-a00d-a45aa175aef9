/**
 * Serial Number Column Fix Verification Script
 * 
 * This script verifies that the SERIAL_NUMBER column fix is working correctly
 * and that the TTR Summary Report displays proper data.
 */

(function() {
    'use strict';

    console.log('🔍 Serial Number Column Fix Verification Script');
    console.log('===============================================');

    const verifySerialNumberFix = {
        
        // Check if the application is loaded
        checkApplicationLoaded() {
            console.log('\n📋 Checking Application State...');
            
            const checks = [
                {
                    name: 'window.dataProcessor',
                    exists: typeof window.dataProcessor !== 'undefined',
                    required: true
                },
                {
                    name: 'window.fileHandler',
                    exists: typeof window.fileHandler !== 'undefined',
                    required: true
                },
                {
                    name: 'TTR Summary Table',
                    exists: document.getElementById('ttrSummaryTableBody') !== null,
                    required: true
                }
            ];

            let allPassed = true;
            checks.forEach(check => {
                const status = check.exists ? '✅ PASS' : '❌ FAIL';
                console.log(`${status}: ${check.name}`);
                if (check.required && !check.exists) {
                    allPassed = false;
                }
            });

            return allPassed;
        },

        // Check current TTR table state
        checkTTRTableState() {
            console.log('\n📊 Current TTR Table State...');
            
            const tableBody = document.getElementById('ttrSummaryTableBody');
            if (!tableBody) {
                console.log('❌ TTR Summary table body not found');
                return false;
            }

            const rows = tableBody.children;
            console.log(`TTR Table has ${rows.length} rows`);

            if (rows.length === 0) {
                console.log('⚠️ TTR table is empty');
                return false;
            }

            // Check if showing empty message
            const emptyMessage = tableBody.querySelector('.empty-table-message');
            if (emptyMessage) {
                console.log('ℹ️ TTR table showing empty message:', emptyMessage.textContent.trim());
                return false;
            }

            // Check actual data rows
            let dataRows = 0;
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                if (!row.classList.contains('empty-table-message')) {
                    dataRows++;
                    const cells = row.children;
                    if (cells.length >= 9) {
                        const reportDate = cells[0].textContent.trim();
                        const hocSerial = cells[1].textContent.trim();
                        const hocCount = cells[2].textContent.trim();
                        const ibdSerial = cells[3].textContent.trim();
                        const ibdCount = cells[4].textContent.trim();
                        const wuSerial = cells[5].textContent.trim();
                        const wuCount = cells[6].textContent.trim();
                        const oneBillionCount = cells[7].textContent.trim();
                        const grandTotal = cells[8].textContent.trim();

                        console.log(`Row ${i + 1}:`);
                        console.log(`  Report Date: ${reportDate}`);
                        console.log(`  HOC Serial: ${hocSerial}`);
                        console.log(`  HOC Count: ${hocCount}`);
                        console.log(`  IBD Serial: ${ibdSerial}`);
                        console.log(`  IBD Count: ${ibdCount}`);
                        console.log(`  WU Serial: ${wuSerial}`);
                        console.log(`  WU Count: ${wuCount}`);
                        console.log(`  1B+ Count: ${oneBillionCount}`);
                        console.log(`  Grand Total: ${grandTotal}`);

                        // Check for the problematic values
                        if (reportDate === 'Unknown Date') {
                            console.log('❌ Report Date still showing as "Unknown Date"');
                        }
                        if (hocSerial === '-' && ibdSerial === '-' && wuSerial === '-') {
                            console.log('❌ All serial numbers still showing as dashes');
                        }
                        if (hocCount === '0' && ibdCount === '0' && wuCount === '0' && grandTotal === '0') {
                            console.log('❌ All counts still showing as 0');
                        }

                        // Specific check for WU Count issue
                        if (wuSerial !== '-' && wuCount === '0') {
                            console.log('❌ WU Count showing 0 but WU Serial has data - WU Count fix needed');
                        } else if (wuSerial !== '-' && wuCount !== '0') {
                            console.log('✅ WU Count fix working - showing actual count with serial data');
                        }
                    }
                }
            }

            console.log(`Found ${dataRows} data rows in TTR table`);
            return dataRows > 0;
        },

        // Check if files are loaded
        checkFileState() {
            console.log('\n📁 Checking File State...');
            
            if (!window.fileHandler) {
                console.log('❌ FileHandler not available');
                return false;
            }

            const totalFiles = window.fileHandler.files ? window.fileHandler.files.length : 0;
            const activeFiles = window.fileHandler.files ? 
                window.fileHandler.files.filter(file => !file.isRemoved).length : 0;
            const processedData = window.fileHandler.processedData ? window.fileHandler.processedData.length : 0;

            console.log(`Total Files: ${totalFiles}`);
            console.log(`Active Files: ${activeFiles}`);
            console.log(`Processed Data Files: ${processedData}`);

            if (totalFiles === 0) {
                console.log('ℹ️ No files loaded. Please upload CSV files to test the fix.');
                return false;
            }

            return activeFiles > 0;
        },

        // Test with sample CSV files
        testWithSampleFiles() {
            console.log('\n🧪 Testing with Sample Files...');
            
            // Check if sample files exist
            const sampleFiles = [
                'TTR_15Jan2024.csv',
                'daily_transactions_2024-01-20.csv'
            ];

            console.log('Available sample files to test:');
            sampleFiles.forEach(file => {
                console.log(`  - ${file}`);
            });

            console.log('\nTo test the fix:');
            console.log('1. Upload one of the sample CSV files using the file upload area');
            console.log('2. Wait for processing to complete');
            console.log('3. Check the TTR Summary Report table');
            console.log('4. Run this verification script again');

            return true;
        },

        // Check serial number processing
        checkSerialNumberProcessing() {
            console.log('\n🔢 Checking Serial Number Processing...');
            
            if (!window.dataProcessor || !window.dataProcessor.rawData) {
                console.log('❌ No raw data available for checking');
                return false;
            }

            const rawData = window.dataProcessor.rawData;
            console.log(`Raw data contains ${rawData.length} transactions`);

            if (rawData.length === 0) {
                console.log('⚠️ No transactions in raw data');
                return false;
            }

            // Check first few transactions for serial number columns
            let serialNumberFound = 0;
            let serialNoFound = 0;
            let totalChecked = Math.min(10, rawData.length);

            for (let i = 0; i < totalChecked; i++) {
                const transaction = rawData[i];
                if (transaction.SERIAL_NUMBER) {
                    serialNumberFound++;
                }
                if (transaction.SERIAL_NO) {
                    serialNoFound++;
                }
            }

            console.log(`In first ${totalChecked} transactions:`);
            console.log(`  SERIAL_NUMBER column found: ${serialNumberFound} times`);
            console.log(`  SERIAL_NO column found: ${serialNoFound} times`);

            if (serialNumberFound > 0) {
                console.log('✅ SERIAL_NUMBER column is being processed correctly');
                return true;
            } else if (serialNoFound > 0) {
                console.log('✅ SERIAL_NO column is being processed correctly');
                return true;
            } else {
                console.log('❌ No serial number columns found in data');
                return false;
            }
        },

        // Run all verification tests
        runAll() {
            console.log('🚀 Running Serial Number Fix Verification...');
            console.log('============================================');

            const results = {
                applicationLoaded: this.checkApplicationLoaded(),
                fileState: this.checkFileState(),
                ttrTableState: this.checkTTRTableState(),
                serialNumberProcessing: this.checkSerialNumberProcessing()
            };

            console.log('\n📊 Verification Results Summary:');
            console.log('================================');
            
            let passCount = 0;
            let totalTests = 0;
            
            Object.entries(results).forEach(([test, passed]) => {
                const status = passed ? '✅ PASS' : '❌ FAIL';
                console.log(`${status}: ${test}`);
                if (passed) passCount++;
                totalTests++;
            });

            const overallStatus = passCount === totalTests ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED';
            console.log(`\n${overallStatus} (${passCount}/${totalTests})`);

            if (!results.fileState) {
                console.log('\n💡 To complete testing:');
                console.log('1. Upload a CSV file with SERIAL_NUMBER column');
                console.log('2. Wait for processing to complete');
                console.log('3. Run verifySerialNumberFix.runAll() again');
            } else if (passCount === totalTests) {
                console.log('\n🎉 Serial Number Fix is working correctly!');
                console.log('The TTR Summary Report should now display proper data.');
            } else {
                console.log('\n⚠️ Some issues detected. Please check the failed tests above.');
            }

            return results;
        }
    };

    // Add verification functions to global scope
    if (typeof window !== 'undefined') {
        window.verifySerialNumberFix = verifySerialNumberFix;
        
        console.log('\n💡 Usage:');
        console.log('- Run all tests: verifySerialNumberFix.runAll()');
        console.log('- Check TTR table: verifySerialNumberFix.checkTTRTableState()');
        console.log('- Check files: verifySerialNumberFix.checkFileState()');
    }

})();
