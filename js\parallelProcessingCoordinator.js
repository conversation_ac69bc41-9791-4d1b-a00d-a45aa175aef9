/**
 * Parallel Processing Coordinator
 * Manages multiple workers for distributed CSV processing
 */

class ParallelProcessingCoordinator {
    constructor() {
        // Configuration from constants
        this.config = {
            workerCount: window.APP_CONSTANTS?.WORKER.PARALLEL_WORKERS_COUNT || 8,
            enabled: window.APP_CONSTANTS?.WORKER.PARALLEL_PROCESSING_ENABLED || true,
            batchSize: window.APP_CONSTANTS?.DATA.BATCH_SIZE || 10000,
            coordinationTimeout: window.APP_CONSTANTS?.WORKER.WORKER_COORDINATION_TIMEOUT_MS || 60000,
            memoryLimitPerWorker: window.APP_CONSTANTS?.WORKER.WORKER_MEMORY_LIMIT_MB || 12.5,
            fallbackWorkerCount: window.APP_CONSTANTS?.WORKER.FALLBACK_WORKERS_COUNT || 4,
            performanceTrackingEnabled: window.APP_CONSTANTS?.WORKER.PERFORMANCE_TRACKING_ENABLED || true
        };

        // Worker management
        this.workers = [];
        this.workerStatus = new Map();
        this.activeJobs = new Map();
        this.completedBatches = new Map();
        this.isInitialized = false;
        this.isProcessing = false;

        // Performance tracking
        this.processingStartTime = null;
        this.totalBatchesProcessed = 0;
        this.totalRecordsProcessed = 0;

        // Enhanced performance tracking for countdown timer
        this.performanceMetrics = {
            batchProcessingTimes: [],
            averageProcessingSpeed: 0,
            estimatedTimeRemaining: 0,
            totalBatches: 0,
            processedBatches: 0,
            lastUpdateTime: 0,
            processingStarted: false,
            recordsPerSecond: 0,
            batchStartTimes: new Map(),
            workerPerformance: new Map()
        };

        // Result aggregation
        this.aggregatedMetrics = null;
        this.aggregatedDateData = {};
        this.aggregatedHighValueTransactions = [];
        this.aggregatedSampleTransactions = [];

        console.log('Parallel Processing Coordinator initialized with config:', this.config);
    }

    // Initialize workers
    async initializeWorkers() {
        if (this.isInitialized) {
            console.log('Workers already initialized');
            return true;
        }

        if (!this.config.enabled) {
            console.log('Parallel processing disabled');
            return false;
        }

        if (!window.Worker) {
            console.warn('Web Workers not supported, falling back to single-threaded processing');
            return false;
        }

        try {
            console.log(`Initializing ${this.config.workerCount} parallel workers...`);

            // Create workers with fallback logic
            let successfulWorkers = 0;
            const errors = [];

            for (let i = 0; i < this.config.workerCount; i++) {
                try {
                    await this.createWorker(i);
                    successfulWorkers++;
                } catch (error) {
                    console.warn(`Failed to create worker ${i}:`, error);
                    errors.push(error);
                }
            }

            // Check if we have enough workers
            if (successfulWorkers === 0) {
                throw new Error('No workers could be initialized');
            }

            // If we failed to create the desired number of workers, try fallback
            if (successfulWorkers < this.config.workerCount) {
                console.warn(`Only ${successfulWorkers} workers initialized out of ${this.config.workerCount} requested`);

                // If we have at least one worker, continue with reduced count
                if (successfulWorkers > 0) {
                    console.log(`Continuing with ${successfulWorkers} workers instead of ${this.config.workerCount}`);
                } else {
                    // Try fallback with reduced worker count
                    console.warn(`No workers initialized, attempting fallback to ${this.config.fallbackWorkerCount} workers`);

                    // Clean up existing workers
                    this.cleanup();
                    successfulWorkers = 0;

                    // Try with fallback worker count
                    for (let i = 0; i < this.config.fallbackWorkerCount; i++) {
                        try {
                            await this.createWorker(i);
                            successfulWorkers++;
                        } catch (error) {
                            console.warn(`Failed to create fallback worker ${i}:`, error);
                        }
                    }
                }
            }

            if (successfulWorkers === 0) {
                throw new Error('No workers could be initialized, even with fallback');
            }

            this.isInitialized = true;
            const workerType = window.location.protocol === 'file:' ? 'embedded Blob workers' : 'file-based workers';
            console.log(`✅ Successfully initialized ${this.workers.length} parallel workers (requested: ${this.config.workerCount}, actual: ${successfulWorkers})`);
            console.log(`🚀 Ready for high-performance CSV processing with ${successfulWorkers} concurrent ${workerType}`);

            if (successfulWorkers < this.config.workerCount) {
                console.warn(`Running with reduced worker count: ${successfulWorkers}/${this.config.workerCount}`);
            }

            return true;

        } catch (error) {
            console.error('Failed to initialize parallel workers:', error);
            this.cleanup();
            return false;
        }
    }

    // Create a single worker
    async createWorker(workerId) {
        return new Promise((resolve, reject) => {
            let worker = null;
            let initTimeout = null;
            let isResolved = false;

            const cleanup = () => {
                if (initTimeout) {
                    clearTimeout(initTimeout);
                    initTimeout = null;
                }
                if (worker && !isResolved) {
                    worker.terminate();
                    worker = null;
                }
            };

            const resolveOnce = () => {
                if (!isResolved) {
                    isResolved = true;
                    cleanup();
                    resolve();
                }
            };

            const rejectOnce = (error) => {
                if (!isResolved) {
                    isResolved = true;
                    cleanup();
                    reject(error);
                }
            };

            try {
                console.log(`Creating worker ${workerId}...`);

                // Smart worker creation based on protocol
                const isFileProtocol = window.location.protocol === 'file:';

                if (isFileProtocol) {
                    // Skip direct file loading for file:// protocol, go straight to Blob fallback
                    console.log(`File protocol detected, using embedded worker for CORS compatibility...`);
                    try {
                        worker = this.createWorkerFromBlob();
                        console.log(`Worker ${workerId} created from embedded Blob worker`);
                    } catch (e) {
                        console.error(`Failed to create embedded worker:`, e.message);
                        throw new Error(`Embedded worker creation failed: ${e.message}`);
                    }
                } else {
                    // Try direct file loading for HTTP/HTTPS protocols
                    try {
                        // First try: Workers directory path (works with HTTP server)
                        worker = new Worker('./workers/parallelCsvWorker.js');
                        console.log(`Worker ${workerId} created with path: ./workers/parallelCsvWorker.js`);
                    } catch (e) {
                        console.warn(`Failed to load worker from ./workers/parallelCsvWorker.js:`, e.message);
                        try {
                            // Second try: Alternative path
                            worker = new Worker('workers/parallelCsvWorker.js');
                            console.log(`Worker ${workerId} created with path: workers/parallelCsvWorker.js`);
                        } catch (e2) {
                            console.warn(`Failed to load worker from workers/parallelCsvWorker.js:`, e2.message);
                            try {
                                // Third try: Create worker from Blob URL (CORS fallback)
                                console.log(`Attempting to create worker from Blob URL for CORS compatibility...`);
                                worker = this.createWorkerFromBlob();
                                console.log(`Worker ${workerId} created from Blob URL`);
                            } catch (e3) {
                                console.error(`Failed to create worker from Blob URL:`, e3.message);
                                throw new Error(`Worker creation failed: ${e3.message}`);
                            }
                        }
                    }
                }

                // Store worker immediately
                this.workers[workerId] = worker;
                this.workerStatus.set(workerId, {
                    isReady: false,
                    isProcessing: false,
                    processedBatches: 0,
                    lastActivity: Date.now(),
                    initStartTime: Date.now()
                });

                // Set up message handler with enhanced debugging
                worker.onmessage = (e) => {
                    try {
                        console.log(`Worker ${workerId} message:`, e.data);

                        // Handle initialization response specifically
                        if (e.data.action === 'workerInitialized' && e.data.workerId === workerId) {
                            console.log(`Worker ${workerId} initialized successfully`);
                            const status = this.workerStatus.get(workerId);
                            if (status) {
                                status.isReady = true;
                                status.lastActivity = Date.now();
                            }
                            resolveOnce();
                            return;
                        }

                        // Handle other messages through normal handler
                        this.handleWorkerMessage(workerId, e.data);
                    } catch (error) {
                        console.error(`Error handling worker ${workerId} message:`, error);
                        rejectOnce(new Error(`Message handling error: ${error.message}`));
                    }
                };

                // Set up error handler with enhanced debugging
                worker.onerror = (error) => {
                    console.error(`Worker ${workerId} error:`, error);
                    this.handleWorkerError(workerId, error);
                    rejectOnce(new Error(`Worker error: ${error.message || 'Unknown worker error'}`));
                };

                // Set up termination handler
                worker.onmessageerror = (error) => {
                    console.error(`Worker ${workerId} message error:`, error);
                    rejectOnce(new Error(`Worker message error: ${error.message || 'Message parsing error'}`));
                };

                // Set timeout for initialization with enhanced debugging
                initTimeout = setTimeout(() => {
                    const status = this.workerStatus.get(workerId);
                    const elapsed = status ? Date.now() - status.initStartTime : 'unknown';
                    console.error(`Worker ${workerId} initialization timeout after ${elapsed}ms`);
                    rejectOnce(new Error(`Worker ${workerId} initialization timeout after ${elapsed}ms`));
                }, 10000); // Increased timeout to 10 seconds

                // Initialize worker with enhanced debugging
                console.log(`Sending init message to worker ${workerId}...`);
                const initMessage = {
                    action: 'initWorker',
                    workerId: workerId,
                    data: {
                        workerId: workerId,
                        workerConfig: {
                            batchSize: this.config.batchSize,
                            memoryLimit: this.config.memoryLimitPerWorker * 1024 * 1024,
                            gcThreshold: 0.8,
                            sampleRate: 0.001
                        }
                    }
                };

                console.log(`Init message for worker ${workerId}:`, initMessage);
                worker.postMessage(initMessage);

            } catch (error) {
                console.error(`Failed to create worker ${workerId}:`, error);
                rejectOnce(error);
            }
        });
    }

    // Create worker from Blob URL (CORS fallback)
    createWorkerFromBlob() {
        try {
            console.log('Creating embedded worker for CORS-safe operation...');
            // Use embedded worker script directly (no fetch needed)
            const workerScript = this.getEmbeddedWorkerScript();

            // Create Blob URL
            const blob = new Blob([workerScript], { type: 'application/javascript' });
            const blobUrl = URL.createObjectURL(blob);

            // Create worker from Blob URL
            const worker = new Worker(blobUrl);

            // Clean up Blob URL after worker is created
            setTimeout(() => {
                URL.revokeObjectURL(blobUrl);
            }, 1000);

            return worker;
        } catch (error) {
            throw new Error(`Blob worker creation failed: ${error.message}`);
        }
    }

    // Embedded worker script as fallback
    getEmbeddedWorkerScript() {
        return `
/**
 * Embedded Parallel CSV Processing Web Worker
 * Fallback implementation for CORS-restricted environments
 */

// Worker configuration
const WORKER_CONFIG = {
    batchSize: 10000,
    memoryLimit: 25 * 1024 * 1024, // 25MB per worker
    gcThreshold: 0.8,
    sampleRate: 0.001
};

// Worker state
let workerId = null;
let isProcessing = false;
let processedBatches = 0;

// Enhanced message handling with parallel processing support
self.onmessage = function(e) {
    try {
        console.log(\`Worker \${workerId || 'uninitialized'} received message:\`, e.data);

        // Validate message structure
        if (!e.data || typeof e.data !== 'object') {
            throw new Error('Invalid message format received');
        }

        const { action, data, workerId: msgWorkerId } = e.data;

        if (!action) {
            throw new Error('No action specified in message');
        }

        // Set worker ID if provided
        if (msgWorkerId !== undefined) {
            workerId = msgWorkerId;
        }

        console.log(\`Worker \${workerId} processing action: \${action}\`);

        switch (action) {
            case 'initWorker':
                initializeWorker(data);
                break;
            case 'processBatch':
                processBatch(data);
                break;
            case 'getWorkerStatus':
                getWorkerStatus();
                break;
            case 'cleanup':
                cleanup();
                break;
            default:
                throw new Error(\`Unknown action: \${action}\`);
        }
    } catch (error) {
        console.error(\`Worker \${workerId || 'uninitialized'} error processing message:\`, error);

        // Enhanced error reporting with context
        self.postMessage({
            action: 'error',
            workerId: workerId,
            error: {
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
                context: e.data ? e.data.action : 'unknown',
                isProcessing: isProcessing
            }
        });
    }
};

// Initialize worker with configuration
function initializeWorker(config) {
    try {
        console.log(\`Initializing Embedded Worker with config:\`, config);

        workerId = config.workerId;

        // Merge configuration
        if (config.workerConfig) {
            Object.assign(WORKER_CONFIG, config.workerConfig);
        }

        console.log(\`Embedded Worker \${workerId} initialized with config:\`, WORKER_CONFIG);

        // Send initialization confirmation
        self.postMessage({
            action: 'workerInitialized',
            workerId: workerId,
            result: {
                config: WORKER_CONFIG,
                timestamp: new Date().toISOString(),
                status: 'ready',
                type: 'embedded'
            }
        });

        console.log(\`Embedded Worker \${workerId} initialization message sent\`);
    } catch (error) {
        console.error(\`Embedded Worker \${workerId} initialization failed:\`, error);
        self.postMessage({
            action: 'error',
            workerId: workerId,
            error: {
                message: \`Embedded worker initialization failed: \${error.message}\`,
                stack: error.stack,
                timestamp: new Date().toISOString(),
                context: 'initializeWorker'
            }
        });
    }
}

// Process a batch of records (enhanced for embedded worker)
function processBatch(data) {
    const startTime = performance.now();
    isProcessing = true;

    try {
        const { batchData, batchIndex, header } = data;

        if (!batchData || !header) {
            throw new Error('Invalid batch data: missing batchData or header');
        }

        console.log(\`Embedded Worker \${workerId}: Processing batch \${batchIndex} with \${batchData.length} records\`);

        // Initialize batch metrics (complete structure)
        const batchMetrics = {
            totalTransactions: 0,
            totalAmount: 0,
            hocCount: 0,
            hocAmount: 0,
            hocAmountMMK: 0,
            hocAmountUSD: 0,
            hocCreditCount: 0,
            hocCreditAmount: 0,
            hocCreditAmountMMK: 0,
            hocCreditAmountUSD: 0,
            hocDebitCount: 0,
            hocDebitAmount: 0,
            hocDebitAmountMMK: 0,
            hocDebitAmountUSD: 0,
            ibdCount: 0,
            ibdAmount: 0,
            ibdAmountMMK: 0,
            ibdAmountUSD: 0,
            ibdCreditCount: 0,
            ibdCreditAmount: 0,
            ibdCreditAmountMMK: 0,
            ibdCreditAmountUSD: 0,
            ibdDebitCount: 0,
            ibdDebitAmount: 0,
            ibdDebitAmountMMK: 0,
            ibdDebitAmountUSD: 0,
            hocUniqueSerialCount: 0,
            ibdUniqueSerialCount: 0,
            currencyCounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyCreditAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyDebitAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            // High-value transaction counts by currency
            highValueTransactionCounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            highValueTransactionCount: 0,
            highValueTransactionCountUSD: 0,
            // HOC currency-specific credit amounts
            hocCreditAmountCNY: 0,
            hocCreditAmountEUR: 0,
            hocCreditAmountINR: 0,
            hocCreditAmountJPY: 0,
            hocCreditAmountSGD: 0,
            hocCreditAmountTHB: 0,
            // HOC currency-specific debit amounts
            hocDebitAmountCNY: 0,
            hocDebitAmountEUR: 0,
            hocDebitAmountINR: 0,
            hocDebitAmountJPY: 0,
            hocDebitAmountSGD: 0,
            hocDebitAmountTHB: 0,
            // IBD currency-specific credit amounts
            ibdCreditAmountCNY: 0,
            ibdCreditAmountEUR: 0,
            ibdCreditAmountINR: 0,
            ibdCreditAmountJPY: 0,
            ibdCreditAmountSGD: 0,
            ibdCreditAmountTHB: 0,
            // IBD currency-specific debit amounts
            ibdDebitAmountCNY: 0,
            ibdDebitAmountEUR: 0,
            ibdDebitAmountINR: 0,
            ibdDebitAmountJPY: 0,
            ibdDebitAmountSGD: 0,
            ibdDebitAmountTHB: 0,
            // WU currency-specific credit amounts
            wuCreditAmountCNY: 0,
            wuCreditAmountEUR: 0,
            wuCreditAmountINR: 0,
            wuCreditAmountJPY: 0,
            wuCreditAmountSGD: 0,
            wuCreditAmountTHB: 0,
            // WU currency-specific debit amounts
            wuDebitAmountCNY: 0,
            wuDebitAmountEUR: 0,
            wuDebitAmountINR: 0,
            wuDebitAmountJPY: 0,
            wuDebitAmountSGD: 0,
            wuDebitAmountTHB: 0,
            hocSerialNumbers: new Set(),
            ibdSerialNumbers: new Set()
        };

        const batchDateData = {};
        const highValueTransactions = [];
        const highValueTransactionsUSD = [];
        const sampleTransactions = [];

        // Process each record in the batch
        let processedRecords = 0;
        for (const record of batchData) {
            if (!record.REPORTTYPE) continue;

            processedRecords++;
            const amount = parseFloat(record.TRANSACTION_AMOUNT) || 0;
            const isCredit = record.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
            const isDebit = record.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
            const currency = record.TRANSACTION_CURRENCY;
            const serialNo = record.SERIAL_NO || '';

            // Update basic metrics
            batchMetrics.totalTransactions++;
            batchMetrics.totalAmount += amount;

            // Update currency counts and amounts for ALL supported currencies
            const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
            if (supportedCurrencies.includes(currency)) {
                batchMetrics.currencyCounts[currency]++;

                // Track credit/debit amounts by currency
                if (isCredit) {
                    batchMetrics.currencyCreditAmounts[currency] += amount;
                } else if (isDebit) {
                    batchMetrics.currencyDebitAmounts[currency] += amount;
                }
            }

            // Update by report type
            if (record.REPORTTYPE === 'HOC') {
                batchMetrics.hocCount++;
                batchMetrics.hocAmount += amount;

                if (currency === 'MMK') {
                    batchMetrics.hocAmountMMK += amount;
                } else if (currency === 'USD') {
                    batchMetrics.hocAmountUSD += amount;
                }
                // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

                if (serialNo) {
                    batchMetrics.hocSerialNumbers.add(serialNo);
                }

                if (isCredit) {
                    batchMetrics.hocCreditCount++;
                    batchMetrics.hocCreditAmount += amount;
                    if (currency === 'MMK') {
                        batchMetrics.hocCreditAmountMMK += amount;
                    } else if (currency === 'USD') {
                        batchMetrics.hocCreditAmountUSD += amount;
                    } else if (currency === 'CNY') {
                        batchMetrics.hocCreditAmountCNY += amount;
                    } else if (currency === 'EUR') {
                        batchMetrics.hocCreditAmountEUR += amount;
                    } else if (currency === 'INR') {
                        batchMetrics.hocCreditAmountINR += amount;
                    } else if (currency === 'JPY') {
                        batchMetrics.hocCreditAmountJPY += amount;
                    } else if (currency === 'SGD') {
                        batchMetrics.hocCreditAmountSGD += amount;
                    } else if (currency === 'THB') {
                        batchMetrics.hocCreditAmountTHB += amount;
                    }
                } else if (isDebit) {
                    batchMetrics.hocDebitCount++;
                    batchMetrics.hocDebitAmount += amount;
                    if (currency === 'MMK') {
                        batchMetrics.hocDebitAmountMMK += amount;
                    } else if (currency === 'USD') {
                        batchMetrics.hocDebitAmountUSD += amount;
                    } else if (currency === 'CNY') {
                        batchMetrics.hocDebitAmountCNY += amount;
                    } else if (currency === 'EUR') {
                        batchMetrics.hocDebitAmountEUR += amount;
                    } else if (currency === 'INR') {
                        batchMetrics.hocDebitAmountINR += amount;
                    } else if (currency === 'JPY') {
                        batchMetrics.hocDebitAmountJPY += amount;
                    } else if (currency === 'SGD') {
                        batchMetrics.hocDebitAmountSGD += amount;
                    } else if (currency === 'THB') {
                        batchMetrics.hocDebitAmountTHB += amount;
                    }
                }
            } else if (record.REPORTTYPE === 'IBD') {
                batchMetrics.ibdCount++;
                batchMetrics.ibdAmount += amount;

                if (currency === 'MMK') {
                    batchMetrics.ibdAmountMMK += amount;
                } else if (currency === 'USD') {
                    batchMetrics.ibdAmountUSD += amount;
                }
                // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

                if (serialNo) {
                    batchMetrics.ibdSerialNumbers.add(serialNo);
                }

                if (isCredit) {
                    batchMetrics.ibdCreditCount++;
                    batchMetrics.ibdCreditAmount += amount;
                    if (currency === 'MMK') {
                        batchMetrics.ibdCreditAmountMMK += amount;
                    } else if (currency === 'USD') {
                        batchMetrics.ibdCreditAmountUSD += amount;
                    } else if (currency === 'CNY') {
                        batchMetrics.ibdCreditAmountCNY += amount;
                    } else if (currency === 'EUR') {
                        batchMetrics.ibdCreditAmountEUR += amount;
                    } else if (currency === 'INR') {
                        batchMetrics.ibdCreditAmountINR += amount;
                    } else if (currency === 'JPY') {
                        batchMetrics.ibdCreditAmountJPY += amount;
                    } else if (currency === 'SGD') {
                        batchMetrics.ibdCreditAmountSGD += amount;
                    } else if (currency === 'THB') {
                        batchMetrics.ibdCreditAmountTHB += amount;
                    }
                } else if (isDebit) {
                    batchMetrics.ibdDebitCount++;
                    batchMetrics.ibdDebitAmount += amount;
                    if (currency === 'MMK') {
                        batchMetrics.ibdDebitAmountMMK += amount;
                    } else if (currency === 'USD') {
                        batchMetrics.ibdDebitAmountUSD += amount;
                    } else if (currency === 'CNY') {
                        batchMetrics.ibdDebitAmountCNY += amount;
                    } else if (currency === 'EUR') {
                        batchMetrics.ibdDebitAmountEUR += amount;
                    } else if (currency === 'INR') {
                        batchMetrics.ibdDebitAmountINR += amount;
                    } else if (currency === 'JPY') {
                        batchMetrics.ibdDebitAmountJPY += amount;
                    } else if (currency === 'SGD') {
                        batchMetrics.ibdDebitAmountSGD += amount;
                    } else if (currency === 'THB') {
                        batchMetrics.ibdDebitAmountTHB += amount;
                    }
                }
            }

            // Check for high-value transactions for any currency
            const currencyThresholds = {
                MMK: 1000000000,  // 1 billion MMK
                USD: 10000,       // 10,000 USD
                SGD: 15000,       // 15,000 SGD
                EUR: 9000,        // 9,000 EUR
                JPY: 1100000,     // 1,100,000 JPY
                CNY: 70000,       // 70,000 CNY
                THB: 350000,      // 350,000 THB
                INR: 800000       // 800,000 INR
            };

            const threshold = currencyThresholds[currency];
            if (threshold && amount >= threshold) {
                // Update currency-specific high-value counts
                if (batchMetrics.highValueTransactionCounts && batchMetrics.highValueTransactionCounts[currency] !== undefined) {
                    batchMetrics.highValueTransactionCounts[currency]++;
                }

                // Maintain backward compatibility with legacy counters
                if (currency === 'MMK') {
                    batchMetrics.highValueTransactionCount++;
                    highValueTransactions.push(record);
                } else if (currency === 'USD') {
                    batchMetrics.highValueTransactionCountUSD++;
                    highValueTransactionsUSD.push(record);
                }
            }

            // Sample collection
            if (Math.random() < WORKER_CONFIG.sampleRate) {
                sampleTransactions.push(record);
            }
        }

        // Update unique serial counts
        batchMetrics.hocUniqueSerialCount = batchMetrics.hocSerialNumbers.size;
        batchMetrics.ibdUniqueSerialCount = batchMetrics.ibdSerialNumbers.size;

        // Convert Sets to Arrays for serialization
        const serializableMetrics = {
            ...batchMetrics,
            hocSerialNumbers: Array.from(batchMetrics.hocSerialNumbers),
            ibdSerialNumbers: Array.from(batchMetrics.ibdSerialNumbers)
        };

        const processingTime = performance.now() - startTime;
        processedBatches++;

        // Report batch completion
        self.postMessage({
            action: 'batchProcessed',
            workerId: workerId,
            result: {
                batchIndex: batchIndex,
                metrics: serializableMetrics,
                dateData: batchDateData,
                highValueTransactions: highValueTransactions,
                highValueTransactionsUSD: highValueTransactionsUSD,
                sampleTransactions: sampleTransactions.slice(0, 50),
                processedRecords: processedRecords,
                processingTime: processingTime,
                totalBatchesProcessed: processedBatches,
                type: 'embedded'
            }
        });

    } catch (error) {
        throw new Error(\`Embedded batch processing failed: \${error.message}\`);
    } finally {
        isProcessing = false;
    }
}

// Get worker status
function getWorkerStatus() {
    self.postMessage({
        action: 'workerStatus',
        workerId: workerId,
        status: {
            isProcessing: isProcessing,
            processedBatches: processedBatches,
            uptime: performance.now(),
            type: 'embedded'
        }
    });
}

// Cleanup worker resources
function cleanup() {
    isProcessing = false;
    processedBatches = 0;

    self.postMessage({
        action: 'workerCleaned',
        workerId: workerId,
        type: 'embedded'
    });
}

console.log('Embedded Parallel CSV Worker initialized');
`;
    }

    // Handle worker messages
    handleWorkerMessage(workerId, message) {
        const { action, result, error } = message;

        try {
            switch (action) {
                case 'workerInitialized':
                    this.handleWorkerInitialized(workerId, result);
                    break;
                case 'batchProcessed':
                    this.handleBatchProcessed(workerId, result);
                    break;
                case 'chunkProcessed':
                    this.handleChunkProcessed(workerId, result);
                    break;
                case 'error':
                    this.handleWorkerError(workerId, error);
                    break;
                case 'workerStatus':
                    this.handleWorkerStatus(workerId, result);
                    break;
                case 'performanceMetrics':
                    this.handlePerformanceMetrics(workerId, result);
                    break;
                default:
                    console.warn(`Unknown worker message action: ${action}`);
            }
        } catch (error) {
            console.error(`Error handling worker ${workerId} message:`, error);
        }
    }

    // Handle worker initialization
    handleWorkerInitialized(workerId, result) {
        const status = this.workerStatus.get(workerId);
        if (status) {
            status.isReady = true;
            status.lastActivity = Date.now();
            console.log(`Worker ${workerId} initialized and ready`);
        }
    }

    // Handle batch processing completion
    handleBatchProcessed(workerId, result) {
        const { batchIndex, metrics, dateData, highValueTransactions, highValueTransactionsUSD, sampleTransactions, processedRecords, processingTime } = result;
        const now = Date.now();

        // Store completed batch
        this.completedBatches.set(batchIndex, {
            workerId: workerId,
            metrics: metrics,
            dateData: dateData,
            highValueTransactions: highValueTransactions || [],
            highValueTransactionsUSD: highValueTransactionsUSD || [],
            sampleTransactions: sampleTransactions || [],
            processedRecords: processedRecords,
            completedAt: now,
            processingTime: processingTime || 0
        });

        // Update worker status
        const status = this.workerStatus.get(workerId);
        if (status) {
            status.isProcessing = false;
            status.processedBatches++;
            status.lastActivity = now;
        }

        // Update totals
        this.totalBatchesProcessed++;
        this.totalRecordsProcessed += processedRecords;

        // Update performance metrics for countdown timer
        this.updatePerformanceMetrics(batchIndex, processedRecords, processingTime || 0);

        console.log(`Worker ${workerId} completed batch ${batchIndex}: ${processedRecords} records processed in ${processingTime || 0}ms`);

        // Check if all batches are complete and update progress
        this.checkJobCompletion();
    }

    // Handle chunk processing (legacy support)
    handleChunkProcessed(workerId, result) {
        // NOTE: Metrics are now updated at the file level, not chunk level
        // This prevents duplicate accumulation of metrics
        console.log(`Worker ${workerId} completed chunk processing, metrics will be updated at file completion`);



        console.log(`Worker ${workerId} completed chunk processing`);
    }

    // Handle worker errors
    handleWorkerError(workerId, error) {
        console.error(`Worker ${workerId} error:`, error);

        const status = this.workerStatus.get(workerId);
        if (status) {
            status.isProcessing = false;
            status.hasError = true;
            status.lastError = error;
            status.lastActivity = Date.now();
        }

        // Notify main application
        if (window.app && window.app.showNotification) {
            window.app.showNotification(
                `Worker ${workerId} encountered an error. Processing may be slower.`,
                'warning',
                5000
            );
        }
    }

    // Handle worker status updates
    handleWorkerStatus(workerId, status) {
        const workerStatus = this.workerStatus.get(workerId);
        if (workerStatus) {
            Object.assign(workerStatus, status);
            workerStatus.lastActivity = Date.now();
        }
    }

    // Handle performance metrics
    handlePerformanceMetrics(workerId, metrics) {
        console.log(`Worker ${workerId} performance:`, metrics);
    }

    // Process file with parallel workers
    async processFileParallel(file, header, batches) {
        if (!this.isInitialized) {
            throw new Error('Workers not initialized');
        }

        if (batches.length === 0) {
            throw new Error('No batches to process');
        }

        console.log(`Starting parallel processing of ${batches.length} batches across ${this.workers.length} workers`);

        this.isProcessing = true;
        this.processingStartTime = Date.now();
        this.completedBatches.clear();
        this.totalBatchesProcessed = 0;
        this.totalRecordsProcessed = 0;

        // Initialize performance metrics for countdown timer
        this.performanceMetrics = {
            ...this.performanceMetrics,
            totalBatches: batches.length,
            processedBatches: 0,
            processingStarted: true,
            lastUpdateTime: this.processingStartTime,
            batchProcessingTimes: [],
            batchStartTimes: new Map(),
            workerPerformance: new Map()
        };

        // Reset aggregated results
        this.aggregatedMetrics = this.initializeAggregatedMetrics();
        this.aggregatedDateData = {};
        this.aggregatedHighValueTransactions = [];
        this.aggregatedHighValueTransactionsUSD = [];
        this.aggregatedSampleTransactions = [];

        try {
            // Distribute batches across workers
            await this.distributeBatches(file, header, batches);

            // Wait for all batches to complete
            await this.waitForCompletion(batches.length);

            // Aggregate results
            this.aggregateResults();

            const processingTime = Date.now() - this.processingStartTime;
            console.log(`Parallel processing completed in ${processingTime}ms: ${this.totalRecordsProcessed} records processed`);

            return {
                metrics: this.aggregatedMetrics,
                dateData: this.aggregatedDateData,
                highValueTransactions: this.aggregatedHighValueTransactions,
                highValueTransactionsUSD: this.aggregatedHighValueTransactionsUSD,
                sampleTransactions: this.aggregatedSampleTransactions,
                processingTime: processingTime,
                totalRecordsProcessed: this.totalRecordsProcessed
            };

        } catch (error) {
            console.error('Parallel processing failed:', error);
            throw error;
        } finally {
            this.isProcessing = false;
        }
    }

    // Distribute batches across workers
    async distributeBatches(file, header, batches) {
        const availableWorkers = this.getAvailableWorkers();

        if (availableWorkers.length === 0) {
            throw new Error('No available workers for processing');
        }

        console.log(`Distributing ${batches.length} batches across ${availableWorkers.length} workers`);

        // Distribute batches in round-robin fashion
        for (let i = 0; i < batches.length; i++) {
            const workerIndex = i % availableWorkers.length;
            const workerId = availableWorkers[workerIndex];
            const batch = batches[i];

            // Mark worker as processing
            const status = this.workerStatus.get(workerId);
            if (status) {
                status.isProcessing = true;
                status.lastActivity = Date.now();
            }

            // Send batch to worker
            this.workers[workerId].postMessage({
                action: 'processBatch',
                workerId: workerId,
                data: {
                    batchData: batch.data,
                    batchIndex: i,
                    header: header,
                    fileInfo: {
                        fileName: file.name,
                        fileSize: file.size
                    }
                }
            });

            console.log(`Assigned batch ${i} (${batch.data.length} records) to worker ${workerId}`);
        }
    }

    // Get available workers
    getAvailableWorkers() {
        const available = [];

        for (let i = 0; i < this.workers.length; i++) {
            const status = this.workerStatus.get(i);
            if (status && status.isReady && !status.hasError) {
                available.push(i);
            }
        }

        return available;
    }

    // Wait for all batches to complete
    async waitForCompletion(totalBatches) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkCompletion = () => {
                // Check if all batches are completed
                if (this.completedBatches.size >= totalBatches) {
                    resolve();
                    return;
                }

                // Check for timeout
                if (Date.now() - startTime > this.config.coordinationTimeout) {
                    reject(new Error('Processing timeout: Not all batches completed in time'));
                    return;
                }

                // Continue checking
                setTimeout(checkCompletion, 100);
            };

            checkCompletion();
        });
    }

    // Update performance metrics for countdown timer
    updatePerformanceMetrics(batchIndex, processedRecords, processingTime) {
        if (!this.config.performanceTrackingEnabled) return;

        const now = Date.now();

        // Update batch processing times
        this.performanceMetrics.batchProcessingTimes.push(processingTime);
        this.performanceMetrics.processedBatches++;

        // Keep only recent processing times for better accuracy (last 10 batches)
        if (this.performanceMetrics.batchProcessingTimes.length > 10) {
            this.performanceMetrics.batchProcessingTimes.shift();
        }

        // Calculate average processing speed
        if (this.performanceMetrics.batchProcessingTimes.length > 0) {
            const avgTime = this.performanceMetrics.batchProcessingTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.batchProcessingTimes.length;
            this.performanceMetrics.averageProcessingSpeed = avgTime;

            // Calculate records per second
            const avgRecordsPerBatch = this.config.batchSize;
            this.performanceMetrics.recordsPerSecond = avgRecordsPerBatch / (avgTime / 1000);
        }

        // Calculate estimated time remaining
        const remainingBatches = this.performanceMetrics.totalBatches - this.performanceMetrics.processedBatches;
        if (remainingBatches > 0 && this.performanceMetrics.averageProcessingSpeed > 0) {
            this.performanceMetrics.estimatedTimeRemaining = remainingBatches * this.performanceMetrics.averageProcessingSpeed;
        } else {
            this.performanceMetrics.estimatedTimeRemaining = 0;
        }

        this.performanceMetrics.lastUpdateTime = now;
    }

    // Check if current job is complete
    checkJobCompletion() {
        // This method is called after each batch completion
        // The actual completion check is handled in waitForCompletion

        // Update progress with countdown timer
        if (window.fileHandler && window.fileHandler.updateProgressWithTimer) {
            const progress = Math.round((this.totalBatchesProcessed / this.performanceMetrics.totalBatches) * 100);
            const statusText = `Parallel processing: ${this.totalRecordsProcessed} records processed`;
            const timeRemaining = this.performanceMetrics.estimatedTimeRemaining;

            window.fileHandler.updateProgressWithTimer(progress, statusText, timeRemaining);
        } else if (window.fileHandler && window.fileHandler.updateProgress) {
            // Fallback to original progress update
            const progress = Math.round((this.totalBatchesProcessed / this.performanceMetrics.totalBatches) * 100);
            window.fileHandler.updateProgress(progress, `Parallel processing: ${this.totalRecordsProcessed} records processed`);
        }
    }

    // Aggregate results from all completed batches
    aggregateResults() {
        console.log(`Aggregating results from ${this.completedBatches.size} completed batches`);

        // Sort batches by index to maintain order
        const sortedBatches = Array.from(this.completedBatches.entries())
            .sort(([a], [b]) => a - b);

        for (const [batchIndex, batchResult] of sortedBatches) {
            // Aggregate metrics
            this.accumulateMetrics(this.aggregatedMetrics, batchResult.metrics);

            // Aggregate date data
            this.accumulateDateData(this.aggregatedDateData, batchResult.dateData);

            // Collect high-value transactions
            if (batchResult.highValueTransactions) {
                this.aggregatedHighValueTransactions.push(...batchResult.highValueTransactions);
            }

            // Collect high-value USD transactions
            if (batchResult.highValueTransactionsUSD) {
                this.aggregatedHighValueTransactionsUSD.push(...batchResult.highValueTransactionsUSD);
            }

            // Collect sample transactions
            if (batchResult.sampleTransactions) {
                this.aggregatedSampleTransactions.push(...batchResult.sampleTransactions);
            }
        }

        // Update unique serial counts
        this.aggregatedMetrics.hocUniqueSerialCount = this.aggregatedMetrics.hocSerialNumbers.size;
        this.aggregatedMetrics.ibdUniqueSerialCount = this.aggregatedMetrics.ibdSerialNumbers.size;

        // Calculate total currency amounts from report-type specific amounts to prevent double counting
        // This ensures consistency with worker calculations and export logic
        const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);

        this.aggregatedMetrics.currencyAmounts.MMK = safeAdd(
            safeAdd(this.aggregatedMetrics.hocAmountMMK || 0, this.aggregatedMetrics.ibdAmountMMK || 0),
            this.aggregatedMetrics.wuAmountMMK || 0
        );
        this.aggregatedMetrics.currencyAmounts.USD = safeAdd(
            safeAdd(this.aggregatedMetrics.hocAmountUSD || 0, this.aggregatedMetrics.ibdAmountUSD || 0),
            this.aggregatedMetrics.wuAmountUSD || 0
        );

        // For other currencies (SGD, EUR, JPY, CNY, THB, INR), calculate from credit/debit amounts
        // since they don't have report-type specific tracking
        const otherCurrencies = ['SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        otherCurrencies.forEach(currency => {
            this.aggregatedMetrics.currencyAmounts[currency] = safeAdd(
                this.aggregatedMetrics.currencyCreditAmounts[currency] || 0,
                this.aggregatedMetrics.currencyDebitAmounts[currency] || 0
            );
        });

        // Limit sample transactions to prevent memory issues
        if (this.aggregatedSampleTransactions.length > 1000) {
            this.aggregatedSampleTransactions = this.aggregatedSampleTransactions.slice(0, 1000);
        }

        console.log(`Aggregation complete: ${this.aggregatedMetrics.totalTransactions} total transactions, ${this.aggregatedHighValueTransactions.length} high-value MMK transactions, ${this.aggregatedHighValueTransactionsUSD.length} high-value USD transactions`);
        console.log(`Corrected currency amounts - MMK: ${this.aggregatedMetrics.currencyAmounts.MMK.toLocaleString()}, USD: ${this.aggregatedMetrics.currencyAmounts.USD.toLocaleString()}`);
    }

    // Initialize aggregated metrics structure
    initializeAggregatedMetrics() {
        return {
            totalTransactions: 0,
            totalAmount: 0,
            hocCount: 0,
            hocAmount: 0,
            hocAmountMMK: 0,
            hocAmountUSD: 0,
            hocCreditCount: 0,
            hocCreditAmount: 0,
            hocCreditAmountMMK: 0,
            hocCreditAmountUSD: 0,
            hocDebitCount: 0,
            hocDebitAmount: 0,
            hocDebitAmountMMK: 0,
            hocDebitAmountUSD: 0,
            ibdCount: 0,
            ibdAmount: 0,
            ibdAmountMMK: 0,
            ibdAmountUSD: 0,
            ibdCreditCount: 0,
            ibdCreditAmount: 0,
            ibdCreditAmountMMK: 0,
            ibdCreditAmountUSD: 0,
            ibdDebitCount: 0,
            ibdDebitAmount: 0,
            ibdDebitAmountMMK: 0,
            ibdDebitAmountUSD: 0,
            hocUniqueSerialCount: 0,
            ibdUniqueSerialCount: 0,
            currencyCounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyCreditAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyDebitAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            // High-value transaction counts by currency
            highValueTransactionCounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            highValueTransactionCount: 0,
            highValueTransactionCountUSD: 0,
            hocSerialNumbers: new Set(),
            ibdSerialNumbers: new Set()
        };
    }

    // Accumulate metrics from batch into target metrics
    accumulateMetrics(targetMetrics, batchMetrics) {
        const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);

        targetMetrics.totalTransactions = safeAdd(targetMetrics.totalTransactions, batchMetrics.totalTransactions);
        targetMetrics.totalAmount = safeAdd(targetMetrics.totalAmount, batchMetrics.totalAmount);
        targetMetrics.hocCount = safeAdd(targetMetrics.hocCount, batchMetrics.hocCount);
        targetMetrics.hocAmount = safeAdd(targetMetrics.hocAmount, batchMetrics.hocAmount);
        targetMetrics.hocAmountMMK = safeAdd(targetMetrics.hocAmountMMK, batchMetrics.hocAmountMMK);
        targetMetrics.hocAmountUSD = safeAdd(targetMetrics.hocAmountUSD, batchMetrics.hocAmountUSD);
        targetMetrics.hocCreditCount = safeAdd(targetMetrics.hocCreditCount, batchMetrics.hocCreditCount);
        targetMetrics.hocCreditAmount = safeAdd(targetMetrics.hocCreditAmount, batchMetrics.hocCreditAmount);
        targetMetrics.hocCreditAmountMMK = safeAdd(targetMetrics.hocCreditAmountMMK, batchMetrics.hocCreditAmountMMK);
        targetMetrics.hocCreditAmountUSD = safeAdd(targetMetrics.hocCreditAmountUSD, batchMetrics.hocCreditAmountUSD);
        targetMetrics.hocCreditAmountCNY = safeAdd(targetMetrics.hocCreditAmountCNY, batchMetrics.hocCreditAmountCNY);
        targetMetrics.hocCreditAmountEUR = safeAdd(targetMetrics.hocCreditAmountEUR, batchMetrics.hocCreditAmountEUR);
        targetMetrics.hocCreditAmountINR = safeAdd(targetMetrics.hocCreditAmountINR, batchMetrics.hocCreditAmountINR);
        targetMetrics.hocCreditAmountJPY = safeAdd(targetMetrics.hocCreditAmountJPY, batchMetrics.hocCreditAmountJPY);
        targetMetrics.hocCreditAmountSGD = safeAdd(targetMetrics.hocCreditAmountSGD, batchMetrics.hocCreditAmountSGD);
        targetMetrics.hocCreditAmountTHB = safeAdd(targetMetrics.hocCreditAmountTHB, batchMetrics.hocCreditAmountTHB);
        targetMetrics.hocDebitCount = safeAdd(targetMetrics.hocDebitCount, batchMetrics.hocDebitCount);
        targetMetrics.hocDebitAmount = safeAdd(targetMetrics.hocDebitAmount, batchMetrics.hocDebitAmount);
        targetMetrics.hocDebitAmountMMK = safeAdd(targetMetrics.hocDebitAmountMMK, batchMetrics.hocDebitAmountMMK);
        targetMetrics.hocDebitAmountUSD = safeAdd(targetMetrics.hocDebitAmountUSD, batchMetrics.hocDebitAmountUSD);
        targetMetrics.hocDebitAmountCNY = safeAdd(targetMetrics.hocDebitAmountCNY, batchMetrics.hocDebitAmountCNY);
        targetMetrics.hocDebitAmountEUR = safeAdd(targetMetrics.hocDebitAmountEUR, batchMetrics.hocDebitAmountEUR);
        targetMetrics.hocDebitAmountINR = safeAdd(targetMetrics.hocDebitAmountINR, batchMetrics.hocDebitAmountINR);
        targetMetrics.hocDebitAmountJPY = safeAdd(targetMetrics.hocDebitAmountJPY, batchMetrics.hocDebitAmountJPY);
        targetMetrics.hocDebitAmountSGD = safeAdd(targetMetrics.hocDebitAmountSGD, batchMetrics.hocDebitAmountSGD);
        targetMetrics.hocDebitAmountTHB = safeAdd(targetMetrics.hocDebitAmountTHB, batchMetrics.hocDebitAmountTHB);
        targetMetrics.ibdCount = safeAdd(targetMetrics.ibdCount, batchMetrics.ibdCount);
        targetMetrics.ibdAmount = safeAdd(targetMetrics.ibdAmount, batchMetrics.ibdAmount);
        targetMetrics.ibdAmountMMK = safeAdd(targetMetrics.ibdAmountMMK, batchMetrics.ibdAmountMMK);
        targetMetrics.ibdAmountUSD = safeAdd(targetMetrics.ibdAmountUSD, batchMetrics.ibdAmountUSD);
        targetMetrics.ibdCreditCount = safeAdd(targetMetrics.ibdCreditCount, batchMetrics.ibdCreditCount);
        targetMetrics.ibdCreditAmount = safeAdd(targetMetrics.ibdCreditAmount, batchMetrics.ibdCreditAmount);
        targetMetrics.ibdCreditAmountMMK = safeAdd(targetMetrics.ibdCreditAmountMMK, batchMetrics.ibdCreditAmountMMK);
        targetMetrics.ibdCreditAmountUSD = safeAdd(targetMetrics.ibdCreditAmountUSD, batchMetrics.ibdCreditAmountUSD);
        targetMetrics.ibdCreditAmountCNY = safeAdd(targetMetrics.ibdCreditAmountCNY, batchMetrics.ibdCreditAmountCNY);
        targetMetrics.ibdCreditAmountEUR = safeAdd(targetMetrics.ibdCreditAmountEUR, batchMetrics.ibdCreditAmountEUR);
        targetMetrics.ibdCreditAmountINR = safeAdd(targetMetrics.ibdCreditAmountINR, batchMetrics.ibdCreditAmountINR);
        targetMetrics.ibdCreditAmountJPY = safeAdd(targetMetrics.ibdCreditAmountJPY, batchMetrics.ibdCreditAmountJPY);
        targetMetrics.ibdCreditAmountSGD = safeAdd(targetMetrics.ibdCreditAmountSGD, batchMetrics.ibdCreditAmountSGD);
        targetMetrics.ibdCreditAmountTHB = safeAdd(targetMetrics.ibdCreditAmountTHB, batchMetrics.ibdCreditAmountTHB);
        targetMetrics.ibdDebitCount = safeAdd(targetMetrics.ibdDebitCount, batchMetrics.ibdDebitCount);
        targetMetrics.ibdDebitAmount = safeAdd(targetMetrics.ibdDebitAmount, batchMetrics.ibdDebitAmount);
        targetMetrics.ibdDebitAmountMMK = safeAdd(targetMetrics.ibdDebitAmountMMK, batchMetrics.ibdDebitAmountMMK);
        targetMetrics.ibdDebitAmountUSD = safeAdd(targetMetrics.ibdDebitAmountUSD, batchMetrics.ibdDebitAmountUSD);
        targetMetrics.ibdDebitAmountCNY = safeAdd(targetMetrics.ibdDebitAmountCNY, batchMetrics.ibdDebitAmountCNY);
        targetMetrics.ibdDebitAmountEUR = safeAdd(targetMetrics.ibdDebitAmountEUR, batchMetrics.ibdDebitAmountEUR);
        targetMetrics.ibdDebitAmountINR = safeAdd(targetMetrics.ibdDebitAmountINR, batchMetrics.ibdDebitAmountINR);
        targetMetrics.ibdDebitAmountJPY = safeAdd(targetMetrics.ibdDebitAmountJPY, batchMetrics.ibdDebitAmountJPY);
        targetMetrics.ibdDebitAmountSGD = safeAdd(targetMetrics.ibdDebitAmountSGD, batchMetrics.ibdDebitAmountSGD);
        targetMetrics.ibdDebitAmountTHB = safeAdd(targetMetrics.ibdDebitAmountTHB, batchMetrics.ibdDebitAmountTHB);

        // Safe currency accumulation with null checks for ALL supported currencies
        const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

        if (batchMetrics.currencyCounts) {
            supportedCurrencies.forEach(currency => {
                targetMetrics.currencyCounts[currency] = safeAdd(targetMetrics.currencyCounts[currency], batchMetrics.currencyCounts[currency]);
            });
        }
        if (batchMetrics.currencyAmounts) {
            supportedCurrencies.forEach(currency => {
                targetMetrics.currencyAmounts[currency] = safeAdd(targetMetrics.currencyAmounts[currency], batchMetrics.currencyAmounts[currency]);
            });
        }
        if (batchMetrics.currencyCreditAmounts) {
            supportedCurrencies.forEach(currency => {
                targetMetrics.currencyCreditAmounts[currency] = safeAdd(targetMetrics.currencyCreditAmounts[currency], batchMetrics.currencyCreditAmounts[currency]);
            });
        }
        if (batchMetrics.currencyDebitAmounts) {
            supportedCurrencies.forEach(currency => {
                targetMetrics.currencyDebitAmounts[currency] = safeAdd(targetMetrics.currencyDebitAmounts[currency], batchMetrics.currencyDebitAmounts[currency]);
            });
        }

        targetMetrics.highValueTransactionCount = safeAdd(targetMetrics.highValueTransactionCount, batchMetrics.highValueTransactionCount);
        targetMetrics.highValueTransactionCountUSD = safeAdd(targetMetrics.highValueTransactionCountUSD, batchMetrics.highValueTransactionCountUSD);

        // Accumulate unique serial numbers (handle both Sets and Arrays)
        if (batchMetrics.hocSerialNumbers) {
            if (Array.isArray(batchMetrics.hocSerialNumbers)) {
                batchMetrics.hocSerialNumbers.forEach(sn => targetMetrics.hocSerialNumbers.add(sn));
            } else if (batchMetrics.hocSerialNumbers.forEach) {
                batchMetrics.hocSerialNumbers.forEach(sn => targetMetrics.hocSerialNumbers.add(sn));
            }
        }
        if (batchMetrics.ibdSerialNumbers) {
            if (Array.isArray(batchMetrics.ibdSerialNumbers)) {
                batchMetrics.ibdSerialNumbers.forEach(sn => targetMetrics.ibdSerialNumbers.add(sn));
            } else if (batchMetrics.ibdSerialNumbers.forEach) {
                batchMetrics.ibdSerialNumbers.forEach(sn => targetMetrics.ibdSerialNumbers.add(sn));
            }
        }
    }

    // Accumulate date data from batch into target date data
    accumulateDateData(targetDateData, batchDateData) {
        if (!batchDateData || Object.keys(batchDateData).length === 0) return;

        const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);

        for (const [date, data] of Object.entries(batchDateData)) {
            if (!targetDateData[date]) {
                targetDateData[date] = {
                    hocCreditCount: 0, hocCreditAmountMMK: 0, hocCreditAmountUSD: 0,
                    hocDebitCount: 0, hocDebitAmountMMK: 0, hocDebitAmountUSD: 0,
                    ibdCreditCount: 0, ibdCreditAmountMMK: 0, ibdCreditAmountUSD: 0,
                    ibdDebitCount: 0, ibdDebitAmountMMK: 0, ibdDebitAmountUSD: 0,
                    wuCreditCount: 0, wuCreditAmountMMK: 0, wuCreditAmountUSD: 0,
                    wuDebitCount: 0, wuDebitAmountMMK: 0, wuDebitAmountUSD: 0,
                    hocCreditAmount: 0, hocDebitAmount: 0, ibdCreditAmount: 0, ibdDebitAmount: 0
                };
            }

            // Accumulate all fields
            targetDateData[date].hocCreditCount = safeAdd(targetDateData[date].hocCreditCount, data.hocCreditCount);
            targetDateData[date].hocCreditAmountMMK = safeAdd(targetDateData[date].hocCreditAmountMMK, data.hocCreditAmountMMK);
            targetDateData[date].hocCreditAmountUSD = safeAdd(targetDateData[date].hocCreditAmountUSD, data.hocCreditAmountUSD);
            targetDateData[date].hocDebitCount = safeAdd(targetDateData[date].hocDebitCount, data.hocDebitCount);
            targetDateData[date].hocDebitAmountMMK = safeAdd(targetDateData[date].hocDebitAmountMMK, data.hocDebitAmountMMK);
            targetDateData[date].hocDebitAmountUSD = safeAdd(targetDateData[date].hocDebitAmountUSD, data.hocDebitAmountUSD);
            targetDateData[date].ibdCreditCount = safeAdd(targetDateData[date].ibdCreditCount, data.ibdCreditCount);
            targetDateData[date].ibdCreditAmountMMK = safeAdd(targetDateData[date].ibdCreditAmountMMK, data.ibdCreditAmountMMK);
            targetDateData[date].ibdCreditAmountUSD = safeAdd(targetDateData[date].ibdCreditAmountUSD, data.ibdCreditAmountUSD);
            targetDateData[date].ibdDebitCount = safeAdd(targetDateData[date].ibdDebitCount, data.ibdDebitCount);
            targetDateData[date].ibdDebitAmountMMK = safeAdd(targetDateData[date].ibdDebitAmountMMK, data.ibdDebitAmountMMK);
            targetDateData[date].ibdDebitAmountUSD = safeAdd(targetDateData[date].ibdDebitAmountUSD, data.ibdDebitAmountUSD);
            targetDateData[date].wuCreditCount = safeAdd(targetDateData[date].wuCreditCount, data.wuCreditCount);
            targetDateData[date].wuCreditAmountMMK = safeAdd(targetDateData[date].wuCreditAmountMMK, data.wuCreditAmountMMK);
            targetDateData[date].wuCreditAmountUSD = safeAdd(targetDateData[date].wuCreditAmountUSD, data.wuCreditAmountUSD);
            targetDateData[date].wuDebitCount = safeAdd(targetDateData[date].wuDebitCount, data.wuDebitCount);
            targetDateData[date].wuDebitAmountMMK = safeAdd(targetDateData[date].wuDebitAmountMMK, data.wuDebitAmountMMK);
            targetDateData[date].wuDebitAmountUSD = safeAdd(targetDateData[date].wuDebitAmountUSD, data.wuDebitAmountUSD);
            // Legacy fields
            targetDateData[date].hocCreditAmount = safeAdd(targetDateData[date].hocCreditAmount, data.hocCreditAmount);
            targetDateData[date].hocDebitAmount = safeAdd(targetDateData[date].hocDebitAmount, data.hocDebitAmount);
            targetDateData[date].ibdCreditAmount = safeAdd(targetDateData[date].ibdCreditAmount, data.ibdCreditAmount);
            targetDateData[date].ibdDebitAmount = safeAdd(targetDateData[date].ibdDebitAmount, data.ibdDebitAmount);
        }
    }

    // Get processing status
    getProcessingStatus() {
        return {
            isInitialized: this.isInitialized,
            isProcessing: this.isProcessing,
            workerCount: this.workers.length,
            availableWorkers: this.getAvailableWorkers().length,
            totalBatchesProcessed: this.totalBatchesProcessed,
            totalRecordsProcessed: this.totalRecordsProcessed,
            processingTime: this.processingStartTime ? Date.now() - this.processingStartTime : 0
        };
    }

    // Cleanup workers and resources
    cleanup() {
        console.log('Cleaning up parallel processing coordinator...');

        // Terminate all workers
        for (let i = 0; i < this.workers.length; i++) {
            if (this.workers[i]) {
                this.workers[i].terminate();
            }
        }

        // Clear all data structures
        this.workers = [];
        this.workerStatus.clear();
        this.activeJobs.clear();
        this.completedBatches.clear();
        this.isInitialized = false;
        this.isProcessing = false;

        // Clear aggregated results
        this.aggregatedMetrics = null;
        this.aggregatedDateData = {};
        this.aggregatedHighValueTransactions = [];
        this.aggregatedHighValueTransactionsUSD = [];
        this.aggregatedSampleTransactions = [];

        console.log('Parallel processing coordinator cleanup complete');
    }

    // Check if parallel processing is available
    static isAvailable() {
        return window.Worker &&
               window.APP_CONSTANTS?.WORKER.PARALLEL_PROCESSING_ENABLED !== false;
    }

    // Diagnostic function to test worker creation
    static async testWorkerCreation() {
        console.log('Testing worker creation...');

        try {
            // Test if Worker constructor is available
            if (!window.Worker) {
                throw new Error('Web Workers not supported in this browser');
            }

            // Test worker script loading with robust path resolution
            let worker;
            const workerPaths = [
                './workers/parallelCsvWorker.js',
                'workers/parallelCsvWorker.js',
                '/workers/parallelCsvWorker.js',
                `${window.location.pathname.replace(/\/[^\/]*$/, '')}/workers/parallelCsvWorker.js`
            ];

            let lastError = null;
            for (const path of workerPaths) {
                try {
                    worker = new Worker(path);
                    console.log(`✅ Worker created successfully with path: ${path}`);
                    this.workerScriptPath = path; // Store successful path for future use
                    break;
                } catch (e) {
                    lastError = e;
                    console.warn(`❌ Failed with ${path}:`, e.message);
                }
            }

            if (!worker) {
                throw new Error(`Worker script not found in any location: ${lastError?.message}`);
            }

            // Test worker communication
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    worker.terminate();
                    reject(new Error('Worker communication test timeout'));
                }, 5000);

                worker.onmessage = (e) => {
                    clearTimeout(timeout);
                    worker.terminate();
                    console.log('✅ Worker communication test successful:', e.data);
                    resolve(true);
                };

                worker.onerror = (error) => {
                    clearTimeout(timeout);
                    worker.terminate();
                    reject(new Error(`Worker error: ${error.message}`));
                };

                // Send test message
                worker.postMessage({
                    action: 'initWorker',
                    workerId: 'test',
                    data: {
                        workerId: 'test',
                        workerConfig: {
                            batchSize: 1000,
                            memoryLimit: 1024 * 1024,
                            gcThreshold: 0.8,
                            sampleRate: 0.001
                        }
                    }
                });
            });

        } catch (error) {
            console.error('❌ Worker creation test failed:', error);
            throw error;
        }
    }
}

// Export for use in other modules
window.ParallelProcessingCoordinator = ParallelProcessingCoordinator;
