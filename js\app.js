/**
 * Main Application Logic
 * Initializes the application and coordinates between modules
 */

// Main App class
class App {
    constructor() {
        // Prevent multiple instances
        if (window.app && window.app.isInitialized) {
            console.warn('App instance already exists and is initialized');
            return window.app;
        }

        // Initialize application state
        this.isInitialized = false;
        this.notificationTimeout = null;
        this.performanceEventsSetup = false; // Track performance event setup

        // Set up global error handling
        this.setupGlobalErrorHandling();

        // Initialize the application when the DOM is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.init();
            });
        } else {
            // DOM is already loaded
            setTimeout(() => this.init(), 0);
        }
    }

    // Set up global error handling to prevent application crashes
    setupGlobalErrorHandling() {
        // Handle uncaught JavaScript errors
        window.addEventListener('error', (event) => {
            console.error('Global error caught:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });

            // Show user-friendly error message
            this.showNotification(
                'An unexpected error occurred. The application will continue to function, but some features may be affected.',
                'error',
                5000
            );

            // Prevent the error from crashing the application
            return true;
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);

            // Show user-friendly error message
            this.showNotification(
                'A background operation failed. Please try again or refresh the page if issues persist.',
                'warning',
                4000
            );

            // Prevent the rejection from causing issues
            event.preventDefault();
        });

        // Handle resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError('Resource loading error', {
                    element: event.target.tagName,
                    source: event.target.src || event.target.href,
                    message: 'Failed to load resource'
                });

                // Only show notification for critical resources
                if (event.target.tagName === 'SCRIPT') {
                    this.showNotification(
                        'Failed to load a required script. Some features may not work correctly.',
                        'warning',
                        4000
                    );
                }
            }
        }, true);

        this.logInfo('Global error handling initialized');
    }

    // Show notification message
    showNotification(message, type = 'success', duration = 3000) {
        // Clear any existing notification
        this.clearNotification();

        // Create notification element if it doesn't exist
        let notification = document.getElementById('notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification';
            notification.className = 'notification';

            const messageElement = document.createElement('p');
            messageElement.className = 'notification-message';
            notification.appendChild(messageElement);

            const closeButton = document.createElement('button');
            closeButton.className = 'notification-close';
            closeButton.innerHTML = '&times;';
            closeButton.addEventListener('click', () => this.clearNotification());
            notification.appendChild(closeButton);

            document.body.appendChild(notification);
        }

        // Set notification type and message
        notification.className = `notification ${type}`;
        notification.querySelector('.notification-message').textContent = message;

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto-hide after duration
        this.notificationTimeout = setTimeout(() => {
            this.clearNotification();
        }, duration);
    }

    // Clear notification
    clearNotification() {
        const notification = document.getElementById('notification');
        if (notification) {
            notification.classList.remove('show');
        }

        if (this.notificationTimeout) {
            clearTimeout(this.notificationTimeout);
            this.notificationTimeout = null;
        }
    }

    // Initialize the application
    init() {
        if (this.isInitialized) return;

        // Check if required modules are loaded with more detailed logging
        const moduleStatus = {
            fileHandler: !!window.fileHandler,
            dataProcessor: !!window.dataProcessor,
            currencyUtils: !!window.CurrencyUtils,
            constants: !!window.FIELD_MAPPINGS
        };

        const missingModules = Object.entries(moduleStatus)
            .filter(([name, loaded]) => !loaded)
            .map(([name]) => name);

        if (missingModules.length > 0) {
            this.logError('Required modules are not loaded. Missing:', moduleStatus);

            // Retry initialization after a short delay, but with a maximum retry count
            if (!this.initRetryCount) this.initRetryCount = 0;
            this.initRetryCount++;

            if (this.initRetryCount < 10) { // Maximum 10 retries (5 seconds)
                setTimeout(() => {
                    if (!this.isInitialized) {
                        this.init();
                    }
                }, 500);
                return;
            } else {
                this.logError('Failed to load required modules after 10 retries. Proceeding with partial initialization.');
                // Continue with partial initialization
            }
        }

        // Check critical dependencies
        if (!this.checkCriticalDependencies()) {
            this.logError('Critical dependencies missing. Some features may not work correctly.');
            this.showNotification(
                'Some external libraries failed to load. Please refresh the page or check your internet connection.',
                'warning',
                10000
            );
        }

        // Set up navigation
        this.setupNavigation();

        // Initialize TTR Monitoring
        this.initTTRMonitoring();

        // Initialize High-Value Export
        this.initHighValueExport();

        // Initialize Transaction Breakdown Export
        this.initTransactionBreakdownExport();

        // Initialize TTR Summary Export
        this.initTTRSummaryExport();

        // Initialize High-Value Card Click Handlers
        this.initHighValueCardHandlers();

        // Set up high-value alert testing
        this.setupHighValueAlertTesting();

        // Set up file processing event listeners
        this.setupFileProcessingEventListeners();

        // Set up memory monitoring
        this.setupMemoryMonitoring();

        // Initialize performance monitoring
        this.initPerformanceMonitoring();

        // Mark as initialized
        this.isInitialized = true;
        this.logInfo('Financial Transaction Dashboard V2.5.7 initialized successfully.');

        // Initialize customer analytics module with retry mechanism
        this.initializeCustomerAnalytics();
    }

    // Initialize customer analytics with retry mechanism
    initializeCustomerAnalytics() {
        const maxRetries = 5;
        let retryCount = 0;

        const tryInitialize = () => {
            if (window.customerAnalytics) {
                try {
                    window.customerAnalytics.initialize();
                    this.logInfo('Customer Analytics module initialized successfully');

                    // Add global debug access
                    window.debugCA = () => window.customerAnalytics.debugCustomerAnalytics();

                    return true;
                } catch (error) {
                    this.logError('Error initializing Customer Analytics:', error);
                    return false;
                }
            } else {
                retryCount++;
                if (retryCount < maxRetries) {
                    this.logWarn(`Customer Analytics module not available, retrying... (${retryCount}/${maxRetries})`);
                    setTimeout(tryInitialize, 500);
                } else {
                    this.logError('Customer Analytics module failed to initialize after maximum retries');
                }
                return false;
            }
        };

        tryInitialize();

        this.logDebug('Available modules:', {
            fileHandler: !!window.fileHandler,
            dataProcessor: !!window.dataProcessor,
            ttrMonitoring: !!window.ttrMonitoring,
            highValueExport: !!window.highValueExport,
            transactionExport: !!window.transactionExport,
            ttrSummaryExport: !!window.ttrSummaryExport,
            performanceMonitor: !!window.performanceMonitor,
            customerAnalytics: !!window.customerAnalytics,
            app: !!window.app
        });
    }

    // Set up navigation system
    setupNavigation() {
        // Get navigation links
        const navLinks = document.querySelectorAll('.nav-link');

        // Add click event listeners
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                this.navigateToPage(page);
            });
        });

        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            const page = e.state?.page || 'dashboard';
            this.navigateToPage(page, false);
        });

        // Initialize with current hash or default to dashboard
        const initialPage = window.location.hash.replace('#', '') || 'dashboard';
        this.navigateToPage(initialPage, false);
    }

    // Navigate to a specific page
    navigateToPage(page, updateHistory = true) {
        // Hide all pages
        const pages = document.querySelectorAll('.page-content');
        pages.forEach(p => p.classList.remove('active'));

        // Show target page
        const targetPage = document.getElementById(`${page}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // Update navigation active state
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.dataset.page === page) {
                link.classList.add('active');
            }
        });

        // Update URL and browser history
        if (updateHistory) {
            const url = page === 'dashboard' ? '#dashboard' : `#${page}`;
            window.history.pushState({ page }, '', url);
        }

        // Update page title
        const pageTitle = page === 'dashboard' ? 'Dashboard' :
                         page === 'ttr-monitoring' ? 'Advanced TTR Monitoring' :
                         'Financial Transaction Dashboard';
        document.title = `${pageTitle} - Financial Transaction Dashboard V2.5.4`;

        console.log(`Navigated to page: ${page}`);
    }

    // Initialize TTR Monitoring
    initTTRMonitoring() {
        // TTR Monitoring will be initialized by its own constructor
        // We just need to make sure it's available globally
        if (window.TTRMonitoring) {
            try {
                window.ttrMonitoring = new window.TTRMonitoring();
                console.log('TTR Monitoring initialized successfully from app.js');

                // Set up integration with file processing
                this.setupTTRIntegration();

                // Ensure TTR page is properly initialized when navigated to
                this.setupTTRPageInitialization();
            } catch (error) {
                console.error('Error initializing TTR Monitoring:', error);
                setTimeout(() => this.initTTRMonitoring(), 1000);
            }
        } else {
            console.warn('TTR Monitoring not available, will retry...');
            setTimeout(() => this.initTTRMonitoring(), 1000);
        }
    }

    // Set up TTR page initialization
    setupTTRPageInitialization() {
        // Listen for navigation to TTR page to ensure proper initialization
        const originalNavigateToPage = this.navigateToPage.bind(this);
        this.navigateToPage = (page, updateHistory = true) => {
            originalNavigateToPage(page, updateHistory);

            // If navigating to TTR page, ensure it's properly initialized
            if (page === 'ttr-monitoring' && window.ttrMonitoring) {
                setTimeout(() => {
                    console.log('Ensuring TTR page is properly initialized...');
                    window.ttrMonitoring.initializeTTRPageElements();
                    window.ttrMonitoring.updateUI();
                    window.ttrMonitoring.forceUIRefresh();
                }, 100);
            }
        };
    }

    // Set up TTR integration with existing file processing
    setupTTRIntegration() {
        // TTR integration is now handled directly in fileHandler.js
        // through event dispatching when files are processed/removed
        console.log('TTR integration setup completed - using fileHandler event dispatching');
    }

    // Initialize High-Value Export functionality
    initHighValueExport() {
        if (window.HighValueExport) {
            window.highValueExport = new window.HighValueExport();

            // Set up export button event listener
            const exportBtn = document.getElementById('exportHighValueBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', () => {
                    this.handleHighValueExport();
                });

                // Initially hide the button until high-value transactions are available
                this.updateHighValueExportButtonVisibility();

                console.log('High-Value Export button initialized');
            } else {
                console.warn('High-Value Export button not found');
            }
        } else {
            console.warn('HighValueExport class not available, will retry...');
            setTimeout(() => this.initHighValueExport(), 1000);
        }
    }

    // Initialize Transaction Breakdown Export functionality
    initTransactionBreakdownExport() {
        if (window.transactionExport) {
            // Set up export button event listener
            const exportBtn = document.getElementById('exportTransactionBreakdownBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', () => {
                    this.handleTransactionBreakdownExport();
                });

                // Initially hide the button until transactions are available
                this.updateTransactionBreakdownExportButtonVisibility();

                console.log('Transaction Breakdown Export button initialized');
            } else {
                console.warn('Transaction Breakdown Export button not found');
            }
        } else {
            console.warn('TransactionExport class not available, will retry...');
            setTimeout(() => this.initTransactionBreakdownExport(), 1000);
        }
    }

    // Initialize TTR Summary Export functionality
    initTTRSummaryExport() {
        if (window.ttrSummaryExport) {
            // Set up export button event listener
            const exportBtn = document.getElementById('exportTTRSummaryBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', () => {
                    this.handleTTRSummaryExport();
                });

                // Initially hide the button until TTR data is available
                this.updateTTRSummaryExportButtonVisibility();

                console.log('TTR Summary Export button initialized');
            } else {
                console.warn('TTR Summary Export button not found');
            }
        } else {
            console.warn('TTRSummaryExport class not available, will retry...');
            setTimeout(() => this.initTTRSummaryExport(), 1000);
        }
    }

    // Initialize High-Value Card Click Handlers
    initHighValueCardHandlers() {
        // Set up click handlers for high-value transaction cards
        const mmkCard = document.querySelector('.high-value-card');
        const usdCard = document.querySelector('.high-value-card-usd');

        if (mmkCard) {
            mmkCard.addEventListener('click', () => {
                if (window.dataProcessor) {
                    window.dataProcessor.showAllHighValueTransactions('MMK');
                }
            });
            console.log('MMK high-value card click handler initialized');
        } else {
            console.warn('MMK high-value card not found');
        }

        if (usdCard) {
            usdCard.addEventListener('click', () => {
                if (window.dataProcessor) {
                    window.dataProcessor.showAllHighValueTransactions('USD');
                }
            });
            console.log('USD high-value card click handler initialized');
        } else {
            console.warn('USD high-value card not found');
        }

        console.log('High-Value card click handlers initialized');
    }

    // Set up high-value alert testing functionality
    setupHighValueAlertTesting() {
        const testBtn = document.getElementById('testHighValueAlertsBtn');
        if (testBtn) {
            testBtn.addEventListener('click', () => {
                this.handleHighValueAlertTest();
            });
            console.log('High-Value alert testing button initialized');
        } else {
            console.warn('High-Value alert testing button not found');
        }
    }

    // Handle high-value alert test button click
    handleHighValueAlertTest() {
        if (!window.dataProcessor) {
            this.showNotification('DataProcessor not available for testing', 'error');
            return;
        }

        try {
            // Check if the manual trigger method exists
            if (typeof window.dataProcessor.triggerHighValueAlerts === 'function') {
                console.log('Triggering high-value alerts manually...');
                window.dataProcessor.triggerHighValueAlerts();

                // Show feedback to user
                const mmkCount = window.dataProcessor.summaryMetrics?.highValueTransactionCount || 0;
                const usdCount = window.dataProcessor.summaryMetrics?.highValueTransactionCountUSD || 0;

                this.showNotification(
                    `🔔 High-Value Alert Test: ${mmkCount} MMK transactions (≥1B) + ${usdCount} USD transactions (≥$10K) detected. Check console for detailed logs.`,
                    'info',
                    6000
                );
            } else {
                this.showNotification('High-value alert trigger method not available', 'error');
            }
        } catch (error) {
            console.error('Error testing high-value alerts:', error);
            this.showNotification('Error testing high-value alerts. Check console for details.', 'error');
        }
    }

    // Update the visibility of the high-value export button based on available data
    updateHighValueExportButtonVisibility() {
        const exportBtn = document.getElementById('exportHighValueBtn');
        if (!exportBtn || !window.dataProcessor) {
            return;
        }

        try {
            // Check if there are any high-value transactions available
            const hasHighValueTransactions = window.dataProcessor.fileHighValueTransactions &&
                                           window.dataProcessor.fileHighValueTransactions.size > 0;

            const summaryMetrics = window.dataProcessor.getSummaryMetrics();
            const hasHighValueCount = summaryMetrics && summaryMetrics.highValueTransactionCount > 0;

            if (hasHighValueTransactions || hasHighValueCount) {
                exportBtn.style.display = 'flex';
                exportBtn.style.opacity = '1';
                console.log('High-Value Export button shown - transactions available');
            } else {
                exportBtn.style.display = 'none';
                console.log('High-Value Export button hidden - no transactions available');
            }
        } catch (error) {
            console.error('Error updating high-value export button visibility:', error);
            // Default to showing the button if there's an error
            exportBtn.style.display = 'flex';
        }
    }

    // Update the visibility of the transaction breakdown export button based on available data
    updateTransactionBreakdownExportButtonVisibility() {
        const exportBtn = document.getElementById('exportTransactionBreakdownBtn');
        if (!exportBtn || !window.dataProcessor) {
            return;
        }

        try {
            // Check if there are any transactions available
            const summaryMetrics = window.dataProcessor.getSummaryMetrics();
            const hasTransactions = summaryMetrics && summaryMetrics.totalTransactions > 0;
            const hasRawData = window.dataProcessor.rawData && window.dataProcessor.rawData.length > 0;

            if (hasTransactions || hasRawData) {
                exportBtn.style.display = 'flex';
                exportBtn.style.opacity = '1';
                console.log('Transaction Breakdown Export button shown - transactions available');
            } else {
                exportBtn.style.display = 'none';
                console.log('Transaction Breakdown Export button hidden - no transactions available');
            }
        } catch (error) {
            console.error('Error updating transaction breakdown export button visibility:', error);
            // Default to hiding the button if there's an error
            exportBtn.style.display = 'none';
        }
    }

    // Update the visibility of the TTR summary export button based on available data
    updateTTRSummaryExportButtonVisibility() {
        const exportBtn = document.getElementById('exportTTRSummaryBtn');
        if (!exportBtn || !window.ttrSummaryExport) {
            return;
        }

        try {
            // Quick check for basic requirements before calling hasTTRData
            if (!window.dataProcessor || !window.fileHandler) {
                exportBtn.style.display = 'none';
                return;
            }

            // Check if there is TTR data available
            const hasTTRData = window.ttrSummaryExport.hasTTRData();

            if (hasTTRData) {
                exportBtn.style.display = 'flex';
                exportBtn.style.opacity = '1';
                console.log('TTR Summary Export button shown - TTR data available');
            } else {
                exportBtn.style.display = 'none';
                console.log('TTR Summary Export button hidden - no TTR data available');
            }
        } catch (error) {
            console.error('Error updating TTR summary export button visibility:', error);
            // Default to hiding the button if there's an error
            exportBtn.style.display = 'none';
        }
    }

    // Set up file processing event listeners
    setupFileProcessingEventListeners() {
        // Listen for file processing completion to update export button visibility
        document.addEventListener('fileProcessingEnd', () => {
            // Small delay to ensure data processing is complete
            setTimeout(() => {
                this.updateHighValueExportButtonVisibility();
                this.updateTransactionBreakdownExportButtonVisibility();
                this.updateTTRSummaryExportButtonVisibility();
            }, 500);
        });

        // Also listen for data processor updates
        document.addEventListener('dataProcessorUpdated', () => {
            this.updateHighValueExportButtonVisibility();
            this.updateTransactionBreakdownExportButtonVisibility();
            this.updateTTRSummaryExportButtonVisibility();
        });

        // Listen for TTR-specific updates
        document.addEventListener('ttrSummaryUpdated', () => {
            console.log('TTR Summary updated event received - updating export button visibility');
            this.updateTTRSummaryExportButtonVisibility();
        });

        console.log('File processing event listeners set up');
    }

    // Handle high-value export button click
    async handleHighValueExport() {
        if (!window.highValueExport) {
            this.showNotification('High-Value Export not available', 'error');
            return;
        }

        try {
            // Check if there are any high-value transactions to export
            const highValueData = window.highValueExport.getHighValueTransactionData();

            if (!highValueData || highValueData.transactions.length === 0) {
                this.showNotification('No high-value transactions to export. Please upload CSV files with transactions ≥1B MMK or ≥10K USD.', 'warning', 4000);
                return;
            }

            // Show loading state
            const exportBtn = document.getElementById('exportHighValueBtn');
            if (exportBtn) {
                const originalText = exportBtn.innerHTML;
                exportBtn.innerHTML = '<span class="export-icon"></span> Exporting...';
                exportBtn.disabled = true;

                try {
                    // Check if streaming export will be used
                    const isLargeDataset = highValueData.transactions.length > 10000;
                    if (isLargeDataset) {
                        this.showNotification(
                            `Large dataset detected (${highValueData.transactions.length} records). Using streaming export to optimize memory usage...`,
                            'info',
                            3000
                        );
                    }

                    // Perform async export
                    const result = await window.highValueExport.exportToExcel();

                    // Show success notification
                    this.showNotification(
                        `Successfully exported ${result.recordCount} high-value transactions to ${result.filename}${isLargeDataset ? ' (streaming mode)' : ''}`,
                        'success',
                        5000
                    );

                    console.log('High-value export completed:', result);

                } catch (error) {
                    console.error('Error during high-value export:', error);

                    // Try CSV fallback
                    try {
                        const csvResult = await window.highValueExport.exportToCSV();
                        this.showNotification(
                            `Excel export failed. Exported ${csvResult.recordCount} transactions to CSV: ${csvResult.filename}`,
                            'warning',
                            5000
                        );
                    } catch (csvError) {
                        console.error('CSV export also failed:', csvError);
                        this.showNotification('Export failed. Please try again.', 'error');
                    }
                } finally {
                    // Restore button state
                    exportBtn.innerHTML = originalText;
                    exportBtn.disabled = false;
                }
            }

        } catch (error) {
            console.error('Error handling high-value export:', error);
            this.showNotification('Error preparing export. Please try again.', 'error');
        }
    }

    // Handle transaction breakdown export button click
    async handleTransactionBreakdownExport() {
        if (!window.transactionExport) {
            this.showNotification('Transaction Export not available', 'error');
            return;
        }

        try {
            // Check if there are any transactions to export
            const transactionData = window.transactionExport.getTransactionBreakdownData();

            if (!transactionData || !window.transactionExport.hasTransactionData(transactionData)) {
                this.showNotification('No transaction data to export. Please upload CSV files first.', 'warning', 4000);
                return;
            }

            const transactionCount = window.transactionExport.getTransactionCount(transactionData);

            // Show loading notification for large datasets
            if (transactionCount > 5000) {
                this.showNotification(
                    `Large dataset detected (${transactionCount} records). Export may take a moment...`,
                    'info',
                    3000
                );
            }

            // Perform async export
            const result = await window.transactionExport.exportTransactionBreakdownToExcel();

            console.log('Transaction breakdown export completed:', result);

        } catch (error) {
            console.error('Error handling transaction breakdown export:', error);
            this.showNotification('Export failed. Please try again.', 'error');
        }
    }

    // Handle TTR summary export button click
    async handleTTRSummaryExport() {
        if (!window.ttrSummaryExport) {
            this.showNotification('TTR Summary Export not available', 'error');
            return;
        }

        try {
            // Check if there is TTR data to export
            const hasTTRData = window.ttrSummaryExport.hasTTRData();

            if (!hasTTRData) {
                this.showNotification('No TTR summary data to export. Please upload CSV files first.', 'warning', 4000);
                return;
            }

            // Get TTR data count for user feedback
            const ttrData = window.ttrSummaryExport.getTTRSummaryData();
            const recordCount = ttrData ? ttrData.length : 0;

            // Show loading notification for larger datasets
            if (recordCount > 10) {
                this.showNotification(
                    `Exporting ${recordCount} TTR summary records...`,
                    'info',
                    3000
                );
            }

            // Perform async export
            const result = await window.ttrSummaryExport.exportTTRSummaryToExcel();

            console.log('TTR summary export completed:', result);

        } catch (error) {
            console.error('Error handling TTR summary export:', error);
            this.showNotification('TTR Summary export failed. Please try again.', 'error');
        }
    }

    // Set up memory monitoring
    setupMemoryMonitoring() {
        // Check if performance memory API is available
        if (window.performance && window.performance.memory) {
            // Create memory monitor element
            const memoryMonitor = document.createElement('div');
            memoryMonitor.id = 'memoryMonitor';
            memoryMonitor.className = 'memory-monitor';
            memoryMonitor.innerHTML = 'Memory: Monitoring...';
            document.body.appendChild(memoryMonitor);

            // Update memory usage every 5 seconds
            this.memoryMonitorInterval = setInterval(() => {
                this.updateMemoryUsage();
            }, 5000);

            // Initial update
            this.updateMemoryUsage();

            console.log('Memory monitoring initialized');
        } else {
            console.log('Performance memory API not available');
        }
    }

    // Update memory usage display with enhanced monitoring
    updateMemoryUsage() {
        if (window.performance && window.performance.memory) {
            const memoryInfo = window.performance.memory;
            const usedHeapSize = this.formatBytes(memoryInfo.usedJSHeapSize);
            const heapLimit = this.formatBytes(memoryInfo.jsHeapSizeLimit);
            const usageRatio = memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit;

            const memoryMonitor = document.getElementById('memoryMonitor');
            if (memoryMonitor) {
                memoryMonitor.innerHTML = `Memory: ${usedHeapSize} / ${heapLimit} (${Math.round(usageRatio * 100)}%)`;

                // Enhanced memory pressure handling
                if (usageRatio > 0.9) {
                    memoryMonitor.className = 'memory-monitor critical';
                    this.handleCriticalMemoryPressure();
                } else if (usageRatio > 0.8) {
                    memoryMonitor.className = 'memory-monitor warning';
                    this.triggerGarbageCollection();
                } else if (usageRatio > 0.6) {
                    memoryMonitor.className = 'memory-monitor caution';
                } else {
                    memoryMonitor.className = 'memory-monitor';
                }
            }

            // Update UI memory usage indicator
            const memoryUsageElement = document.getElementById('memoryUsage');
            if (memoryUsageElement) {
                memoryUsageElement.textContent = `${usedHeapSize} / ${heapLimit}`;
                memoryUsageElement.className = `memory-usage ${
                    usageRatio > 0.9 ? 'critical' :
                    usageRatio > 0.8 ? 'warning' :
                    usageRatio > 0.6 ? 'caution' : 'normal'
                }`;
            }
        }
    }

    // Handle critical memory pressure
    handleCriticalMemoryPressure() {
        console.warn('Critical memory pressure detected! Taking emergency measures...');

        // Show warning to user
        this.showNotification(
            'High memory usage detected. The system is optimizing memory usage to prevent crashes.',
            'warning',
            5000
        );

        // Aggressive garbage collection
        this.triggerGarbageCollection();

        // Clear non-essential data
        this.clearNonEssentialData();

        // Reduce processing frequency temporarily
        if (this.memoryMonitorInterval) {
            clearInterval(this.memoryMonitorInterval);
            this.memoryMonitorInterval = setInterval(() => {
                this.updateMemoryUsage();
            }, 10000); // Reduce to every 10 seconds during high memory usage
        }
    }

    // Clear non-essential data to free memory
    clearNonEssentialData() {
        try {
            // Clear sample data from file handler
            if (window.fileHandler && window.fileHandler.processedData) {
                window.fileHandler.processedData.forEach(fileData => {
                    if (fileData.sampleRows && fileData.sampleRows.length > 50) {
                        fileData.sampleRows = fileData.sampleRows.slice(0, 50); // Keep only 50 samples
                    }
                });
                console.log('Reduced sample data to free memory');
            }



            console.log('Non-essential data cleared to free memory');
        } catch (error) {
            console.error('Error clearing non-essential data:', error);
        }
    }

    // Format bytes to human-readable format
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Try to trigger garbage collection
    triggerGarbageCollection() {
        // Create and destroy large objects to encourage garbage collection
        const arr = new Array(100).fill('x').map(() => new Array(1000000).join('x'));
        arr.length = 0;

        // Log memory pressure
        console.log('High memory usage detected, attempting to free memory');
    }

    // Set up Web Worker for background processing
    setupWebWorker() {
        // Check if Web Workers are supported
        if (window.Worker) {
            try {
                // Create a blob URL for the worker script
                const workerScript = `
                    self.onmessage = function(e) {
                        const { action, data } = e.data;

                        if (action === 'processData') {
                            // Process data in the background
                            const result = processDataInBackground(data);
                            self.postMessage({ action: 'processComplete', result });
                        }
                    };

                    // Function to process data in the background
                    function processDataInBackground(data) {
                        // Calculate metrics
                        const metrics = {
                            totalTransactions: 0,
                            totalAmount: 0,
                            hocCount: 0,
                            hocAmount: 0,
                            ibdCount: 0,
                            ibdAmount: 0,
                            wuCount: 0,
                            wuAmount: 0,
                            hocUniqueSerialCount: 0,
                            ibdUniqueSerialCount: 0,
                            wuUniqueSerialCount: 0,
                            currencyCounts: {
                                USD: 0,
                                MMK: 0
                            },
                            currencyAmounts: {
                                USD: 0,
                                MMK: 0
                            }
                        };

                        // Sets to track unique serial numbers
                        const hocSerialNumbers = new Set();
                        const ibdSerialNumbers = new Set();
                        const wuSerialNumbers = new Set();

                        // Process each transaction
                        data.forEach(transaction => {
                            const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                            const serialNo = transaction.SERIAL_NO || '';

                            // Update total counts
                            metrics.totalTransactions++;
                            metrics.totalAmount += amount;

                            // Update report type counts
                            if (transaction.REPORTTYPE === 'HOC') {
                                metrics.hocCount++;
                                metrics.hocAmount += amount;

                                // Track unique serial numbers for HOC
                                if (serialNo) {
                                    hocSerialNumbers.add(serialNo);
                                }
                            } else if (transaction.REPORTTYPE === 'IBD') {
                                metrics.ibdCount++;
                                metrics.ibdAmount += amount;

                                // Track unique serial numbers for IBD
                                if (serialNo) {
                                    ibdSerialNumbers.add(serialNo);
                                }
                            } else if (transaction.REPORTTYPE === 'WU') {
                                // WU (Western Union) transactions - separate metrics (not included in IBD)
                                metrics.wuCount++;
                                metrics.wuAmount += amount;

                                // Track unique serial numbers for WU
                                if (serialNo) {
                                    wuSerialNumbers.add(serialNo);
                                }
                            }

                            // Update currency counts
                            if (transaction.TRANSACTION_CURRENCY === 'USD') {
                                metrics.currencyCounts.USD++;
                                metrics.currencyAmounts.USD += amount;
                            } else if (transaction.TRANSACTION_CURRENCY === 'MMK') {
                                metrics.currencyCounts.MMK++;
                                metrics.currencyAmounts.MMK += amount;
                            }
                        });

                        // Update unique serial counts
                        metrics.hocUniqueSerialCount = hocSerialNumbers.size;
                        metrics.ibdUniqueSerialCount = ibdSerialNumbers.size;
                        metrics.wuUniqueSerialCount = wuSerialNumbers.size;

                        return { metrics, processedData: data };
                    }
                `;

                const blob = new Blob([workerScript], { type: 'application/javascript' });
                const workerUrl = URL.createObjectURL(blob);

                // Create the worker
                this.worker = new Worker(workerUrl);

                // Set up message handler
                this.worker.onmessage = (e) => {
                    const { action, result } = e.data;

                    if (action === 'processComplete') {
                        // Update the UI with the processed data
                        if (window.dataProcessor) {
                            window.dataProcessor.summaryMetrics = result.metrics;
                            window.dataProcessor.updateUI();
                        }


                    }
                };

                console.log('Web Worker initialized for background processing.');
            } catch (error) {
                console.error('Failed to initialize Web Worker:', error);
            }
        }
    }

    // Process data using Web Worker if available
    processDataWithWorker(data) {
        if (this.worker) {
            this.worker.postMessage({
                action: 'processData',
                data: data
            });
        } else {
            // Fallback to main thread processing
            if (window.dataProcessor) {
                window.dataProcessor.processData(data);
            }
        }
    }

    // Get memory usage information
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }

    // Initialize performance monitoring
    initPerformanceMonitoring() {
        // Performance monitor should already be initialized globally
        if (window.performanceMonitor) {
            console.log('Performance monitoring integrated successfully');

            // Set up performance event dispatching
            this.setupPerformanceEvents();
        } else {
            console.warn('Performance monitor not available');
        }
    }

    // Set up performance event dispatching
    setupPerformanceEvents() {
        // Prevent multiple wrapping of the same functions
        if (this.performanceEventsSetup) {
            this.logDebug('Performance events already set up, skipping...');
            return;
        }

        // Override file processing to dispatch events
        if (window.fileHandler && !window.fileHandler._originalProcessFiles) {
            // Store the original function reference to prevent multiple wrapping
            window.fileHandler._originalProcessFiles = window.fileHandler.processFiles.bind(window.fileHandler);

            // Create a properly bound wrapper function that preserves 'this' context
            window.fileHandler.processFiles = async function() {
                document.dispatchEvent(new CustomEvent('fileProcessingStart'));
                try {
                    // Call the original function with proper 'this' context
                    const result = await window.fileHandler._originalProcessFiles.call(window.fileHandler);
                    document.dispatchEvent(new CustomEvent('fileProcessingEnd'));
                    return result;
                } catch (error) {
                    document.dispatchEvent(new CustomEvent('fileProcessingEnd'));
                    throw error;
                }
            }.bind(window.fileHandler);

            this.logDebug('File processing wrapped with performance events');
        }

        // Override TTR processing to dispatch events
        if (window.ttrMonitoring && window.ttrMonitoring.rules && !window.ttrMonitoring.rules._originalAnalyzeTransactions) {
            // Store the original function reference to prevent multiple wrapping
            window.ttrMonitoring.rules._originalAnalyzeTransactions = window.ttrMonitoring.rules.analyzeTransactions.bind(window.ttrMonitoring.rules);

            // Create a properly bound wrapper function that preserves 'this' context
            window.ttrMonitoring.rules.analyzeTransactions = function(transactions, fileId) {
                document.dispatchEvent(new CustomEvent('ttrProcessingStart'));
                try {
                    // Call the original function with proper 'this' context
                    const result = window.ttrMonitoring.rules._originalAnalyzeTransactions.call(window.ttrMonitoring.rules, transactions, fileId);
                    document.dispatchEvent(new CustomEvent('ttrProcessingEnd'));
                    return result;
                } catch (error) {
                    document.dispatchEvent(new CustomEvent('ttrProcessingEnd'));
                    throw error;
                }
            }.bind(window.ttrMonitoring.rules);

            this.logDebug('TTR processing wrapped with performance events');
        }

        // Mark as set up to prevent duplicate wrapping
        this.performanceEventsSetup = true;
    }

    // Check if all components are properly initialized
    checkSystemHealth() {
        const health = {
            app: this.isInitialized,
            modules: {
                dataProcessor: !!window.dataProcessor,
                fileHandler: !!window.fileHandler,
                ttrMonitoring: !!window.ttrMonitoring,
                highValueExport: !!window.highValueExport,
                performanceMonitor: !!window.performanceMonitor
            },
            dependencies: {
                xlsx: typeof XLSX !== 'undefined',
                webWorkers: typeof Worker !== 'undefined'
            },
            memory: this.getMemoryUsage(),
            performance: window.performanceMonitor ? window.performanceMonitor.getMetrics() : null
        };

        const allModulesLoaded = Object.values(health.modules).every(loaded => loaded);
        const criticalDepsLoaded = health.dependencies.xlsx; // XLSX is critical

        health.overall = health.app && allModulesLoaded && criticalDepsLoaded;

        return health;
    }

    // Production-safe logging methods
    logError(message, data = null) {
        // Always log errors but sanitize sensitive data
        const sanitizedData = data ? this.sanitizeForLogging(data) : null;
        console.error(`[ERROR] ${message}`, sanitizedData);

        // In production, also store errors for monitoring
        this.storeErrorForMonitoring(message, sanitizedData);
    }

    logWarn(message, data = null) {
        // Always log warnings but sanitize sensitive data
        const sanitizedData = data ? this.sanitizeForLogging(data) : null;
        console.warn(`[WARN] ${message}`, sanitizedData);
    }

    logInfo(message, data = null) {
        // In production, only log essential info without sensitive data
        if (APP_CONFIG?.DEBUG_MODE) {
            console.log(`[INFO] ${message}`, data);
        } else {
            console.log(`[INFO] ${message}`);
        }
    }

    logDebug(message, data = null) {
        // Debug logging only in development mode
        if (APP_CONFIG?.DEBUG_MODE) {
            console.log(`[DEBUG] ${message}`, data);
        }
    }

    // Sanitize sensitive data for logging
    sanitizeForLogging(data) {
        if (!data || typeof data !== 'object') {
            return data;
        }

        const sensitiveFields = APP_CONFIG?.SECURITY?.SENSITIVE_FIELDS || [
            'TRANSACTION_AMOUNT', 'CUSTOMER_ID', 'CUSTOMER_NAME',
            'ACCOUNT_NUMBER', 'SERIAL_NO', 'TRANSACTION_REF_NUMBER'
        ];

        const sanitized = { ...data };
        sensitiveFields.forEach(field => {
            if (sanitized[field]) {
                sanitized[field] = '[REDACTED]';
            }
        });

        return sanitized;
    }

    // Store errors for production monitoring
    storeErrorForMonitoring(message, data) {
        try {
            // In a real production environment, this would send to monitoring service
            // For now, store in sessionStorage for debugging if needed
            const errorLog = {
                timestamp: new Date().toISOString(),
                message: message,
                data: data,
                userAgent: navigator.userAgent,
                url: window.location.href
            };

            const existingErrors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
            existingErrors.push(errorLog);

            // Keep only last 10 errors to prevent storage overflow
            if (existingErrors.length > 10) {
                existingErrors.splice(0, existingErrors.length - 10);
            }

            sessionStorage.setItem('errorLog', JSON.stringify(existingErrors));
        } catch (e) {
            // Silently fail if storage is not available
        }
    }

    // Check critical external dependencies
    checkCriticalDependencies() {
        const dependencies = {
            xlsx: typeof XLSX !== 'undefined',
            webWorkers: typeof Worker !== 'undefined',
            fileApi: typeof File !== 'undefined' && typeof FileReader !== 'undefined',
            performance: typeof performance !== 'undefined'
        };

        const missing = Object.entries(dependencies)
            .filter(([key, available]) => !available)
            .map(([key]) => key);

        if (missing.length > 0) {
            this.logWarn('Missing dependencies', { missing: missing });

            // Try to provide fallbacks for missing dependencies
            if (!dependencies.xlsx) {
                this.logWarn('XLSX library not available - Excel export will be disabled');
            }

            if (!dependencies.webWorkers) {
                this.logWarn('Web Workers not supported - processing will use main thread');
            }

            return false;
        }

        return true;
    }

}

// Create a global instance of the App (with duplicate prevention)
if (!window.app) {
    window.app = new App();
    console.log('App instance created and assigned to window.app');
} else {
    console.log('App instance already exists, skipping creation');
}
