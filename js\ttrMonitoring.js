/**
 * TTR Monitoring System
 * Main class that coordinates TTR monitoring functionality
 */

class TTRMonitoring {
    constructor() {
        this.isInitialized = false;
        this.alerts = [];
        this.filteredAlerts = [];
        this.rules = null;
        this.exporter = null;
        this.ui = null;

        // File tracking for TTR data
        this.fileAlerts = new Map(); // fileId -> alerts
        this.processedFiles = new Set();

        // Configuration
        this.config = {
            autoRefresh: true,
            refreshInterval: 30000, // 30 seconds
            maxAlerts: 100000,
            consolidateAlerts: true // Enable alert consolidation by default
        };

        // Initialize development mode detection
        this.isDevelopmentMode = this.detectDevelopmentMode();

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    // Detect development mode for conditional logging
    detectDevelopmentMode() {
        return (
            window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1' ||
            window.location.hostname === '' ||
            window.location.protocol === 'file:' ||
            window.location.port !== '' ||
            (typeof window !== 'undefined' && window.location.search.includes('debug=true'))
        );
    }

    // Production-safe logging methods
    logInfo(message, data = null) {
        if (this.isDevelopmentMode) {
            console.log(`[INFO] TTR: ${message}`, data);
        } else {
            console.log(`[INFO] TTR: ${message}`);
        }
    }

    logWarn(message, data = null) {
        console.warn(`[WARN] TTR: ${message}`, data);
    }

    logError(message, error = null) {
        console.error(`[ERROR] TTR: ${message}`, error);
    }

    logDebug(message, data = null) {
        if (this.isDevelopmentMode) {
            console.log(`[DEBUG] TTR: ${message}`, data);
        }
    }

    // Initialize the TTR monitoring system
    init() {
        if (this.isInitialized) return;

        try {
            // Check if required dependencies are loaded
            if (!window.TTRRules || !window.TTRExport) {
                this.logWarn('TTR dependencies not loaded yet, retrying...');
                setTimeout(() => this.init(), 500);
                return;
            }

            // Initialize components
            this.rules = new window.TTRRules();
            this.exporter = new window.TTRExport();

            // Wait for TTR UI to be available
            if (window.TTRUI) {
                this.ui = new window.TTRUI(this);
            } else {
                this.logWarn('TTRUI not available yet, will retry...');
                setTimeout(() => {
                    if (window.TTRUI && !this.ui) {
                        this.ui = new window.TTRUI(this);
                        this.logInfo('TTR UI initialized successfully');
                    }
                }, 500);
            }

            // Set up event listeners
            this.setupEventListeners();

            // Mark as initialized
            this.isInitialized = true;
            this.logInfo('TTR Monitoring system initialized successfully');

            // Initial UI update with force refresh
            this.updateUI();

            // Force immediate display update
            this.forceUIRefresh();

            // Ensure TTR page elements are properly initialized
            this.initializeTTRPageElements();

        } catch (error) {
            this.logError('Error initializing TTR Monitoring', error);
            setTimeout(() => this.init(), 1000);
        }
    }

    // Initialize TTR page elements to ensure proper display
    initializeTTRPageElements() {
        try {
            // Ensure all TTR metric elements exist and have default values
            const metricElements = [
                { id: 'highPriorityAlerts', defaultValue: '0' },
                { id: 'mediumPriorityAlerts', defaultValue: '0' },
                { id: 'lowPriorityAlerts', defaultValue: '0' },
                { id: 'thresholdBreaches', defaultValue: '0' },
                { id: 'alertStatus', defaultValue: '' },
                { id: 'alertStatusText', defaultValue: 'No Active Alerts' }
            ];

            metricElements.forEach(({ id, defaultValue }) => {
                const element = document.getElementById(id);
                if (element) {
                    if (id === 'alertStatus') {
                        element.className = 'status-dot';
                    } else if (id !== 'alertStatusText') {
                        element.textContent = defaultValue;
                    } else {
                        element.textContent = defaultValue;
                    }
                    console.log(`TTR: Initialized element ${id} with default value`);
                } else {
                    console.warn(`TTR: Element ${id} not found in DOM`);
                }
            });

            // Initialize the alerts table
            const tableBody = document.getElementById('ttrAlertsTableBody');
            if (tableBody) {
                tableBody.innerHTML = `
                    <tr class="empty-table-message">
                        <td colspan="8">No alerts detected. Upload CSV files to begin monitoring.</td>
                    </tr>
                `;
                console.log('TTR: Initialized alerts table with empty state');
            } else {
                console.warn('TTR: Alerts table body not found');
            }

            console.log('TTR: Page elements initialization completed');
        } catch (error) {
            console.error('TTR: Error initializing page elements:', error);
        }
    }

    // Set up event listeners
    setupEventListeners() {
        // Listen for file processing events from the main application
        document.addEventListener('fileProcessed', (event) => {
            this.handleFileProcessed(event.detail);
        });

        document.addEventListener('fileRemoved', (event) => {
            this.handleFileRemoved(event.detail);
        });

        // Configuration panel events
        const configToggle = document.getElementById('configToggle');
        if (configToggle) {
            configToggle.addEventListener('click', () => this.toggleConfiguration());
        }

        const saveConfigBtn = document.getElementById('saveConfigBtn');
        if (saveConfigBtn) {
            saveConfigBtn.addEventListener('click', () => this.saveConfiguration());
        }

        const resetConfigBtn = document.getElementById('resetConfigBtn');
        if (resetConfigBtn) {
            resetConfigBtn.addEventListener('click', () => this.resetConfiguration());
        }

        // Export button
        const exportBtn = document.getElementById('exportAlertsBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportAlerts());
        }

        // Filter events
        const priorityFilter = document.getElementById('alertPriorityFilter');
        if (priorityFilter) {
            priorityFilter.addEventListener('change', () => this.applyFilters());
        }

        const typeFilter = document.getElementById('alertTypeFilter');
        if (typeFilter) {
            typeFilter.addEventListener('change', () => this.applyFilters());
        }
    }

    // Handle file processing completion
    async handleFileProcessed(fileData) {
        console.log('TTR: Received fileProcessed event:', fileData);

        if (!fileData) {
            console.warn('TTR: No file data provided');
            return;
        }

        if (!fileData.transactions) {
            console.warn('TTR: No transactions in file data:', fileData);
            return;
        }

        if (!fileData.fileId) {
            console.warn('TTR: No fileId in file data:', fileData);
            return;
        }

        if (!Array.isArray(fileData.transactions)) {
            console.warn('TTR: Transactions is not an array:', typeof fileData.transactions, fileData.transactions);
            return;
        }

        // Enhanced duplicate processing prevention with session tracking
        const processingKey = `${fileData.fileId}_${fileData.processingSessionId || 'unknown'}`;
        if (this.processedFiles.has(fileData.fileId)) {
            console.log(`TTR: File ${fileData.fileId} already processed, skipping... (Session: ${fileData.processingSessionId})`);
            return;
        }

        // Also check if we've processed this specific session to prevent double processing
        if (!this.processedSessions) {
            this.processedSessions = new Set();
        }
        if (fileData.processingSessionId && this.processedSessions.has(processingKey)) {
            console.log(`TTR: Processing session ${processingKey} already handled, skipping...`);
            return;
        }

        console.log(`TTR: Processing analysis for file: ${fileData.fileName || fileData.fileId} with ${fileData.transactions.length} transactions (Session: ${fileData.processingSessionId})`);

        try {
            // Validate that we have the rules engine
            if (!this.rules) {
                console.error('TTR: Rules engine not initialized');
                return;
            }

            // Dispatch TTR processing start event for performance monitoring
            document.dispatchEvent(new CustomEvent('ttrProcessingStart', {
                detail: { fileName: fileData.fileName, transactionCount: fileData.transactions.length }
            }));

            // For large transaction sets, process with responsiveness handling
            let newAlerts = [];
            if (fileData.transactions.length > 5000) {
                newAlerts = await this.processTransactionsWithResponsiveness(fileData.transactions, fileData.fileId, fileData.fileName);
            } else {
                // Run TTR analysis on the new transactions
                newAlerts = this.rules.analyzeTransactions(fileData.transactions, fileData.fileId);
            }

            console.log(`TTR: Generated ${newAlerts.length} raw alerts for file ${fileData.fileId}`);

            // Ensure all alerts have the correct fileId and additional metadata
            newAlerts.forEach((alert, index) => {
                alert.fileId = fileData.fileId;
                alert.fileName = fileData.fileName;
                alert.processedAt = new Date().toISOString();

                // Debug: Log first few alerts to verify fileId assignment
                if (index < 3) {
                    console.log(`TTR: Alert ${alert.id} assigned to file ${alert.fileId}`);
                }
            });

            // Store alerts for this file
            this.fileAlerts.set(fileData.fileId, newAlerts);
            this.processedFiles.add(fileData.fileId);

            // Mark this processing session as completed
            if (fileData.processingSessionId) {
                this.processedSessions.add(processingKey);
            }

            console.log(`TTR: Stored ${newAlerts.length} alerts for file ${fileData.fileId} in file tracking (Session: ${fileData.processingSessionId})`);

            // Add to main alerts array (incremental addition)
            const beforeCount = this.alerts.length;
            this.alerts.push(...newAlerts);
            const afterCount = this.alerts.length;

            console.log(`TTR: Added ${newAlerts.length} alerts to main array. Before: ${beforeCount}, After: ${afterCount}`);

            // Apply intelligent memory management
            this.manageMemoryLimits();

            // Debug: Verify file IDs in main array
            const fileIdsInMainArray = [...new Set(this.alerts.map(alert => alert.fileId))];
            console.log(`TTR: File IDs now in main array:`, fileIdsInMainArray);

            // Update UI with yielding for responsiveness
            await this.updateUIWithYielding();

            // Show notification if alerts were generated
            if (newAlerts.length > 0) {
                const highPriorityCount = newAlerts.filter(a => a.priority === 'high').length;
                const message = `TTR Analysis: ${newAlerts.length} alert(s) generated from ${fileData.fileName || fileData.fileId}` +
                               (highPriorityCount > 0 ? ` (${highPriorityCount} high priority)` : '');

                if (window.app && window.app.showNotification) {
                    window.app.showNotification(message, highPriorityCount > 0 ? 'warning' : 'info');
                }
            }

            // Dispatch TTR processing end event
            document.dispatchEvent(new CustomEvent('ttrProcessingEnd', {
                detail: { fileName: fileData.fileName, alertCount: newAlerts.length }
            }));

            console.log(`TTR: Processing completed for file ${fileData.fileId}. Total alerts: ${this.alerts.length}, Tracked files: ${this.processedFiles.size}`);

        } catch (error) {
            console.error('Error processing TTR analysis:', error);

            // Ensure end event is dispatched even on error
            document.dispatchEvent(new CustomEvent('ttrProcessingEnd', {
                detail: { fileName: fileData.fileName, error: error.message }
            }));

            if (window.app && window.app.showNotification) {
                window.app.showNotification('Error processing TTR analysis', 'error');
            }
        }
    }

    // Handle file removal
    handleFileRemoved(fileData) {
        if (!fileData || !fileData.fileId) {
            console.warn('TTR: Invalid file data for removal:', fileData);
            return;
        }

        console.log(`TTR: Starting removal process for file: ${fileData.fileId}`);
        console.log(`TTR: Current state - Total alerts: ${this.alerts.length}, Tracked files: ${this.processedFiles.size}`);

        try {
            // Debug: Log all current fileIds in alerts
            const currentFileIds = [...new Set(this.alerts.map(alert => alert.fileId))];
            console.log(`TTR: Current file IDs in alerts:`, currentFileIds);
            console.log(`TTR: File ID to remove:`, fileData.fileId);

            // Get alerts for this file before removal
            const fileAlerts = this.fileAlerts.get(fileData.fileId) || [];
            const initialAlertCount = this.alerts.length;

            console.log(`TTR: Found ${fileAlerts.length} alerts in file tracking for ${fileData.fileId}`);

            // Count alerts that match this fileId in main array
            const alertsToRemove = this.alerts.filter(alert => alert.fileId === fileData.fileId);
            console.log(`TTR: Found ${alertsToRemove.length} alerts in main array matching fileId ${fileData.fileId}`);

            // Debug: Log some sample alerts before removal
            if (this.alerts.length > 0) {
                console.log(`TTR: Sample alerts before removal:`, this.alerts.slice(0, 3).map(a => ({id: a.id, fileId: a.fileId})));
            }

            // Remove alerts from main array - use strict equality check
            this.alerts = this.alerts.filter(alert => {
                const shouldKeep = alert.fileId !== fileData.fileId;
                if (!shouldKeep) {
                    console.log(`TTR: Removing alert ${alert.id} from file ${alert.fileId}`);
                }
                return shouldKeep;
            });

            // Remove from file tracking
            this.fileAlerts.delete(fileData.fileId);
            this.processedFiles.delete(fileData.fileId);

            const removedCount = initialAlertCount - this.alerts.length;
            console.log(`TTR: Removal completed - Removed ${removedCount} alerts. Remaining: ${this.alerts.length} alerts from ${this.processedFiles.size} files`);

            // Debug: Log remaining file IDs
            const remainingFileIds = [...new Set(this.alerts.map(alert => alert.fileId))];
            console.log(`TTR: Remaining file IDs in alerts:`, remainingFileIds);

            // Verify data integrity after removal
            const integrity = this.verifyDataIntegrity();
            if (!integrity.isValid) {
                console.error(`TTR: Data integrity issues after removal:`, integrity.issues);
            }

            // Update UI to reflect changes
            this.applyFilters();
            this.updateUI();

            // Show notification about removal
            if (removedCount > 0) {
                const fileName = fileData.fileName || fileData.fileId;
                if (window.app && window.app.showNotification) {
                    window.app.showNotification(
                        `Removed ${removedCount} TTR alert(s) from ${fileName}`,
                        'info'
                    );
                }
            } else {
                console.warn(`TTR: No alerts were removed for file ${fileData.fileId}`);
            }

        } catch (error) {
            console.error('TTR: Error removing file data:', error);
            if (window.app && window.app.showNotification) {
                window.app.showNotification('Error removing TTR alerts', 'error');
            }
        }
    }

    // Process large transaction sets with responsiveness handling
    async processTransactionsWithResponsiveness(transactions, fileId, fileName) {
        const chunkSize = 2000; // Process 2000 transactions at a time
        const totalChunks = Math.ceil(transactions.length / chunkSize);
        let allAlerts = [];

        for (let i = 0; i < totalChunks; i++) {
            const start = i * chunkSize;
            const end = Math.min(start + chunkSize, transactions.length);
            const chunk = transactions.slice(start, end);

            // Update progress
            const progress = Math.round(((i + 1) / totalChunks) * 100);
            this.updateTTRProgress(progress, `Processing TTR chunk ${i + 1}/${totalChunks} for ${fileName}`);

            // Process chunk
            const chunkAlerts = this.rules.analyzeTransactions(chunk, `${fileId}_chunk_${i}`);

            // Update fileId for chunk alerts to match the main file
            chunkAlerts.forEach(alert => {
                alert.fileId = fileId;
            });

            allAlerts.push(...chunkAlerts);

            // Yield control to browser between chunks
            await new Promise(resolve => setTimeout(resolve, 10));
        }

        return allAlerts;
    }

    // Update UI with yielding to prevent blocking
    async updateUIWithYielding() {
        // Apply filters and update UI in batches
        await new Promise(resolve => {
            this.applyFilters();
            setTimeout(resolve, 0);
        });

        // Update UI
        await new Promise(resolve => {
            this.updateUI();
            setTimeout(resolve, 0);
        });
    }

    // Update TTR processing progress
    updateTTRProgress(percentage, statusText) {
        // Update any TTR-specific progress indicators
        const ttrProgressElement = document.getElementById('ttrProgress');
        if (ttrProgressElement) {
            ttrProgressElement.textContent = `TTR: ${statusText} (${percentage}%)`;
        }

        // Also update main progress if available
        const progressText = document.getElementById('progressText');
        if (progressText) {
            progressText.textContent = statusText;
        }

        // Update progress bar if available
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
    }

    // Intelligent memory management to prevent excessive memory usage
    manageMemoryLimits() {
        if (this.alerts.length <= this.config.maxAlerts) {
            return; // No action needed
        }

        console.log(`TTR: Managing memory - ${this.alerts.length} alerts exceed limit of ${this.config.maxAlerts}`);

        // Sort alerts by timestamp (newest first) and priority
        const sortedAlerts = [...this.alerts].sort((a, b) => {
            // First sort by priority (high > medium > low)
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            const aPriority = priorityOrder[a.priority] || 0;
            const bPriority = priorityOrder[b.priority] || 0;

            if (aPriority !== bPriority) {
                return bPriority - aPriority; // Higher priority first
            }

            // Then by timestamp (newer first)
            return new Date(b.timestamp) - new Date(a.timestamp);
        });

        // Keep the most important alerts
        this.alerts = sortedAlerts.slice(0, this.config.maxAlerts);

        // Update file tracking to reflect removed alerts
        this.updateFileTrackingAfterTruncation();

        console.log(`TTR: Memory management completed - kept ${this.alerts.length} most important alerts`);
    }

    // Update file tracking after memory truncation
    updateFileTrackingAfterTruncation() {
        const remainingFileIds = new Set(this.alerts.map(alert => alert.fileId));

        // Remove file tracking for files that no longer have alerts
        for (const fileId of this.fileAlerts.keys()) {
            if (!remainingFileIds.has(fileId)) {
                this.fileAlerts.delete(fileId);
                this.processedFiles.delete(fileId);
            }
        }

        // Update file alerts mapping to reflect current state
        for (const fileId of remainingFileIds) {
            const fileAlerts = this.alerts.filter(alert => alert.fileId === fileId);
            this.fileAlerts.set(fileId, fileAlerts);
        }
    }

    // Apply filters to alerts
    applyFilters() {
        console.log('TTR: Applying filters to alerts');

        const priorityFilter = document.getElementById('alertPriorityFilter');
        const typeFilter = document.getElementById('alertTypeFilter');

        // Start with only active alerts - explicitly exclude dismissed alerts
        let filtered = this.alerts.filter(a => {
            const isDismissed = a.status === 'dismissed';
            const isActive = a.status === 'active' || !a.status;
            return isActive && !isDismissed;
        });

        console.log(`TTR: After status filter - ${filtered.length} alerts remaining`);

        // Apply consolidation if enabled and we have alerts to consolidate
        if (this.config.consolidateAlerts && filtered.length > 0) {
            const consolidated = this.consolidateAlerts(filtered);
            // Use consolidated alerts if we have any, otherwise use individual alerts
            const consolidatedOnly = consolidated.filter(a => a.isConsolidated);
            if (consolidatedOnly.length > 0) {
                filtered = consolidated; // This includes both consolidated and non-consolidated alerts
                console.log(`TTR: After consolidation - ${filtered.length} alerts (including ${consolidatedOnly.length} consolidated)`);
            }
        }

        // Apply priority filter
        if (priorityFilter && priorityFilter.value !== 'all') {
            filtered = filtered.filter(alert => alert.priority === priorityFilter.value);
            console.log(`TTR: After priority filter (${priorityFilter.value}) - ${filtered.length} alerts remaining`);
        }

        // Apply type filter (handle both consolidated and individual alerts)
        if (typeFilter && typeFilter.value !== 'all') {
            filtered = filtered.filter(alert => {
                if (alert.isConsolidated && alert.alertTypes) {
                    // For consolidated alerts, check if any of the individual alert types match
                    return alert.alertTypes.some(alertType => alertType.type === typeFilter.value);
                } else {
                    return alert.type === typeFilter.value;
                }
            });
            console.log(`TTR: After type filter (${typeFilter.value}) - ${filtered.length} alerts remaining`);
        }

        // Store the filtered results
        this.filteredAlerts = filtered;

        // Update alerts table if UI is available
        if (this.ui && this.ui.updateAlertsTable) {
            console.log('TTR: Updating UI with filtered alerts');
            this.ui.updateAlertsTable(this.filteredAlerts);
        } else {
            console.warn('TTR: UI not available for update');
        }

        console.log(`TTR Filters Applied: ${this.alerts.length} total alerts -> ${filtered.length} filtered alerts`);
        return filtered;
    }

    // Update UI elements
    updateUI() {
        this.updateSummaryMetrics();
        this.updateStatusIndicators();
        this.applyFilters(); // This will update the table
    }

    // Update summary metrics
    updateSummaryMetrics() {
        // Get active alerts
        const activeAlerts = this.alerts.filter(a => a.status === 'active' || !a.status);

        // Use either consolidated or individual alerts for metrics
        let alertsForMetrics = activeAlerts;

        if (this.config.consolidateAlerts) {
            // Apply consolidation and use consolidated alerts if available
            const consolidated = this.consolidateAlerts(activeAlerts);
            const consolidatedOnly = consolidated.filter(a => a.isConsolidated);

            // If we have consolidated alerts, use them; otherwise fall back to individual alerts
            if (consolidatedOnly.length > 0) {
                alertsForMetrics = consolidatedOnly;
            }
        }

        const priorityCounts = {
            high: alertsForMetrics.filter(a => a.priority === 'high').length,
            medium: alertsForMetrics.filter(a => a.priority === 'medium').length,
            low: alertsForMetrics.filter(a => a.priority === 'low').length
        };

        // Count threshold breaches - handle both consolidated and individual alerts
        const thresholdBreaches = alertsForMetrics.filter(a => {
            if (a.isConsolidated && a.alertTypes) {
                return a.alertTypes.some(alertType => alertType.type === 'threshold');
            } else {
                return a.type === 'threshold';
            }
        }).length;

        // Update metric cards
        this.updateElement('highPriorityAlerts', priorityCounts.high);
        this.updateElement('mediumPriorityAlerts', priorityCounts.medium);
        this.updateElement('lowPriorityAlerts', priorityCounts.low);
        this.updateElement('thresholdBreaches', thresholdBreaches);

        // Debug logging
        console.log(`TTR Summary Debug: Active alerts: ${activeAlerts.length}, Alerts for metrics: ${alertsForMetrics.length}`);
        console.log(`TTR Summary Counts: High: ${priorityCounts.high}, Medium: ${priorityCounts.medium}, Low: ${priorityCounts.low}, Threshold: ${thresholdBreaches}`);
    }

    // Update status indicators
    updateStatusIndicators() {
        const alertStatus = document.getElementById('alertStatus');
        const alertStatusText = document.getElementById('alertStatusText');

        if (alertStatus && alertStatusText) {
            // Get active alerts
            const activeAlerts = this.alerts.filter(a => a.status === 'active' || !a.status);

            // Use either consolidated or individual alerts for status
            let alertsForStatus = activeAlerts;
            let statusType = 'individual';

            if (this.config.consolidateAlerts) {
                const consolidated = this.consolidateAlerts(activeAlerts);
                const consolidatedOnly = consolidated.filter(a => a.isConsolidated);

                // If we have consolidated alerts, use them; otherwise fall back to individual alerts
                if (consolidatedOnly.length > 0) {
                    alertsForStatus = consolidatedOnly;
                    statusType = 'consolidated';
                }
            }

            const highPriorityCount = alertsForStatus.filter(a => a.priority === 'high').length;

            if (highPriorityCount > 0) {
                alertStatus.className = 'status-dot alert';
                alertStatusText.textContent = `${highPriorityCount} High Priority ${statusType === 'consolidated' ? 'Consolidated ' : ''}Alert${highPriorityCount > 1 ? 's' : ''}`;
            } else if (alertsForStatus.length > 0) {
                alertStatus.className = 'status-dot active';
                alertStatusText.textContent = `${alertsForStatus.length} Active ${statusType === 'consolidated' ? 'Consolidated ' : ''}Alert${alertsForStatus.length > 1 ? 's' : ''}`;
            } else {
                alertStatus.className = 'status-dot';
                alertStatusText.textContent = 'No Active Alerts';
            }
        }
    }

    // Helper method to update element content
    updateElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    // Force UI refresh to ensure TTR data is visible
    forceUIRefresh() {
        // Ensure all TTR metric elements are visible and updated
        const metricElements = [
            'highPriorityAlerts',
            'mediumPriorityAlerts',
            'lowPriorityAlerts',
            'thresholdBreaches'
        ];

        metricElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                // Force visual update
                element.style.opacity = '0.99';
                setTimeout(() => {
                    element.style.opacity = '1';
                }, 10);
            }
        });

        // Force table update
        if (this.ui && this.ui.updateAlertsTable) {
            this.ui.updateAlertsTable(this.filteredAlerts || []);
        }

        console.log('TTR UI force refresh completed');
    }

    // Configuration management
    toggleConfiguration() {
        const configContent = document.getElementById('configContent');
        const configToggle = document.getElementById('configToggle');

        if (configContent && configToggle) {
            const isExpanded = configToggle.getAttribute('aria-expanded') === 'true';

            if (isExpanded) {
                configContent.style.display = 'none';
                configToggle.setAttribute('aria-expanded', 'false');
                configToggle.querySelector('.toggle-text').textContent = 'Show Configuration';
            } else {
                configContent.style.display = 'block';
                configToggle.setAttribute('aria-expanded', 'true');
                configToggle.querySelector('.toggle-text').textContent = 'Hide Configuration';
                this.loadConfiguration();
            }
        }
    }

    // Load current configuration into UI
    loadConfiguration() {
        if (!this.rules) return;

        const config = this.rules.getConfiguration();

        this.updateInputValue('singleThreshold', config.singleThreshold);
        this.updateInputValue('dailyThreshold', config.dailyThreshold);
        this.updateInputValue('structuringWindow', config.structuringWindow);
        this.updateInputValue('structuringThreshold', config.structuringThreshold);
        this.updateInputValue('velocityCount', config.velocityCount);
        this.updateInputValue('burstWindow', config.burstWindow);
    }

    // Helper method to update input element values
    updateInputValue(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.value = value;
        }
    }

    // Save configuration changes
    saveConfiguration() {
        if (!this.rules) return;

        try {
            const newConfig = {
                singleThreshold: parseInt(document.getElementById('singleThreshold').value) || 1000000000,
                dailyThreshold: parseInt(document.getElementById('dailyThreshold').value) || 5000000000,
                structuringWindow: parseInt(document.getElementById('structuringWindow').value) || 24,
                structuringThreshold: parseInt(document.getElementById('structuringThreshold').value) || 90,
                velocityCount: parseInt(document.getElementById('velocityCount').value) || 10,
                burstWindow: parseInt(document.getElementById('burstWindow').value) || 5
            };

            this.rules.updateConfig(newConfig);

            if (window.app && window.app.showNotification) {
                window.app.showNotification('TTR configuration saved successfully', 'success');
            }

        } catch (error) {
            console.error('Error saving TTR configuration:', error);
            if (window.app && window.app.showNotification) {
                window.app.showNotification('Error saving configuration', 'error');
            }
        }
    }

    // Reset configuration to defaults
    resetConfiguration() {
        if (!this.rules) return;

        this.rules.updateConfig({
            singleThreshold: 1000000000,
            dailyThreshold: 5000000000,
            structuringWindow: 24,
            structuringThreshold: 90,
            velocityCount: 10,
            burstWindow: 5
        });

        this.loadConfiguration();

        if (window.app && window.app.showNotification) {
            window.app.showNotification('TTR configuration reset to defaults', 'info');
        }
    }

    // Export alerts
    exportAlerts() {
        if (!this.exporter || this.filteredAlerts.length === 0) {
            if (window.app && window.app.showNotification) {
                window.app.showNotification('No alerts to export', 'warning');
            }
            return;
        }

        try {
            // Default to Excel export
            const result = this.exporter.exportAlertsToExcel(this.filteredAlerts);

            if (window.app && window.app.showNotification) {
                window.app.showNotification(
                    `Exported ${result.recordCount} alerts to ${result.filename}`,
                    'success'
                );
            }

        } catch (error) {
            console.error('Error exporting alerts:', error);

            // Fallback to CSV if Excel fails
            try {
                const result = this.exporter.exportAlertsToCSV(this.filteredAlerts);
                if (window.app && window.app.showNotification) {
                    window.app.showNotification(
                        `Exported ${result.recordCount} alerts to ${result.filename} (CSV format)`,
                        'success'
                    );
                }
            } catch (csvError) {
                console.error('Error with CSV export fallback:', csvError);
                if (window.app && window.app.showNotification) {
                    window.app.showNotification('Error exporting alerts', 'error');
                }
            }
        }
    }

    // Get current alerts (for external access)
    getAlerts() {
        return [...this.alerts];
    }

    // Get filtered alerts (for external access)
    getFilteredAlerts() {
        return [...this.filteredAlerts];
    }

    // Consolidate alerts by customer and date
    consolidateAlerts(alerts) {
        if (!this.config.consolidateAlerts) {
            return alerts; // Return original alerts if consolidation is disabled
        }

        const consolidatedMap = new Map();

        alerts.forEach(alert => {
            const key = `${alert.customerName}_${alert.date}`;

            if (consolidatedMap.has(key)) {
                // Add to existing consolidated alert
                const consolidated = consolidatedMap.get(key);
                consolidated.individualAlerts.push(alert);
                consolidated.alertTypes.push({
                    type: alert.type,
                    subtype: alert.subtype,
                    description: alert.description,
                    riskScore: alert.riskScore,
                    amount: alert.amount
                });

                // Update consolidated properties
                consolidated.totalAmount += (alert.amount || 0);
                consolidated.maxRiskScore = Math.max(consolidated.maxRiskScore, alert.riskScore || 0);
                consolidated.priority = this.getHighestPriority(consolidated.priority, alert.priority);

                // Update description to include all alert types
                consolidated.description = this.createConsolidatedDescription(consolidated.alertTypes);
                consolidated.riskScore = consolidated.maxRiskScore;
                consolidated.amount = consolidated.totalAmount; // Update display amount

                // Update metadata
                consolidated.metadata.consolidatedCount = consolidated.individualAlerts.length;
                consolidated.metadata.originalAlerts.push(alert.id);

            } else {
                // Create new consolidated alert
                const consolidated = {
                    id: `CONSOLIDATED_${alert.customerName.replace(/\s+/g, '_')}_${alert.date}_${Date.now()}`,
                    type: 'consolidated',
                    subtype: 'multiple_rules',
                    customerName: alert.customerName,
                    customerId: alert.customerId,
                    date: alert.date,
                    timestamp: alert.timestamp,
                    fileId: alert.fileId,
                    status: alert.status,
                    reviewed: false,

                    // Consolidated properties
                    isConsolidated: true,
                    individualAlerts: [alert],
                    alertTypes: [{
                        type: alert.type,
                        subtype: alert.subtype,
                        description: alert.description,
                        riskScore: alert.riskScore,
                        amount: alert.amount
                    }],
                    totalAmount: alert.amount || 0,
                    amount: alert.amount || 0, // For display compatibility
                    maxRiskScore: alert.riskScore || 0,
                    riskScore: alert.riskScore || 0,
                    priority: alert.priority,
                    description: alert.description,

                    // Metadata for drill-down
                    metadata: {
                        consolidatedCount: 1,
                        originalAlerts: [alert.id]
                    }
                };

                consolidatedMap.set(key, consolidated);
            }
        });

        return Array.from(consolidatedMap.values());
    }

    // Get the highest priority between two priorities
    getHighestPriority(priority1, priority2) {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const p1Value = priorityOrder[priority1] || 0;
        const p2Value = priorityOrder[priority2] || 0;

        if (p1Value >= p2Value) return priority1;
        return priority2;
    }

    // Create consolidated description showing all triggered rule types
    createConsolidatedDescription(alertTypes) {
        if (alertTypes.length === 1) {
            return alertTypes[0].description;
        }

        const typeDescriptions = alertTypes.map(alertType => {
            const typeMap = {
                'threshold': 'Threshold Breach',
                'structuring': 'Structuring/Smurfing',
                'velocity': 'Velocity Anomaly',
                'pattern': 'Suspicious Pattern',
                'behavioral': 'Behavioral Anomaly'
            };

            const subtypeMap = {
                'single_transaction': 'Single Transaction',
                'daily_cumulative': 'Daily Cumulative',
                'threshold_avoidance': 'Threshold Avoidance',
                'burst_activity': 'Burst Activity',
                'round_amounts': 'Round Amounts',
                'frequency_spike': 'Frequency Spike'
            };

            const typeDesc = typeMap[alertType.type] || alertType.type;
            const subtypeDesc = subtypeMap[alertType.subtype] || alertType.subtype;

            return `${typeDesc} (${subtypeDesc})`;
        });

        return `Multiple rule violations: ${typeDescriptions.join(', ')}`;
    }

    // Dismiss an alert (change status to inactive)
    dismissAlert(alertId) {
        console.log(`TTR: Attempting to dismiss alert ${alertId}`);

        if (!alertId) {
            console.error('TTR: No alert ID provided for dismissal');
            return false;
        }

        // Check if this is a consolidated alert first
        const consolidatedAlert = this.filteredAlerts.find(a => a.id === alertId && a.isConsolidated);

        if (consolidatedAlert) {
            console.log(`TTR: Found consolidated alert ${alertId}, dismissing ${consolidatedAlert.individualAlerts.length} individual alerts`);

            // Dismiss all individual alerts that make up this consolidated alert
            let dismissedCount = 0;
            for (const individualAlert of consolidatedAlert.individualAlerts) {
                const alertIndex = this.alerts.findIndex(a => a.id === individualAlert.id);
                if (alertIndex !== -1) {
                    this.alerts[alertIndex].status = 'dismissed';
                    this.alerts[alertIndex].dismissedAt = new Date().toISOString();
                    dismissedCount++;
                    console.log(`TTR: Individual alert ${individualAlert.id} marked as dismissed`);
                }
            }

            // Remove the consolidated alert from filtered alerts
            this.filteredAlerts = this.filteredAlerts.filter(a => a.id !== alertId);

            // Apply filters to refresh the display
            this.applyFilters();

            // Update UI metrics
            this.updateUI();

            console.log(`TTR: Consolidated alert ${alertId} dismissed with ${dismissedCount} individual alerts`);
            return dismissedCount > 0;
        }

        // Handle individual alert dismissal
        const alertIndex = this.alerts.findIndex(a => a.id === alertId);

        if (alertIndex === -1) {
            console.error(`TTR: Alert ${alertId} not found in alerts array`);
            return false;
        }

        // Update the alert status
        this.alerts[alertIndex].status = 'dismissed';
        this.alerts[alertIndex].dismissedAt = new Date().toISOString();

        console.log(`TTR: Individual alert ${alertId} status changed to dismissed`);

        // Remove from filtered alerts if present
        this.filteredAlerts = this.filteredAlerts.filter(a => a.id !== alertId);

        // Also remove from any consolidated alerts that might contain this alert
        this.filteredAlerts = this.filteredAlerts.map(alert => {
            if (alert.isConsolidated && alert.individualAlerts) {
                const originalCount = alert.individualAlerts.length;
                // Remove the dismissed alert from individual alerts
                alert.individualAlerts = alert.individualAlerts.filter(indAlert => indAlert.id !== alertId);

                if (alert.individualAlerts.length < originalCount) {
                    console.log(`TTR: Removed dismissed alert ${alertId} from consolidated alert ${alert.id}`);

                    // Update consolidated alert properties
                    if (alert.individualAlerts.length > 0) {
                        // Recalculate consolidated properties
                        alert.totalAmount = alert.individualAlerts.reduce((sum, a) => sum + (a.amount || 0), 0);
                        alert.amount = alert.totalAmount;
                        alert.maxRiskScore = Math.max(...alert.individualAlerts.map(a => a.riskScore || 0));
                        alert.riskScore = alert.maxRiskScore;
                        // Get the highest priority from remaining individual alerts
                        const priorities = alert.individualAlerts.map(a => a.priority);
                        alert.priority = priorities.reduce((highest, current) =>
                            this.getHighestPriority(highest, current), 'low');

                        // Update alert types
                        alert.alertTypes = alert.individualAlerts.map(a => ({
                            type: a.type,
                            subtype: a.subtype,
                            description: a.description,
                            riskScore: a.riskScore,
                            amount: a.amount
                        }));
                    }
                }
            }
            return alert;
        }).filter(alert => {
            // Remove consolidated alerts that have no individual alerts left
            if (alert.isConsolidated && alert.individualAlerts && alert.individualAlerts.length === 0) {
                console.log(`TTR: Removing empty consolidated alert ${alert.id}`);
                return false;
            }
            return true;
        });

        // Apply filters to ensure consistent state
        this.applyFilters();

        // Update UI metrics
        this.updateUI();

        console.log(`TTR: Alert ${alertId} dismissed successfully`);
        return true;
    }

    // Get all alerts (main + filtered for consolidated)
    getAllAlerts() {
        // Return both main alerts and any consolidated alerts from filtered alerts
        const mainAlerts = this.alerts || [];
        const consolidatedAlerts = (this.filteredAlerts || []).filter(a => a.isConsolidated);

        // Create a map to avoid duplicates
        const alertMap = new Map();

        // Add main alerts
        mainAlerts.forEach(alert => {
            alertMap.set(alert.id, alert);
        });

        // Add consolidated alerts (they won't duplicate since they have unique IDs)
        consolidatedAlerts.forEach(alert => {
            alertMap.set(alert.id, alert);
        });

        return Array.from(alertMap.values());
    }

    // Get system status
    getStatus() {
        // Filter to only active alerts for accurate status reporting
        const activeAlerts = this.alerts.filter(a => a.status === 'active' || !a.status);
        const consolidatedAlerts = activeAlerts.filter(a => a.isConsolidated);

        return {
            isInitialized: this.isInitialized,
            totalAlerts: this.alerts.length,
            activeAlerts: activeAlerts.length,
            consolidatedAlerts: consolidatedAlerts.length,
            filteredAlerts: this.filteredAlerts.length,
            processedFiles: this.processedFiles.size,
            highPriorityAlerts: consolidatedAlerts.filter(a => a.priority === 'high').length,
            fileAlertCounts: this.getFileAlertCounts(),
            memoryUsage: {
                alertsCount: this.alerts.length,
                maxAlerts: this.config.maxAlerts,
                utilizationPercent: Math.round((this.alerts.length / this.config.maxAlerts) * 100)
            }
        };
    }

    // Get alert counts by file for debugging
    getFileAlertCounts() {
        const counts = {};
        for (const [fileId, alerts] of this.fileAlerts.entries()) {
            counts[fileId] = alerts.length;
        }
        return counts;
    }

    // Debug method to analyze consolidation
    debugConsolidation() {
        const activeAlerts = this.alerts.filter(a => a.status === 'active' || !a.status);
        const consolidatedAlerts = this.config.consolidateAlerts ?
            this.consolidateAlerts(activeAlerts).filter(a => a.isConsolidated) : [];

        console.log('🔍 TTR Consolidation Debug:');
        console.log(`   Consolidation Enabled: ${this.config.consolidateAlerts}`);
        console.log(`   Total Active Alerts: ${activeAlerts.length}`);
        console.log(`   Consolidated Alerts: ${consolidatedAlerts.length}`);

        // Group alerts by customer/date
        const groups = {};
        activeAlerts.forEach(alert => {
            const key = `${alert.customerName}_${alert.date}`;
            if (!groups[key]) groups[key] = [];
            groups[key].push(alert);
        });

        console.log('   Alert Groups:');
        Object.entries(groups).forEach(([key, alerts]) => {
            console.log(`     ${key}: ${alerts.length} alert(s) - ${alerts.length > 1 ? 'CONSOLIDATABLE' : 'individual'}`);
        });

        return {
            activeAlerts: activeAlerts.length,
            consolidatedAlerts: consolidatedAlerts.length,
            groups: Object.keys(groups).length,
            consolidatableGroups: Object.values(groups).filter(g => g.length > 1).length
        };
    }

    // Debug method to verify data integrity
    verifyDataIntegrity() {
        const issues = [];

        // Check if all alerts in main array have corresponding file tracking
        const alertFileIds = new Set(this.alerts.map(alert => alert.fileId));
        const trackedFileIds = new Set(this.fileAlerts.keys());

        for (const fileId of alertFileIds) {
            if (!trackedFileIds.has(fileId)) {
                issues.push(`Alert exists for untracked file: ${fileId}`);
            }
        }

        // Check if file tracking has alerts that don't exist in main array
        const mainAlertIds = new Set(this.alerts.map(alert => alert.id));
        for (const [fileId, fileAlerts] of this.fileAlerts.entries()) {
            for (const alert of fileAlerts) {
                if (!mainAlertIds.has(alert.id)) {
                    issues.push(`File tracking contains alert not in main array: ${alert.id} (file: ${fileId})`);
                }
            }
        }

        // Check for duplicate alert IDs
        const alertIds = this.alerts.map(alert => alert.id);
        const uniqueIds = new Set(alertIds);
        if (alertIds.length !== uniqueIds.size) {
            issues.push(`Duplicate alert IDs detected`);
        }

        if (issues.length > 0) {
            console.warn('TTR Data Integrity Issues:', issues);
        } else {
            console.log('TTR Data Integrity: All checks passed');
        }

        return {
            isValid: issues.length === 0,
            issues: issues,
            stats: {
                totalAlerts: this.alerts.length,
                trackedFiles: this.fileAlerts.size,
                processedFiles: this.processedFiles.size
            }
        };
    }
}

// Export for use in other modules
window.TTRMonitoring = TTRMonitoring;




