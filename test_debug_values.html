<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Values - Financial Transaction Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .debug-item {
            margin: 5px 0;
            padding: 5px;
            background: #f9f9f9;
            border-left: 3px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .success {
            border-left-color: #28a745;
            background: #f5fff5;
        }
        .warning {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .refresh-btn {
            background: #28a745;
        }
        .fix-btn {
            background: #ffc107;
            color: #000;
        }
        .danger-btn {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>Debug Values - Financial Transaction Dashboard</h1>
        <p>This page helps diagnose why all values are showing as 0.</p>
        
        <button onclick="checkAllValues()" class="refresh-btn">🔍 Check All Values</button>
        <button onclick="checkDataProcessor()" class="refresh-btn">📊 Check DataProcessor</button>
        <button onclick="checkRawData()" class="refresh-btn">📄 Check Raw Data</button>
        <button onclick="checkCreditDebitData()" class="refresh-btn">💳 Check Credit/Debit Data</button>
        <button onclick="testWithSampleData()" class="fix-btn">🧪 Test with Sample Data</button>
        <button onclick="fixAllIssues()" class="fix-btn">🔧 Fix All Issues</button>
        <button onclick="clearAndReload()" class="danger-btn">🔄 Clear & Reload</button>
        
        <div id="debugOutput"></div>
    </div>

    <!-- Include the necessary scripts -->
    <script src="js/constants.js"></script>
    <script src="js/currencyUtils.js"></script>
    <script src="js/dataProcessor.js"></script>
    <script src="js/fileHandler.js"></script>

    <script>
        function addDebugSection(title, items, className = '') {
            const output = document.getElementById('debugOutput');
            const section = document.createElement('div');
            section.className = `debug-section ${className}`;
            
            const titleDiv = document.createElement('div');
            titleDiv.className = 'debug-title';
            titleDiv.textContent = title;
            section.appendChild(titleDiv);
            
            items.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.className = `debug-item ${item.type || ''}`;
                itemDiv.innerHTML = item.text;
                section.appendChild(itemDiv);
            });
            
            output.appendChild(section);
        }

        function clearDebugOutput() {
            document.getElementById('debugOutput').innerHTML = '';
        }

        function checkAllValues() {
            clearDebugOutput();
            
            // Check if DataProcessor exists
            const items = [];
            
            if (window.dataProcessor) {
                items.push({text: '✅ DataProcessor is initialized', type: 'success'});
                
                // Check summary metrics
                const metrics = window.dataProcessor.summaryMetrics;
                if (metrics) {
                    items.push({text: `📊 Total Transactions: ${metrics.totalTransactions}`, type: metrics.totalTransactions > 0 ? 'success' : 'error'});
                    items.push({text: `💰 Total Amount: ${metrics.totalAmount}`, type: metrics.totalAmount > 0 ? 'success' : 'error'});
                    items.push({text: `🏢 HOC Count: ${metrics.hocCount}`, type: metrics.hocCount > 0 ? 'success' : 'warning'});
                    items.push({text: `🏦 IBD Count: ${metrics.ibdCount}`, type: metrics.ibdCount > 0 ? 'success' : 'warning'});
                    items.push({text: `💸 WU Count: ${metrics.wuCount || 0}`, type: (metrics.wuCount || 0) > 0 ? 'success' : 'warning'});
                    
                    // Check currency amounts
                    const currencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
                    currencies.forEach(currency => {
                        const amount = metrics.currencyAmounts ? metrics.currencyAmounts[currency] : 0;
                        items.push({text: `💱 ${currency}: ${amount || 0}`, type: amount > 0 ? 'success' : 'warning'});
                    });
                } else {
                    items.push({text: '❌ Summary metrics not found', type: 'error'});
                }
                
                // Check raw data
                const rawData = window.dataProcessor.rawData;
                if (rawData && rawData.length > 0) {
                    items.push({text: `📄 Raw Data: ${rawData.length} records`, type: 'success'});
                } else {
                    items.push({text: '❌ No raw data found', type: 'error'});
                }
            } else {
                items.push({text: '❌ DataProcessor not initialized', type: 'error'});
            }
            
            addDebugSection('Current Values Check', items);
        }

        function checkDataProcessor() {
            clearDebugOutput();
            
            const items = [];
            
            if (window.dataProcessor) {
                items.push({text: '✅ DataProcessor exists', type: 'success'});
                
                // Check methods
                const methods = ['processData', 'updateMetrics', 'updateUI', 'recalculateCurrencyMetrics'];
                methods.forEach(method => {
                    if (typeof window.dataProcessor[method] === 'function') {
                        items.push({text: `✅ Method ${method} exists`, type: 'success'});
                    } else {
                        items.push({text: `❌ Method ${method} missing`, type: 'error'});
                    }
                });
                
                // Check properties
                const properties = ['summaryMetrics', 'rawData', 'filteredData'];
                properties.forEach(prop => {
                    if (window.dataProcessor[prop] !== undefined) {
                        items.push({text: `✅ Property ${prop} exists`, type: 'success'});
                    } else {
                        items.push({text: `❌ Property ${prop} missing`, type: 'error'});
                    }
                });
            } else {
                items.push({text: '❌ DataProcessor not found', type: 'error'});
            }
            
            // Check FileHandler
            if (window.fileHandler) {
                items.push({text: '✅ FileHandler exists', type: 'success'});
                
                if (window.fileHandler.files && window.fileHandler.files.length > 0) {
                    items.push({text: `📁 Files loaded: ${window.fileHandler.files.length}`, type: 'success'});
                } else {
                    items.push({text: '❌ No files loaded', type: 'error'});
                }
            } else {
                items.push({text: '❌ FileHandler not found', type: 'error'});
            }
            
            addDebugSection('DataProcessor & FileHandler Check', items);
        }

        function checkRawData() {
            clearDebugOutput();
            
            const items = [];
            
            if (window.dataProcessor && window.dataProcessor.rawData) {
                const rawData = window.dataProcessor.rawData;
                items.push({text: `📄 Raw data length: ${rawData.length}`, type: rawData.length > 0 ? 'success' : 'error'});
                
                if (rawData.length > 0) {
                    // Check first few records
                    const sample = rawData.slice(0, 3);
                    sample.forEach((record, index) => {
                        items.push({text: `Record ${index + 1}: ${JSON.stringify(record).substring(0, 100)}...`, type: 'success'});
                    });
                    
                    // Check for required fields
                    const requiredFields = ['TRANSACTION_AMOUNT', 'REPORTTYPE', 'TRANSACTION_CURRENCY', 'ACCOUNT_HOLDER_ACCOUNT_ROLE'];
                    requiredFields.forEach(field => {
                        const hasField = rawData[0] && rawData[0][field] !== undefined;
                        items.push({text: `Field ${field}: ${hasField ? '✅ Present' : '❌ Missing'}`, type: hasField ? 'success' : 'error'});
                    });
                }
            } else {
                items.push({text: '❌ No raw data available', type: 'error'});
            }
            
            addDebugSection('Raw Data Check', items);
        }

        function fixAllIssues() {
            clearDebugOutput();
            
            const items = [];
            
            if (window.dataProcessor) {
                try {
                    // Try to recalculate metrics
                    items.push({text: '🔧 Attempting to recalculate currency metrics...', type: 'warning'});
                    window.dataProcessor.recalculateCurrencyMetrics();
                    items.push({text: '✅ Currency metrics recalculated', type: 'success'});
                    
                    // Try to validate calculations
                    items.push({text: '🔧 Validating currency calculations...', type: 'warning'});
                    const issuesFixed = window.dataProcessor.validateAndFixCurrencyCalculations();
                    items.push({text: `✅ Fixed ${issuesFixed} calculation issues`, type: 'success'});
                    
                    // Try to fix WU processing
                    items.push({text: '🔧 Checking WU transaction processing...', type: 'warning'});
                    const wuFixed = window.dataProcessor.fixWUTransactionProcessing();
                    items.push({text: `${wuFixed ? '✅' : '⚠️'} WU processing ${wuFixed ? 'fixed' : 'checked'}`, type: wuFixed ? 'success' : 'warning'});
                    
                    // Update UI
                    items.push({text: '🔧 Updating UI...', type: 'warning'});
                    window.dataProcessor.updateUI();
                    items.push({text: '✅ UI updated', type: 'success'});
                    
                } catch (error) {
                    items.push({text: `❌ Error during fix: ${error.message}`, type: 'error'});
                }
            } else {
                items.push({text: '❌ Cannot fix: DataProcessor not available', type: 'error'});
            }
            
            addDebugSection('Fix Attempts', items);
            
            // Recheck values after fix
            setTimeout(() => {
                checkAllValues();
            }, 1000);
        }

        function testWithSampleData() {
            clearDebugOutput();

            const items = [];

            if (window.dataProcessor) {
                try {
                    // Create sample transaction data
                    const sampleData = [
                        {
                            REPORTTYPE: 'HOC',
                            TRANSACTION_AMOUNT: **********,
                            TRANSACTION_CURRENCY: 'MMK',
                            ACCOUNT_HOLDER_ACCOUNT_ROLE: 'C',
                            TRANSACTION_DATE: '2024-01-15',
                            SERIAL_NO: 'HOC001',
                            CUSTOMER_NAME: 'Test Customer 1',
                            PARTICIPANT_NAME_CONDUCTOR: 'Test Conductor',
                            PARTICIPANT_NAME_COUNTERPARTY: 'Test Counterparty',
                            PARTICIPANT_ID_NUMBER_CONDUCTOR: 'TC001',
                            PARTICIPANT_ID_NUMBER_COUNTERPARTY: 'TCP001',
                            TRANSACTION_ID: 'TXN001',
                            CUSTOMER_ID: 'CUST001',
                            ACCOUNT_NUMBER: 'ACC001',
                            TRANSACTION_TYPE: 'Transfer',
                            CHANNEL: 'Online',
                            LOCATION: 'Yangon',
                            BUSINESS_TYPE: 'Banking'
                        },
                        {
                            REPORTTYPE: 'IBD',
                            TRANSACTION_AMOUNT: 15000,
                            TRANSACTION_CURRENCY: 'USD',
                            ACCOUNT_HOLDER_ACCOUNT_ROLE: 'D',
                            TRANSACTION_DATE: '2024-01-15',
                            SERIAL_NO: 'IBD001',
                            CUSTOMER_NAME: 'Test Customer 2',
                            PARTICIPANT_NAME_CONDUCTOR: 'Test Conductor 2',
                            PARTICIPANT_NAME_COUNTERPARTY: 'Test Counterparty 2',
                            PARTICIPANT_ID_NUMBER_CONDUCTOR: 'TC002',
                            PARTICIPANT_ID_NUMBER_COUNTERPARTY: 'TCP002',
                            TRANSACTION_ID: 'TXN002',
                            CUSTOMER_ID: 'CUST002',
                            ACCOUNT_NUMBER: 'ACC002',
                            TRANSACTION_TYPE: 'Transfer',
                            CHANNEL: 'Branch',
                            LOCATION: 'Mandalay',
                            BUSINESS_TYPE: 'Banking'
                        },
                        {
                            REPORTTYPE: 'WU',
                            TRANSACTION_AMOUNT: 500,
                            TRANSACTION_CURRENCY: 'USD',
                            ACCOUNT_HOLDER_ACCOUNT_ROLE: 'C',
                            TRANSACTION_DATE: '2024-01-15',
                            SERIAL_NO: 'WU001',
                            CUSTOMER_NAME: 'Test Customer 3',
                            PARTICIPANT_NAME_CONDUCTOR: 'Test Conductor 3',
                            PARTICIPANT_NAME_COUNTERPARTY: 'Test Counterparty 3',
                            PARTICIPANT_ID_NUMBER_CONDUCTOR: 'TC003',
                            PARTICIPANT_ID_NUMBER_COUNTERPARTY: 'TCP003',
                            TRANSACTION_ID: 'TXN003',
                            CUSTOMER_ID: 'CUST003',
                            ACCOUNT_NUMBER: 'ACC003',
                            TRANSACTION_TYPE: 'Remittance',
                            CHANNEL: 'Agent',
                            LOCATION: 'Naypyidaw',
                            BUSINESS_TYPE: 'Money Transfer'
                        }
                    ];

                    items.push({text: '🧪 Testing with sample data...', type: 'warning'});
                    items.push({text: `📄 Sample data created: ${sampleData.length} transactions`, type: 'success'});

                    // Process the sample data
                    window.dataProcessor.processData(sampleData);
                    items.push({text: '✅ Sample data processed', type: 'success'});

                    // Check the results
                    const metrics = window.dataProcessor.summaryMetrics;
                    items.push({text: `📊 Total Transactions: ${metrics.totalTransactions}`, type: metrics.totalTransactions > 0 ? 'success' : 'error'});
                    items.push({text: `💰 Total Amount: ${metrics.totalAmount}`, type: metrics.totalAmount > 0 ? 'success' : 'error'});
                    items.push({text: `🏢 HOC Count: ${metrics.hocCount}`, type: metrics.hocCount > 0 ? 'success' : 'error'});
                    items.push({text: `🏦 IBD Count: ${metrics.ibdCount}`, type: metrics.ibdCount > 0 ? 'success' : 'error'});
                    items.push({text: `💸 WU Count: ${metrics.wuCount}`, type: metrics.wuCount > 0 ? 'success' : 'error'});

                    // Check currency amounts
                    items.push({text: `💱 MMK Amount: ${metrics.currencyAmounts.MMK}`, type: metrics.currencyAmounts.MMK > 0 ? 'success' : 'error'});
                    items.push({text: `💱 USD Amount: ${metrics.currencyAmounts.USD}`, type: metrics.currencyAmounts.USD > 0 ? 'success' : 'error'});

                } catch (error) {
                    items.push({text: `❌ Error during test: ${error.message}`, type: 'error'});
                }
            } else {
                items.push({text: '❌ Cannot test: DataProcessor not available', type: 'error'});
            }

            addDebugSection('Sample Data Test', items);
        }

        function checkCreditDebitData() {
            clearDebugOutput();

            const items = [];

            if (window.dataProcessor && window.dataProcessor.rawData) {
                const rawData = window.dataProcessor.rawData;
                items.push({text: `📄 Raw data length: ${rawData.length}`, type: rawData.length > 0 ? 'success' : 'error'});

                if (rawData.length > 0) {
                    // Analyze credit/debit distribution
                    let creditCount = 0;
                    let debitCount = 0;
                    let otherCount = 0;
                    let creditAmount = 0;
                    let debitAmount = 0;

                    const roleDistribution = {};
                    const currencyDistribution = {};

                    rawData.forEach((record, index) => {
                        const role = record.ACCOUNT_HOLDER_ACCOUNT_ROLE;
                        const amount = parseFloat(record.TRANSACTION_AMOUNT) || 0;
                        const currency = record.TRANSACTION_CURRENCY || 'MMK';

                        // Count roles
                        roleDistribution[role] = (roleDistribution[role] || 0) + 1;

                        // Count currencies
                        currencyDistribution[currency] = (currencyDistribution[currency] || 0) + 1;

                        if (role === 'C') {
                            creditCount++;
                            creditAmount += amount;
                        } else if (role === 'D') {
                            debitCount++;
                            debitAmount += amount;
                        } else {
                            otherCount++;
                        }

                        // Show first few records for inspection
                        if (index < 5) {
                            items.push({text: `Record ${index + 1}: Role=${role}, Amount=${amount}, Currency=${currency}`, type: 'success'});
                        }
                    });

                    items.push({text: `💳 Credit transactions (C): ${creditCount} (${creditAmount.toLocaleString()})`, type: creditCount > 0 ? 'success' : 'error'});
                    items.push({text: `💸 Debit transactions (D): ${debitCount} (${debitAmount.toLocaleString()})`, type: debitCount > 0 ? 'success' : 'warning'});
                    items.push({text: `❓ Other roles: ${otherCount}`, type: otherCount === 0 ? 'success' : 'warning'});

                    // Show role distribution
                    items.push({text: `📊 Role Distribution:`, type: 'success'});
                    Object.entries(roleDistribution).forEach(([role, count]) => {
                        items.push({text: `  - ${role}: ${count} transactions`, type: 'success'});
                    });

                    // Show currency distribution
                    items.push({text: `💱 Currency Distribution:`, type: 'success'});
                    Object.entries(currencyDistribution).forEach(([currency, count]) => {
                        items.push({text: `  - ${currency}: ${count} transactions`, type: 'success'});
                    });

                    // Check current metrics
                    const metrics = window.dataProcessor.summaryMetrics;
                    if (metrics) {
                        items.push({text: `📈 Current Metrics:`, type: 'success'});
                        items.push({text: `  - MMK Credit: ${metrics.currencyCreditAmounts.MMK}`, type: metrics.currencyCreditAmounts.MMK > 0 ? 'success' : 'error'});
                        items.push({text: `  - MMK Debit: ${metrics.currencyDebitAmounts.MMK}`, type: metrics.currencyDebitAmounts.MMK > 0 ? 'success' : 'warning'});
                        items.push({text: `  - MMK Net: ${metrics.currencyAmounts.MMK}`, type: 'success'});
                    }
                }
            } else {
                items.push({text: '❌ No raw data available', type: 'error'});
            }

            addDebugSection('Credit/Debit Data Analysis', items);
        }

        function clearAndReload() {
            if (confirm('This will clear all data and reload the page. Continue?')) {
                localStorage.clear();
                sessionStorage.clear();
                window.location.reload();
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            // Wait a bit for scripts to load
            setTimeout(() => {
                checkAllValues();
            }, 1000);
        });
    </script>
</body>
</html>
