/**
 * TTR Rules Engine
 * Implements detection algorithms for Anti-Money Laundering compliance
 */

class TTRRules {
    constructor() {
        // Default configuration values
        this.config = {
            // Threshold settings
            singleThreshold: 1000000000, // MMK 1 billion
            dailyThreshold: 5000000000,  // MMK 5 billion

            // Structuring detection
            structuringWindow: 24,     // hours
            structuringThreshold: 90,  // percentage of limit

            // Velocity monitoring
            velocityCount: 10,         // max transactions per hour
            burstWindow: 5,           // minutes for burst detection

            // Pattern analysis
            dormantPeriod: 90,        // days
            roundAmountThreshold: 0.97, // 97% of transactions being round amounts

            // Risk scoring weights
            weights: {
                threshold: 40,
                structuring: 30,
                velocity: 15,
                pattern: 5,
                behavioral: 5
            }
        };

        // Customer transaction history for behavioral analysis
        this.customerHistory = new Map();

        // Alert ID counter
        this.alertIdCounter = 1;
    }

    // Update configuration
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('TTR Rules configuration updated:', this.config);
    }

    // Main method to analyze transactions and generate alerts with performance optimization
    analyzeTransactions(transactions, fileId) {
        const alerts = [];

        // Early exit for empty transactions
        if (!transactions || transactions.length === 0) {
            return alerts;
        }

        // Pre-filter transactions to reduce processing overhead
        const mmkTransactions = [];
        const transactionCount = transactions.length;

        // Single pass to filter MMK transactions and cache parsed amounts
        for (let i = 0; i < transactionCount; i++) {
            const transaction = transactions[i];
            if (transaction.TRANSACTION_CURRENCY === 'MMK') {
                // Pre-parse amount for performance
                transaction._cachedAmount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                mmkTransactions.push(transaction);
            }
        }

        // Skip processing if no MMK transactions (most rules only apply to MMK)
        if (mmkTransactions.length === 0) {
            return alerts;
        }

        // Group transactions by customer for analysis (only MMK transactions)
        const customerGroups = this.groupTransactionsByCustomer(mmkTransactions);

        // Skip processing if insufficient transactions for pattern analysis
        if (mmkTransactions.length < 20) {
            // Only run threshold detection for small datasets
            alerts.push(...this.detectThresholdBreaches(mmkTransactions, fileId));
        } else {
            // Run all detection rules for larger datasets
            alerts.push(...this.detectThresholdBreaches(mmkTransactions, fileId));
            alerts.push(...this.detectStructuring(customerGroups, fileId));
            alerts.push(...this.detectVelocityAnomalies(customerGroups, fileId));
            alerts.push(...this.detectSuspiciousPatterns(customerGroups, fileId));
            alerts.push(...this.detectBehavioralAnomalies(customerGroups, fileId));
        }

        // Batch calculate risk scores and priorities
        const alertsLength = alerts.length;
        for (let i = 0; i < alertsLength; i++) {
            const alert = alerts[i];
            alert.riskScore = this.calculateRiskScore(alert);
            alert.priority = this.determinePriority(alert.riskScore);
        }

        // Sort alerts by risk score (highest first)
        alerts.sort((a, b) => b.riskScore - a.riskScore);

        return alerts;
    }

    // 1. Threshold Breach Detection (optimized)
    detectThresholdBreaches(transactions, fileId) {
        const alerts = [];
        const dailyTotals = new Map();
        const transactionCount = transactions.length;

        for (let i = 0; i < transactionCount; i++) {
            const transaction = transactions[i];

            // Use cached amount if available, otherwise parse
            const amount = transaction._cachedAmount !== undefined ?
                transaction._cachedAmount :
                parseFloat(transaction.TRANSACTION_AMOUNT) || 0;

            const date = this.extractDate(transaction.TRANSACTION_DATE);
            const customerId = this.getCustomerId(transaction);

            // Single transaction threshold breach
            if (amount >= this.config.singleThreshold) {
                alerts.push(this.createAlert({
                    type: 'threshold',
                    subtype: 'single_transaction',
                    customerId: customerId,
                    customerName: transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                    amount: amount,
                    date: date,
                    transactionId: transaction.TRANSACTION_ID || `${fileId}_${this.alertIdCounter}`,
                    description: `Single transaction exceeds threshold: ${this.formatCurrency(amount)}`,
                    fileId: fileId,
                    rawTransaction: transaction
                }));
            }

            // Daily cumulative tracking
            const dailyKey = `${customerId}_${date}`;
            let dailyData = dailyTotals.get(dailyKey);

            if (!dailyData) {
                dailyData = {
                    customerId: customerId,
                    customerName: transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                    date: date,
                    total: 0,
                    count: 0,
                    transactions: []
                };
                dailyTotals.set(dailyKey, dailyData);
            }

            dailyData.total += amount;
            dailyData.count++;
            dailyData.transactions.push(transaction);

            // Check daily cumulative threshold
            if (dailyData.total >= this.config.dailyThreshold) {
                alerts.push(this.createAlert({
                    type: 'threshold',
                    subtype: 'daily_cumulative',
                    customerId: customerId,
                    customerName: dailyData.customerName,
                    amount: dailyData.total,
                    date: date,
                    transactionId: `DAILY_${dailyKey}`,
                    description: `Daily cumulative amount exceeds threshold: ${this.formatCurrency(dailyData.total)} (${dailyData.count} transactions)`,
                    fileId: fileId,
                    metadata: {
                        transactionCount: dailyData.count,
                        transactions: dailyData.transactions
                    }
                }));
            }
        }

        return alerts;
    }

    // 2. Structuring/Smurfing Detection
    detectStructuring(customerGroups, fileId) {
        const alerts = [];
        const structuringLimit = this.config.singleThreshold * (this.config.structuringThreshold / 100);

        customerGroups.forEach((transactions, customerId) => {
            // Group transactions by time windows
            const timeWindows = this.groupTransactionsByTimeWindow(transactions, this.config.structuringWindow);

            timeWindows.forEach(windowTransactions => {
                const suspiciousTransactions = windowTransactions.filter(t => {
                    const amount = parseFloat(t.TRANSACTION_AMOUNT) || 0;
                    return t.TRANSACTION_CURRENCY === 'MMK' &&
                           amount >= structuringLimit &&
                           amount < this.config.singleThreshold;
                });

                // Flag if multiple transactions just below threshold
                if (suspiciousTransactions.length >= 3) {
                    const totalAmount = suspiciousTransactions.reduce((sum, t) =>
                        sum + (parseFloat(t.TRANSACTION_AMOUNT) || 0), 0);

                    alerts.push(this.createAlert({
                        type: 'structuring',
                        subtype: 'threshold_avoidance',
                        customerId: customerId,
                        customerName: suspiciousTransactions[0].CUSTOMER_NAME ||
                                    suspiciousTransactions[0].PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                        amount: totalAmount,
                        date: this.extractDate(suspiciousTransactions[0].TRANSACTION_DATE),
                        transactionId: `STRUCT_${customerId}_${Date.now()}`,
                        description: `Potential structuring: ${suspiciousTransactions.length} transactions totaling ${this.formatCurrency(totalAmount)} within ${this.config.structuringWindow} hours`,
                        fileId: fileId,
                        metadata: {
                            transactionCount: suspiciousTransactions.length,
                            timeWindow: this.config.structuringWindow,
                            transactions: suspiciousTransactions
                        }
                    }));
                }
            });
        });

        return alerts;
    }

    // 3. Velocity-Based Monitoring
    detectVelocityAnomalies(customerGroups, fileId) {
        const alerts = [];

        customerGroups.forEach((transactions, customerId) => {
            // Check hourly transaction velocity
            const hourlyGroups = this.groupTransactionsByTimeWindow(transactions, 1); // 1 hour windows

            hourlyGroups.forEach(hourlyTransactions => {
                if (hourlyTransactions.length > this.config.velocityCount) {
                    const totalAmount = hourlyTransactions.reduce((sum, t) =>
                        sum + (parseFloat(t.TRANSACTION_AMOUNT) || 0), 0);

                    alerts.push(this.createAlert({
                        type: 'velocity',
                        subtype: 'high_frequency',
                        customerId: customerId,
                        customerName: hourlyTransactions[0].CUSTOMER_NAME ||
                                    hourlyTransactions[0].PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                        amount: totalAmount,
                        date: this.extractDate(hourlyTransactions[0].TRANSACTION_DATE),
                        transactionId: `VEL_${customerId}_${Date.now()}`,
                        description: `High velocity: ${hourlyTransactions.length} transactions in 1 hour (limit: ${this.config.velocityCount})`,
                        fileId: fileId,
                        metadata: {
                            transactionCount: hourlyTransactions.length,
                            timeWindow: 1,
                            transactions: hourlyTransactions
                        }
                    }));
                }
            });

            // Check for burst activity (multiple transactions within minutes)
            const burstGroups = this.groupTransactionsByTimeWindow(transactions, this.config.burstWindow / 60); // Convert minutes to hours

            burstGroups.forEach(burstTransactions => {
                if (burstTransactions.length >= 3) { // 3+ transactions within burst window
                    const totalAmount = burstTransactions.reduce((sum, t) =>
                        sum + (parseFloat(t.TRANSACTION_AMOUNT) || 0), 0);

                    alerts.push(this.createAlert({
                        type: 'velocity',
                        subtype: 'burst_activity',
                        customerId: customerId,
                        customerName: burstTransactions[0].CUSTOMER_NAME ||
                                    burstTransactions[0].PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                        amount: totalAmount,
                        date: this.extractDate(burstTransactions[0].TRANSACTION_DATE),
                        transactionId: `BURST_${customerId}_${Date.now()}`,
                        description: `Burst activity: ${burstTransactions.length} transactions within ${this.config.burstWindow} minutes`,
                        fileId: fileId,
                        metadata: {
                            transactionCount: burstTransactions.length,
                            timeWindow: this.config.burstWindow,
                            transactions: burstTransactions
                        }
                    }));
                }
            });
        });

        return alerts;
    }

    // 4. Suspicious Pattern Analysis
    detectSuspiciousPatterns(customerGroups, fileId) {
        const alerts = [];

        customerGroups.forEach((transactions, customerId) => {
            // Check for round amount clustering
            const roundAmounts = transactions.filter(t => {
                const amount = parseFloat(t.TRANSACTION_AMOUNT) || 0;
                return this.isRoundAmount(amount);
            });

            const roundAmountRatio = roundAmounts.length / transactions.length;
            if (roundAmountRatio >= this.config.roundAmountThreshold && transactions.length >= 20) {
                const totalAmount = roundAmounts.reduce((sum, t) =>
                    sum + (parseFloat(t.TRANSACTION_AMOUNT) || 0), 0);

                alerts.push(this.createAlert({
                    type: 'pattern',
                    subtype: 'round_amounts',
                    customerId: customerId,
                    customerName: transactions[0].CUSTOMER_NAME ||
                                transactions[0].PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                    amount: totalAmount,
                    date: this.extractDate(transactions[0].TRANSACTION_DATE),
                    transactionId: `ROUND_${customerId}_${Date.now()}`,
                    description: `Suspicious round amount pattern: ${Math.round(roundAmountRatio * 100)}% of transactions are round amounts`,
                    fileId: fileId,
                    metadata: {
                        roundAmountRatio: roundAmountRatio,
                        roundTransactionCount: roundAmounts.length,
                        totalTransactionCount: transactions.length,
                        transactions: roundAmounts
                    }
                }));
            }
        });

        return alerts;
    }

    // 5. Behavioral Anomaly Detection
    detectBehavioralAnomalies(customerGroups, fileId) {
        const alerts = [];

        customerGroups.forEach((transactions, customerId) => {
            // Skip if not enough transactions for behavioral analysis
            if (transactions.length < 3) return;

            // Check for dormant account reactivation
            const sortedTransactions = transactions.sort((a, b) =>
                new Date(a.TRANSACTION_DATE) - new Date(b.TRANSACTION_DATE));

            const firstTransaction = new Date(sortedTransactions[0].TRANSACTION_DATE);
            const lastTransaction = new Date(sortedTransactions[sortedTransactions.length - 1].TRANSACTION_DATE);
            const daysBetween = (lastTransaction - firstTransaction) / (1000 * 60 * 60 * 24);

            // Check for sudden large transactions after dormancy
            if (daysBetween > this.config.dormantPeriod) {
                const largeTransactions = transactions.filter(t => {
                    const amount = parseFloat(t.TRANSACTION_AMOUNT) || 0;
                    return amount >= this.config.singleThreshold * 0.5; // 50% of threshold
                });

                if (largeTransactions.length > 0) {
                    const totalAmount = largeTransactions.reduce((sum, t) =>
                        sum + (parseFloat(t.TRANSACTION_AMOUNT) || 0), 0);

                    alerts.push(this.createAlert({
                        type: 'behavioral',
                        subtype: 'dormant_reactivation',
                        customerId: customerId,
                        customerName: transactions[0].CUSTOMER_NAME ||
                                    transactions[0].PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                        amount: totalAmount,
                        date: this.extractDate(lastTransaction.toISOString()),
                        transactionId: `DORMANT_${customerId}_${Date.now()}`,
                        description: `Dormant account reactivation: Large transactions after ${Math.round(daysBetween)} days of inactivity`,
                        fileId: fileId,
                        metadata: {
                            dormantDays: Math.round(daysBetween),
                            largeTransactionCount: largeTransactions.length,
                            deviationMultiple: daysBetween / this.config.dormantPeriod,
                            transactions: largeTransactions
                        }
                    }));
                }
            }

            // Check for unusual transaction patterns (frequency spikes)
            const dailyGroups = this.groupTransactionsByDate(transactions);
            const dailyCounts = Array.from(dailyGroups.values()).map(dayTxs => dayTxs.length);

            if (dailyCounts.length > 1) {
                const avgDaily = dailyCounts.reduce((sum, count) => sum + count, 0) / dailyCounts.length;
                const maxDaily = Math.max(...dailyCounts);

                // Flag if any day has significantly more transactions than average
                if (maxDaily > avgDaily * 3 && maxDaily >= 5) {
                    const spikeEntry = Array.from(dailyGroups.entries())
                        .find(([, txs]) => txs.length === maxDaily);

                    if (spikeEntry) {
                        const [spikeDate, spikeTxs] = spikeEntry;
                        const totalAmount = spikeTxs.reduce((sum, t) =>
                            sum + (parseFloat(t.TRANSACTION_AMOUNT) || 0), 0);

                        alerts.push(this.createAlert({
                            type: 'behavioral',
                            subtype: 'frequency_spike',
                            customerId: customerId,
                            customerName: spikeTxs[0].CUSTOMER_NAME ||
                                        spikeTxs[0].PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                            amount: totalAmount,
                            date: spikeDate,
                            transactionId: `SPIKE_${customerId}_${Date.now()}`,
                            description: `Unusual frequency spike: ${maxDaily} transactions on ${spikeDate} (avg: ${Math.round(avgDaily)})`,
                            fileId: fileId,
                            metadata: {
                                spikeCount: maxDaily,
                                averageDaily: Math.round(avgDaily),
                                deviationMultiple: maxDaily / avgDaily,
                                transactions: spikeTxs
                            }
                        }));
                    }
                }
            }
        });

        return alerts;
    }

    // Helper Methods

    // Create a standardized alert object
    createAlert(alertData) {
        return {
            id: `TTR_${this.alertIdCounter++}`,
            timestamp: new Date().toISOString(),
            ...alertData,
            status: 'active',
            reviewed: false,
            riskScore: 0, // Will be calculated later
            priority: 'low' // Will be determined later
        };
    }

    // Calculate risk score based on alert type and metadata
    calculateRiskScore(alert) {
        let score = 0;
        const weights = this.config.weights;

        switch (alert.type) {
            case 'threshold':
                score = weights.threshold;
                if (alert.subtype === 'single_transaction') {
                    const multiplier = alert.amount / this.config.singleThreshold;
                    score += Math.min(multiplier * 10, 30); // Up to 30 additional points
                } else if (alert.subtype === 'daily_cumulative') {
                    const multiplier = alert.amount / this.config.dailyThreshold;
                    score += Math.min(multiplier * 15, 25); // Up to 25 additional points
                }
                break;

            case 'structuring':
                score = weights.structuring;
                if (alert.metadata && alert.metadata.transactionCount) {
                    score += Math.min(alert.metadata.transactionCount * 3, 20);
                }
                break;

            case 'velocity':
                score = weights.velocity;
                if (alert.metadata && alert.metadata.transactionCount) {
                    const excess = alert.metadata.transactionCount - this.config.velocityCount;
                    score += Math.min(excess * 2, 15);
                }
                break;

            case 'pattern':
                score = weights.pattern;
                if (alert.metadata && alert.metadata.roundAmountRatio) {
                    score += alert.metadata.roundAmountRatio * 10;
                }
                break;

            case 'behavioral':
                score = weights.behavioral;
                if (alert.metadata && alert.metadata.deviationMultiple) {
                    score += Math.min(alert.metadata.deviationMultiple * 3, 15);
                }
                break;
        }

        return Math.min(Math.round(score), 100); // Cap at 100
    }

    // Determine priority based on risk score
    determinePriority(riskScore) {
        if (riskScore >= 80) return 'high';
        if (riskScore >= 50) return 'medium';
        return 'low';
    }

    // Group transactions by customer
    groupTransactionsByCustomer(transactions) {
        const groups = new Map();

        transactions.forEach(transaction => {
            const customerId = this.getCustomerId(transaction);
            if (!groups.has(customerId)) {
                groups.set(customerId, []);
            }
            groups.get(customerId).push(transaction);
        });

        return groups;
    }

    // Group transactions by date
    groupTransactionsByDate(transactions) {
        const groups = new Map();

        transactions.forEach(transaction => {
            const date = this.extractDate(transaction.TRANSACTION_DATE);
            if (!groups.has(date)) {
                groups.set(date, []);
            }
            groups.get(date).push(transaction);
        });

        return groups;
    }

    // Group transactions by time windows (in hours)
    groupTransactionsByTimeWindow(transactions, windowHours) {
        const windows = [];
        const sortedTransactions = transactions.sort((a, b) =>
            new Date(a.TRANSACTION_DATE) - new Date(b.TRANSACTION_DATE));

        let currentWindow = [];
        let windowStart = null;

        sortedTransactions.forEach(transaction => {
            const transactionTime = new Date(transaction.TRANSACTION_DATE);

            if (!windowStart) {
                windowStart = transactionTime;
                currentWindow = [transaction];
            } else {
                const timeDiff = (transactionTime - windowStart) / (1000 * 60 * 60); // Convert to hours

                if (timeDiff <= windowHours) {
                    currentWindow.push(transaction);
                } else {
                    if (currentWindow.length > 0) {
                        windows.push([...currentWindow]);
                    }
                    windowStart = transactionTime;
                    currentWindow = [transaction];
                }
            }
        });

        if (currentWindow.length > 0) {
            windows.push(currentWindow);
        }

        return windows;
    }

    // Extract customer ID from transaction
    getCustomerId(transaction) {
        return transaction.CUSTOMER_ID ||
               transaction.CUSTOMER_NAME ||
               transaction.PARTICIPANT_NAME_CONDUCTOR ||
               transaction.ACCOUNT_NUMBER ||
               'UNKNOWN';
    }

    // Extract date from transaction date
    extractDate(dateString) {
        if (!dateString) return 'Unknown';

        try {
            // Extract just the date part if there's a space (time component)
            let dateStr = dateString;
            if (dateStr.includes(' ')) {
                dateStr = dateStr.split(' ')[0];
            }

            // Format: "DD-MMM-YY" (e.g., "01-JAN-23")
            if (/^\d{2}-[A-Z]{3}-\d{2}$/.test(dateStr)) {
                const [day, month, year] = dateStr.split('-');
                const monthMap = {
                    'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04', 'MAY': '05', 'JUN': '06',
                    'JUL': '07', 'AUG': '08', 'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
                };

                // Assume 20xx for years less than 50, 19xx otherwise
                const fullYear = parseInt(year) < 50 ? `20${year}` : `19${year}`;

                // Return in YYYY-MM-DD format for consistency
                return `${fullYear}-${monthMap[month]}-${day}`;
            }

            // Format: "YYYY-MM-DD" (ISO format)
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                return dateStr;
            }

            // Try to parse as a standard date
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0]; // YYYY-MM-DD format
            }

            // If all else fails, return the original string
            return dateStr;
        } catch (error) {
            console.warn('Invalid date format:', dateString, error);
            return 'Unknown';
        }
    }

    // Check if amount is a "round" amount
    isRoundAmount(amount) {
        if (amount <= 0) return false;

        // Check for round millions, hundreds of thousands, etc.
        const roundThresholds = [
            1000000,    // 1 million
            5000000,    // 5 million
            10000000,   // 10 million
            100000000,  // 100 million
            500000,     // 500 thousand
            1000000000  // 1 billion
        ];

        return roundThresholds.some(threshold => amount === threshold) ||
               (amount % 1000000 === 0 && amount >= 1000000) || // Exact millions
               (amount % 500000 === 0 && amount >= 500000);     // Exact half-millions
    }

    // Format currency for display
    formatCurrency(amount, currency = 'MMK') {
        const supportedCurrencies = ['USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

        if (currency === 'MMK') {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount) + ' MMK';
        } else if (supportedCurrencies.includes(currency)) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency,
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        } else {
            // Fallback for unsupported currencies
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount) + ` ${currency}`;
        }
    }

    // Get configuration for UI display
    getConfiguration() {
        return { ...this.config };
    }

    // Reset alert counter (for testing)
    resetAlertCounter() {
        this.alertIdCounter = 1;
    }
}

// Export for use in other modules
window.TTRRules = TTRRules;
