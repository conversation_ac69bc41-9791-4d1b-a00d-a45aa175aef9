# Test CSV File Documentation

## Overview
The `test_currency_transactions.csv` file contains comprehensive test data to validate the new currency fields implementation in the Transaction Breakdown section.

## Test Data Structure

### File Details
- **Total Records**: 56 transactions
- **Date Range**: January 15-19, 2024
- **Transaction Types**: HOC, IBD, WU
- **Currencies Tested**: CNY, EUR, INR, JPY, SGD, THB, MMK, USD
- **Transaction Roles**: Credit (C) and Debit (D)

### Data Distribution

#### HOC Transactions (20 records)
**Credit Transactions:**
- CNY: 5,000.00 + 7,500.00 = **12,500.00 CNY**
- EUR: 2,500.50 = **2,500.50 EUR**
- INR: 75,000.00 = **75,000.00 INR**
- JPY: 350,000.00 = **350,000.00 JPY**
- SGD: 4,200.00 + 5,500.00 + 3,200.00 = **12,900.00 SGD**
- THB: 95,000.00 = **95,000.00 THB**
- MMK: 1,500,000.00 = **1,500,000.00 MMK**
- USD: 3,500.00 = **3,500.00 USD**

**Debit Transactions:**
- CNY: 3,000.00 + 4,200.00 = **7,200.00 CNY**
- EUR: 1,800.75 = **1,800.75 EUR**
- INR: 45,000.00 = **45,000.00 INR**
- JPY: 280,000.00 = **280,000.00 JPY**
- SGD: 3,100.00 = **3,100.00 SGD**
- THB: 72,000.00 = **72,000.00 THB**
- MMK: 1,200,000.00 = **1,200,000.00 MMK**
- USD: 2,800.00 = **2,800.00 USD**

#### IBD Transactions (20 records)
**Credit Transactions:**
- CNY: 8,000.00 = **8,000.00 CNY**
- EUR: 4,200.25 + 3,200.00 = **7,400.25 EUR**
- INR: 125,000.00 = **125,000.00 INR**
- JPY: 580,000.00 = **580,000.00 JPY**
- SGD: 6,800.00 = **6,800.00 SGD**
- THB: 155,000.00 = **155,000.00 THB**
- MMK: 2,500,000.00 = **2,500,000.00 MMK**
- USD: 5,800.00 = **5,800.00 USD**

**Debit Transactions:**
- CNY: 6,500.00 = **6,500.00 CNY**
- EUR: 3,800.50 + 2,800.00 = **6,600.50 EUR**
- INR: 98,000.00 = **98,000.00 INR**
- JPY: 450,000.00 = **450,000.00 JPY**
- SGD: 5,200.00 = **5,200.00 SGD**
- THB: 128,000.00 + 95,000.00 + 68,000.00 = **291,000.00 THB**
- MMK: 1,950,000.00 = **1,950,000.00 MMK**
- USD: 4,600.00 = **4,600.00 USD**

#### WU Transactions (16 records)
**Credit Transactions:**
- CNY: 12,000.00 = **12,000.00 CNY**
- EUR: 6,500.75 = **6,500.75 EUR**
- INR: 185,000.00 + 95,000.00 + 125,000.00 = **405,000.00 INR**
- JPY: 850,000.00 + 420,000.00 = **1,270,000.00 JPY**
- SGD: 9,800.00 = **9,800.00 SGD**
- THB: 225,000.00 = **225,000.00 THB**
- MMK: 3,500,000.00 = **3,500,000.00 MMK**
- USD: 8,500.00 = **8,500.00 USD**

**Debit Transactions:**
- CNY: 9,500.00 = **9,500.00 CNY**
- EUR: 5,200.25 = **5,200.25 EUR**
- INR: 142,000.00 = **142,000.00 INR**
- JPY: 680,000.00 + 350,000.00 = **1,030,000.00 JPY**
- SGD: 7,600.00 = **7,600.00 SGD**
- THB: 185,000.00 = **185,000.00 THB**
- MMK: 2,800,000.00 = **2,800,000.00 MMK**
- USD: 6,800.00 = **6,800.00 USD**

## Expected Results After Upload

When you upload this CSV file to the dashboard, you should see the following amounts in the Transaction Breakdown section:

### HOC Section
**Credit:**
- Amount CNY: 12,500.00 CNY
- Amount EUR: 2,500.50 EUR
- Amount INR: 75,000.00 INR
- Amount JPY: 350,000.00 JPY
- Amount SGD: 12,900.00 SGD
- Amount THB: 95,000.00 THB

**Debit:**
- Amount CNY: 7,200.00 CNY
- Amount EUR: 1,800.75 EUR
- Amount INR: 45,000.00 INR
- Amount JPY: 280,000.00 JPY
- Amount SGD: 3,100.00 SGD
- Amount THB: 72,000.00 THB

### IBD Section
**Credit:**
- Amount CNY: 8,000.00 CNY
- Amount EUR: 7,400.25 EUR
- Amount INR: 125,000.00 INR
- Amount JPY: 580,000.00 JPY
- Amount SGD: 6,800.00 SGD
- Amount THB: 155,000.00 THB

**Debit:**
- Amount CNY: 6,500.00 CNY
- Amount EUR: 6,600.50 EUR
- Amount INR: 98,000.00 INR
- Amount JPY: 450,000.00 JPY
- Amount SGD: 5,200.00 SGD
- Amount THB: 291,000.00 THB

### WU Section
**Credit:**
- Amount CNY: 12,000.00 CNY
- Amount EUR: 6,500.75 EUR
- Amount INR: 405,000.00 INR
- Amount JPY: 1,270,000.00 JPY
- Amount SGD: 9,800.00 SGD
- Amount THB: 225,000.00 THB

**Debit:**
- Amount CNY: 9,500.00 CNY
- Amount EUR: 5,200.25 EUR
- Amount INR: 142,000.00 INR
- Amount JPY: 1,030,000.00 JPY
- Amount SGD: 7,600.00 SGD
- Amount THB: 185,000.00 THB

## Testing Instructions

1. **Upload the CSV file** to the Financial Transaction Dashboard
2. **Wait for processing** to complete
3. **Navigate to the Transaction Breakdown section**
4. **Verify the amounts** match the expected results above
5. **Check that existing MMK and USD fields** still display correctly
6. **Confirm that the Currency Breakdown section** continues to work as before

## Test Scenarios Covered

✅ **Multiple currencies per transaction type**
✅ **Both Credit and Debit transactions**
✅ **Multiple transactions of the same currency** (aggregation testing)
✅ **Different dates** (date handling testing)
✅ **All supported currencies** (CNY, EUR, INR, JPY, SGD, THB)
✅ **Existing currencies** (MMK, USD) for backward compatibility
✅ **Unique serial numbers** for proper transaction tracking
✅ **Realistic transaction amounts** for each currency

## Validation Points

- All new currency fields should display non-zero amounts
- Credit/Debit logic should work correctly (C = Credit, D = Debit)
- Amounts should be properly formatted with currency symbols
- Existing functionality should remain unchanged
- Performance should be acceptable for the dataset size
- No JavaScript errors should occur during processing
