/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #95a5a6;
    --secondary-dark: #7f8c8d;
    --success-color: #2ecc71;
    --success-dark: #27ae60;
    --danger-color: #e74c3c;
    --danger-dark: #c0392b;
    --warning-color: #f39c12;
    --text-color: #2c3e50;
    --text-light: #7f8c8d;
    --bg-color: #f5f7fa;
    --card-bg: #ffffff;
    --border-color: #ecf0f1;
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 10px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 5px 15px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--success-color));
}

/* Navigation Styles */
.main-navigation {
    margin-top: 20px;
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
}

.nav-menu {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 0;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    text-decoration: none;
    color: var(--text-light);
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
    border: 2px solid transparent;
    background-color: transparent;
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.05);
    border-color: rgba(52, 152, 219, 0.1);
}

.nav-link.active {
    color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.1);
    border-color: var(--primary-color);
    font-weight: 600;
}

.nav-icon {
    font-size: 1.2rem;
}

.nav-text {
    font-size: 0.95rem;
}

/* Page Content Styles */
.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

h1 {
    color: var(--text-color);
    font-weight: 600;
}

section {
    margin-bottom: 30px;
    transition: var(--transition);
}

/* Upload Section Styles */
.upload-section {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 30px;
}

/* Main container for upload area and file list */
.upload-main-container {
    display: flex;
    gap: 25px;
    align-items: stretch;
    min-height: 400px;
}

/* Responsive design for mobile devices */
@media (max-width: 768px) {
    .upload-main-container {
        flex-direction: column;
        min-height: auto;
    }

    .container {
        padding: 10px;
    }

    .nav-menu {
        flex-direction: column;
        gap: 10px;
    }

    .nav-link {
        padding: 10px 16px;
        text-align: center;
    }

    /* Table responsiveness */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .data-table {
        min-width: 800px;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .breakdown-grid {
        grid-template-columns: 1fr;
    }

    /* High-value analytics responsive */
    .high-value-metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .high-value-metrics-grid {
        grid-template-columns: 1fr;
    }

    .nav-text {
        display: none;
    }

    .nav-icon {
        font-size: 1.5rem;
    }
}

.upload-container {
    flex: 1;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 3px dashed #d1ecf1;
    border-radius: 12px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.08);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-container:hover::before,
.upload-container.drag-over::before {
    opacity: 1;
}

.upload-container:hover,
.upload-container.drag-over {
    border-color: #3498db;
    background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
}

.upload-icon {
    margin-bottom: 20px;
    color: #3498db;
    transition: all 0.3s ease;
    transform: scale(1);
}

.upload-container:hover .upload-icon {
    color: #2980b9;
    transform: scale(1.1);
}

.upload-container h3 {
    color: var(--text-color);
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 12px;
    transition: color 0.3s ease;
}

.upload-container:hover h3 {
    color: #2980b9;
}

.upload-container p {
    color: #7f8c8d;
    font-size: 1rem;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.upload-container:hover p {
    color: #5a6c7d;
}

.file-info {
    color: #95a5a6;
    font-size: 0.85rem;
    margin-top: 15px;
    font-style: italic;
    background-color: rgba(149, 165, 166, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-block;
}

/* Enhanced progress bar container with improved visibility */
.upload-progress {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    display: none; /* Hidden by default */
    border: 1px solid rgba(52, 152, 219, 0.1);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
    margin: 20px 0;
    z-index: 100; /* Ensure it appears above other elements */
}

/* Primary visibility class - most important */
.upload-progress.visible {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
}

/* Secondary visibility class for compatibility */
.upload-progress.show {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
}

/* Force show class for debugging and manual control */
.upload-progress.force-show {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
    position: relative !important;
}

.upload-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
}

/* Enhanced progress container with better visibility */
.progress-container {
    background-color: #ecf0f1;
    border-radius: 10px;
    height: 28px; /* Slightly taller for better visibility */
    overflow: hidden;
    margin-bottom: 15px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    border: 1px solid rgba(52, 152, 219, 0.2);
    display: block; /* Ensure container is always visible */
}

/* Enhanced progress bar with improved animations */
.progress-bar {
    background: linear-gradient(90deg, #3498db, #2ecc71);
    height: 100%;
    width: 0%;
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    min-width: 0;
    max-width: 100%;
    display: block !important; /* Force display */
    will-change: width; /* Optimize for width animations */
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        transparent 25%,
        rgba(255, 255, 255, 0.2) 25%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 50%,
        transparent 75%,
        rgba(255, 255, 255, 0.2) 75%);
    background-size: 20px 20px;
    animation: progressStripes 1s linear infinite;
}

@keyframes progressStripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

/* Enhanced progress text with better visibility and animations */
#progressText {
    font-weight: 600;
    color: var(--text-color);
    text-align: center;
    font-size: 1rem;
    margin: 0;
    padding: 8px 0; /* Slightly more padding */
    line-height: 1.4;
    min-height: 1.4em;
    display: block !important; /* Force display */
    word-wrap: break-word;
    overflow-wrap: break-word;
    transition: opacity 0.2s ease; /* Smooth text transitions */
    position: relative;
    z-index: 1;
}

/* Progress text loading animation */
#progressText.loading {
    animation: progressTextPulse 1.5s ease-in-out infinite;
}

@keyframes progressTextPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.file-list {
    flex: 1;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(52, 152, 219, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.file-list::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #2ecc71, #3498db, #9b59b6);
}

.file-list h3 {
    margin-bottom: 20px;
    color: var(--text-color);
    font-size: 1.3rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 12px;
}

.file-list h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #2ecc71, #3498db);
    border-radius: 2px;
}

.file-list ul {
    list-style: none;
    margin: 0;
    flex: 1;
    overflow-y: auto;
    max-height: 300px;
    padding-right: 5px;
}

.file-list ul::-webkit-scrollbar {
    width: 6px;
}

.file-list ul::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.file-list ul::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #3498db, #2ecc71);
    border-radius: 3px;
}

.file-list ul::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #2980b9, #27ae60);
}

.file-list li {
    padding: 15px 18px;
    border-radius: 10px;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid transparent;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.file-list li::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(46, 204, 113, 0.1));
    transition: width 0.3s ease;
    z-index: 0;
}

.file-list li:hover::before {
    width: 100%;
}

.file-list li:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-left-color: #3498db;
}

/* Processed file styles */
.file-list li.processed-file {
    background: linear-gradient(135deg, #f1f9f5 0%, #e8f5e8 100%);
    border-left: 4px solid #2ecc71;
    box-shadow: 0 2px 8px rgba(46, 204, 113, 0.15);
}

.file-list li.processed-file::before {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.15), rgba(39, 174, 96, 0.15));
}

.file-list li .file-name {
    flex: 1;
    font-weight: 500;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    color: var(--text-color);
    font-size: 0.95rem;
}

.file-list li .file-name::before {
    content: '';
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 12px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'/%3E%3Cpolyline points='14 2 14 8 20 8'/%3E%3Cline x1='16' y1='13' x2='8' y2='13'/%3E%3Cline x1='16' y1='17' x2='8' y2='17'/%3E%3Cpolyline points='10 9 9 9 8 9'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    transition: transform 0.2s ease;
}

.file-list li:hover .file-name::before {
    transform: scale(1.1);
}

.file-list li.processed-file .file-name::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232ecc71' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'/%3E%3Cpolyline points='14 2 14 8 20 8'/%3E%3Cpolyline points='9 15 12 18 15 15'/%3E%3Cpath d='M12 18v-8'/%3E%3C/svg%3E");
}

.file-list li .file-status {
    font-size: 0.8rem;
    padding: 6px 14px;
    border-radius: 25px;
    margin-right: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    position: relative;
    z-index: 1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.file-list li .file-status.processed {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
    box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
}

.file-list li .file-status.processed:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.4);
}

.file-list li .file-status.processed::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 6px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
}

.file-list li.removed-file {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.08) 0%, rgba(192, 57, 43, 0.05) 100%);
    border-left: 4px solid var(--danger-color);
    opacity: 0.85;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.15);
}

.file-list li.removed-file::before {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.15), rgba(192, 57, 43, 0.15));
}

.file-list li.removed-file .file-name {
    text-decoration: line-through;
    color: var(--text-light);
    opacity: 0.8;
}

.file-list li.removed-file .file-name::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23e74c3c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'/%3E%3Cline x1='9' y1='15' x2='15' y2='9'/%3E%3Cline x1='9' y1='9' x2='15' y2='15'/%3E%3C/svg%3E");
    opacity: 0.7;
}

.file-list li .file-status.removed {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.file-list li .file-status.removed:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

.file-list li .file-status.removed::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 6px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cline x1='15' y1='9' x2='9' y2='15'/%3E%3Cline x1='9' y1='9' x2='15' y2='15'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
}

.submit-container {
    margin-top: 25px;
    text-align: center;
    display: none; /* Hidden by default, shown when files are selected */
    padding-top: 20px;
    border-top: 2px solid rgba(52, 152, 219, 0.1);
}

.submit-btn {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    padding: 14px 35px;
    font-size: 1.05rem;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:hover {
    background: linear-gradient(135deg, #219653, #27ae60);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.submit-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

/* Disabled button styles */
.submit-btn.disabled-btn {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(149, 165, 166, 0.3);
    opacity: 0.7;
}

.submit-btn.disabled-btn::before {
    display: none;
}

.submit-btn.disabled-btn:hover {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    transform: none;
    box-shadow: 0 2px 8px rgba(149, 165, 166, 0.3);
}

/* Submit button message styles */
.submit-button-message {
    margin-top: 10px;
    color: #e74c3c;
    font-size: 0.9rem;
    background-color: rgba(231, 76, 60, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    text-align: center;
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}



.btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    font-size: 0.95rem;
    box-shadow: var(--shadow-sm);
    min-width: 120px;
    text-align: center;
}

.btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
}

/* Remove file button styles */
.remove-file {
    background: linear-gradient(135deg, transparent, rgba(149, 165, 166, 0.05));
    border: 1px solid rgba(149, 165, 166, 0.2);
    color: var(--secondary-color);
    cursor: pointer;
    font-size: 0.85rem;
    padding: 8px 14px;
    border-radius: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    font-weight: 500;
    position: relative;
    z-index: 1;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.remove-file::before {
    content: '';
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-right: 6px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2395a5a6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'/%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    transition: all 0.3s ease;
}

.remove-file:hover {
    color: white;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-color: #e74c3c;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.remove-file:hover::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'/%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'/%3E%3C/svg%3E");
    transform: scale(1.1);
}

/* Delete button (for already removed files) */
.file-list li.removed-file .remove-file {
    color: var(--danger-color);
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.05));
    border-color: rgba(231, 76, 60, 0.3);
}

.file-list li.removed-file .remove-file::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23e74c3c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'/%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'/%3E%3Cline x1='10' y1='11' x2='10' y2='17'/%3E%3Cline x1='14' y1='11' x2='14' y2='17'/%3E%3C/svg%3E");
}

.file-list li.removed-file .remove-file:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    color: white;
    border-color: #c0392b;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(192, 57, 43, 0.4);
}

.file-list li.removed-file .remove-file:hover::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'/%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'/%3E%3Cline x1='10' y1='11' x2='10' y2='17'/%3E%3Cline x1='14' y1='11' x2='14' y2='17'/%3E%3C/svg%3E");
    transform: scale(1.1);
}

/* Confirmation dialog styles */
.confirmation-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.confirmation-dialog.show {
    opacity: 1;
    visibility: visible;
}

.confirmation-content {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 25px;
    width: 90%;
    max-width: 400px;
    box-shadow: var(--shadow-lg);
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    border-top: 4px solid var(--danger-color);
}

.confirmation-dialog.show .confirmation-content {
    transform: translateY(0);
}

.confirmation-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
}

.confirmation-message {
    margin-bottom: 20px;
    color: var(--text-color);
    line-height: 1.5;
}

.confirmation-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.confirm-btn {
    background-color: var(--danger-color);
}

.confirm-btn:hover {
    background-color: var(--danger-dark);
}

.cancel-btn {
    background-color: var(--secondary-color);
}

.cancel-btn:hover {
    background-color: var(--secondary-dark);
}

/* Metrics Section Styles - Integrated Design */
.metrics-section {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 12px;
    padding: 32px 28px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.02);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.02);
    margin-bottom: 32px;
    backdrop-filter: blur(10px);
}

.metrics-section:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.03);
    transform: translateY(-2px);
}

.metrics-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #667eea 50%, var(--primary-dark) 100%);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* Transaction Breakdown Section - Complete Modern Redesign */
.breakdown-section {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(248, 250, 252, 0.95) 50%,
        rgba(255, 255, 255, 0.98) 100%);
    border-radius: 18px;
    padding: 40px 36px;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.08),
        0 6px 20px rgba(0, 0, 0, 0.04),
        0 2px 8px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.4);
    margin-bottom: 40px;
    backdrop-filter: blur(25px) saturate(200%);
}

.breakdown-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 85%, rgba(46, 204, 113, 0.04) 0%, transparent 50%),
        radial-gradient(circle at 85% 15%, rgba(52, 152, 219, 0.04) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(155, 89, 182, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

.breakdown-section:hover {
    box-shadow:
        0 16px 56px rgba(0, 0, 0, 0.12),
        0 8px 28px rgba(0, 0, 0, 0.06),
        0 4px 12px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 1),
        0 0 0 1px rgba(46, 204, 113, 0.1);
    transform: translateY(-6px) scale(1.01);
}

.breakdown-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(135deg,
        var(--success-color) 0%,
        #48c78e 25%,
        #00d4aa 50%,
        #17a2b8 75%,
        var(--success-color) 100%);
    box-shadow:
        0 4px 16px rgba(46, 204, 113, 0.4),
        0 2px 8px rgba(46, 204, 113, 0.2);
    z-index: 2;
}

.metrics-section h3 {
    font-size: 1.4rem;
    margin-bottom: 28px;
    color: var(--text-color);
    position: relative;
    display: inline-flex;
    align-items: center;
    font-weight: 700;
    letter-spacing: -0.03em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.breakdown-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 36px;
    position: relative;
    z-index: 3;
}

.breakdown-section h3 {
    font-size: 1.6rem;
    margin-bottom: 0;
    color: var(--text-color);
    position: relative;
    display: inline-flex;
    align-items: center;
    font-weight: 800;
    letter-spacing: -0.05em;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.breakdown-section h3::before {
    content: '📈';
    margin-right: 16px;
    font-size: 1.4rem;
    opacity: 0.9;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.1));
    animation: breakdownPulse 4s ease-in-out infinite;
}

@keyframes breakdownPulse {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.9; }
    50% { transform: scale(1.08) rotate(2deg); opacity: 1; }
}

.metrics-section h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: 2px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.breakdown-section h3::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 0;
    width: 60px;
    height: 5px;
    background: linear-gradient(90deg,
        var(--success-color) 0%,
        #48c78e 25%,
        #00d4aa 75%,
        var(--success-color) 100%);
    border-radius: 4px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 3px 12px rgba(46, 204, 113, 0.3),
        0 1px 4px rgba(0, 0, 0, 0.1);
}

.metrics-section:hover h3::after {
    width: 80px;
    background: linear-gradient(90deg, var(--success-color), var(--primary-color));
}

.breakdown-section:hover h3::after {
    width: 120px;
    background: linear-gradient(90deg,
        #00d4aa 0%,
        var(--success-color) 25%,
        #48c78e 75%,
        #00d4aa 100%);
    box-shadow:
        0 4px 16px rgba(46, 204, 113, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.15);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
    margin-top: 24px;
}

/* Modern Table-Based Breakdown Grid */
.breakdown-grid {
    margin-top: 32px;
    position: relative;
    z-index: 2;
    display: table;
    width: 100%;
    border-spacing: 0;
    border-collapse: separate;
}

/* Transaction Breakdown Table Container */
.breakdown-table-container {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(248, 250, 252, 0.95) 100%);
    border-radius: 16px;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(15px);
    transition: all 0.4s ease;
}

.breakdown-table-container:hover {
    box-shadow:
        0 12px 48px rgba(0, 0, 0, 0.12),
        0 6px 24px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
}

/* Transaction Breakdown Table */
.breakdown-table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
    font-family: 'Segoe UI', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.breakdown-table thead {
    background: linear-gradient(135deg,
        var(--success-color) 0%,
        #48c78e 25%,
        #00d4aa 75%,
        #17a2b8 100%);
    color: white;
    position: relative;
}

.breakdown-table thead::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.9) 50%,
        rgba(255, 255, 255, 0.3) 100%);
}

.breakdown-table th {
    padding: 20px 24px;
    text-align: left;
    font-weight: 700;
    font-size: 0.95rem;
    letter-spacing: 0.6px;
    text-transform: uppercase;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    position: relative;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.breakdown-table th::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.15) 0%,
        transparent 100%);
    pointer-events: none;
}

.breakdown-table th:first-child {
    border-top-left-radius: 16px;
}

.breakdown-table th:last-child {
    border-top-right-radius: 16px;
}

.metric-card {
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
    border-radius: 12px;
    padding: 24px 20px;
    text-align: left;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: visible;
    min-height: auto;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Transaction Breakdown Cards - Now Table Rows */
.breakdown-card {
    display: table-row;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.breakdown-card:hover {
    background: linear-gradient(135deg,
        rgba(46, 204, 113, 0.04) 0%,
        rgba(52, 152, 219, 0.03) 100%);
}

.metric-card h4 {
    color: var(--text-color);
    margin-bottom: 16px;
    font-size: 1.1rem;
    font-weight: 600;
    position: relative;
    display: inline-block;
    line-height: 1.4;
    word-wrap: break-word;
    hyphens: auto;
}

/* Transaction Type Header Cell */
.breakdown-card h4 {
    display: table-cell;
    padding: 28px 32px;
    font-size: 1.3rem;
    font-weight: 800;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    vertical-align: middle;
    position: relative;
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    width: 200px;
    min-width: 200px;
}

.breakdown-card h4::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 32px;
    border-radius: 5px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
}

.metric-card p {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 12px 0 8px 0;
    transition: all 0.3s ease;
    line-height: 1.2;
    word-break: break-word;
    overflow-wrap: break-word;
}

.metric-card:hover p {
    transform: scale(1.03);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Enhanced metric card accessibility and readability */
.metric-card {
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);
}

.metric-card:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Ensure proper contrast for all metric text */
.metric-card h4 {
    color: #2c3e50;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.metric-card p {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Transaction Type Specific Colors */
.breakdown-card:nth-child(1) h4::before {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #5a67d8 100%);
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.4);
}

.breakdown-card:nth-child(2) h4::before {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #e53e3e 100%);
    box-shadow: 0 3px 12px rgba(240, 147, 251, 0.4);
}

.breakdown-card:nth-child(3) h4::before {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #38b2ac 100%);
    box-shadow: 0 3px 12px rgba(79, 172, 254, 0.4);
}

/* Transaction Details Cell */
.breakdown-details {
    display: table-cell;
    padding: 28px 32px;
    vertical-align: middle;
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    width: auto;
}

/* Modern Data Grid Layout for Transaction Details */
.breakdown-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 16px;
}

/* Modern Transaction Data Cards */
.breakdown-details p {
    margin: 8px 0;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 18px;
    border-radius: 10px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(248, 250, 252, 0.6) 100%);
    border: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.breakdown-details p::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    opacity: 0;
    transition: all 0.3s ease;
}

.breakdown-details p:hover {
    background: linear-gradient(135deg,
        rgba(52, 152, 219, 0.08) 0%,
        rgba(46, 204, 113, 0.06) 100%);
    transform: translateX(6px) scale(1.02);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.breakdown-details p:hover::before {
    opacity: 1;
    width: 6px;
}

.breakdown-details p span {
    font-weight: 700;
    transition: all 0.3s ease;
    color: var(--text-color);
    font-size: 1.05rem;
    font-family: 'Segoe UI', 'SF Mono', 'Monaco', 'Consolas', monospace;
    text-align: right;
    flex-shrink: 0;
    letter-spacing: 0.3px;
}

.breakdown-details > p {
    font-weight: 600;
    margin-bottom: 16px;
    padding: 12px 16px;
    border-bottom: 2px solid rgba(0, 0, 0, 0.06);
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.04), rgba(46, 204, 113, 0.04));
    border-radius: 8px;
    font-size: 1rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    overflow: visible;
    min-height: auto;
    flex-wrap: wrap;
}

/* Modern Credit-Debit Breakdown Grid */
.credit-debit-breakdown {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
    padding-top: 16px;
    width: 100%;
    border-top: 2px solid rgba(0, 0, 0, 0.04);
}

.credit-breakdown, .debit-breakdown {
    padding: 14px 12px;
    border-radius: 8px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: visible;
    backdrop-filter: blur(10px);
    min-height: auto;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.credit-breakdown {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.06), rgba(46, 204, 113, 0.02));
    border: 1px solid rgba(46, 204, 113, 0.15);
    border-left: 4px solid var(--success-color);
}

.credit-breakdown:hover {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(46, 204, 113, 0.04));
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.15);
    transform: translateY(-3px) scale(1.02);
}

.debit-breakdown {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.06), rgba(231, 76, 60, 0.02));
    border: 1px solid rgba(231, 76, 60, 0.15);
    border-left: 4px solid var(--danger-color);
}

.debit-breakdown:hover {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.04));
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.15);
    transform: translateY(-3px) scale(1.02);
}

.credit-breakdown h5, .debit-breakdown h5 {
    margin: 0 0 12px 0;
    font-size: 1rem;
    color: var(--text-color);
    position: relative;
    display: flex;
    align-items: center;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.3;
}

.credit-breakdown h5::before {
    content: '💰';
    margin-right: 6px;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.debit-breakdown h5::before {
    content: '💸';
    margin-right: 6px;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.credit-breakdown h5::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--success-color), #48c78e);
    border-radius: 1px;
    transition: width 0.3s ease;
}

.debit-breakdown h5::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--danger-color), #e74c3c);
    border-radius: 1px;
    transition: width 0.3s ease;
}

.credit-breakdown:hover h5::after, .debit-breakdown:hover h5::after {
    width: 60px;
}

.credit-breakdown p, .debit-breakdown p {
    margin: 6px 0;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 6px 8px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.3;
    min-height: auto;
    flex-wrap: wrap;
    gap: 2px;
}

.credit-breakdown p:hover, .debit-breakdown p:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateX(2px);
}

.credit-breakdown p span, .debit-breakdown p span {
    font-weight: 700;
    font-size: 0.8rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    text-align: right;
    flex-shrink: 0;
    max-width: 65%;
}

.credit-breakdown p span {
    color: var(--success-color);
}

.debit-breakdown p span {
    color: var(--danger-color);
}

/* Enhanced Number Display Styling */
.breakdown-details p span,
.credit-breakdown p span,
.debit-breakdown p span {
    font-family: 'Segoe UI', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Large Number Formatting */
.breakdown-details p span[data-large-number="true"],
.credit-breakdown p span[data-large-number="true"],
.debit-breakdown p span[data-large-number="true"] {
    position: relative;
    display: inline-block;
}

.breakdown-details p span[data-large-number="true"]::after,
.credit-breakdown p span[data-large-number="true"]::after,
.debit-breakdown p span[data-large-number="true"]::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, currentColor, transparent);
    opacity: 0.3;
}

/* Currency Symbol Styling */
.breakdown-details p span[data-currency="MMK"]::before {
    content: '₭ ';
    opacity: 0.7;
    font-size: 0.9em;
}

.breakdown-details p span[data-currency="USD"]::before {
    content: '$ ';
    opacity: 0.7;
    font-size: 0.9em;
}

/* Animated Counter Effect for Large Numbers */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Currency Breakdown Section Styles */
.currency-breakdown-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 251, 252, 0.98) 50%, rgba(255, 255, 255, 0.95) 100%);
    border-radius: 16px;
    padding: 36px 32px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.04),
        0 1px 4px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 36px;
    backdrop-filter: blur(20px) saturate(180%);
}

.currency-breakdown-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(243, 156, 18, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(52, 152, 219, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(155, 89, 182, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

.currency-breakdown-section:hover {
    box-shadow:
        0 12px 48px rgba(0, 0, 0, 0.12),
        0 8px 24px rgba(0, 0, 0, 0.06),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        0 0 0 1px rgba(243, 156, 18, 0.1);
    transform: translateY(-4px) scale(1.01);
}

.currency-breakdown-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(135deg,
        var(--warning-color) 0%,
        #f1c40f 25%,
        #e67e22 50%,
        #d35400 75%,
        var(--warning-color) 100%);
    box-shadow:
        0 3px 12px rgba(243, 156, 18, 0.4),
        0 1px 4px rgba(243, 156, 18, 0.2);
    z-index: 2;
}

.currency-breakdown-section h3 {
    font-size: 1.5rem;
    margin-bottom: 32px;
    color: var(--text-color);
    position: relative;
    display: inline-flex;
    align-items: center;
    font-weight: 800;
    letter-spacing: -0.04em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    z-index: 3;
}

.currency-breakdown-section h3::before {
    content: '💰';
    margin-right: 14px;
    font-size: 1.3rem;
    opacity: 0.9;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    animation: currencyPulse 3s ease-in-out infinite;
}

@keyframes currencyPulse {
    0%, 100% { transform: scale(1); opacity: 0.9; }
    50% { transform: scale(1.05); opacity: 1; }
}

.currency-breakdown-section h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 4px;
    background: linear-gradient(90deg,
        var(--warning-color) 0%,
        #f1c40f 25%,
        #e67e22 75%,
        var(--warning-color) 100%);
    border-radius: 3px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 2px 8px rgba(243, 156, 18, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.1);
}

.currency-breakdown-section:hover h3::after {
    width: 100px;
    background: linear-gradient(90deg,
        #e67e22 0%,
        var(--warning-color) 25%,
        #f1c40f 75%,
        #e67e22 100%);
    box-shadow:
        0 3px 12px rgba(243, 156, 18, 0.4),
        0 2px 6px rgba(0, 0, 0, 0.15);
}

.currency-breakdown-table-container {
    margin-top: 28px;
    position: relative;
    z-index: 2;
}

.currency-breakdown-table {
    width: 100%;
    border-collapse: collapse;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(250, 251, 252, 0.95) 100%);
    border-radius: 14px;
    overflow: hidden;
    box-shadow:
        0 6px 24px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(10px);
}

.currency-breakdown-table thead {
    background: linear-gradient(135deg,
        var(--warning-color) 0%,
        #f39c12 25%,
        #e67e22 75%,
        #d35400 100%);
    color: white;
    position: relative;
}

.currency-breakdown-table thead::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(255, 255, 255, 0.3) 100%);
}

.currency-breakdown-table th {
    padding: 18px 24px;
    text-align: left;
    font-weight: 700;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border-bottom: 2px solid rgba(255, 255, 255, 0.15);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
}

.currency-breakdown-table th::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 100%);
    pointer-events: none;
}

.currency-breakdown-table th:first-child {
    border-top-left-radius: 14px;
}

.currency-breakdown-table th:last-child {
    border-top-right-radius: 14px;
    text-align: right;
}

.currency-breakdown-table tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
}

.currency-breakdown-table tbody tr::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg,
        rgba(243, 156, 18, 0.1) 0%,
        rgba(230, 126, 34, 0.08) 100%);
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.currency-breakdown-table tbody tr:hover {
    background: linear-gradient(135deg,
        rgba(243, 156, 18, 0.06) 0%,
        rgba(230, 126, 34, 0.04) 100%);
    transform: translateX(4px) scale(1.005);
    box-shadow:
        0 4px 16px rgba(243, 156, 18, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05);
}

.currency-breakdown-table tbody tr:hover::before {
    width: 4px;
}

.currency-breakdown-table tbody tr:last-child {
    border-bottom: none;
}

.currency-breakdown-table tbody tr:last-child:hover {
    border-bottom-left-radius: 14px;
    border-bottom-right-radius: 14px;
}

.currency-breakdown-table td {
    padding: 18px 24px;
    font-size: 0.95rem;
    color: var(--text-color);
    vertical-align: middle;
    position: relative;
    z-index: 2;
    background: transparent;
}

.currency-type {
    font-weight: 800;
    font-size: 1.05rem;
    color: var(--text-color);
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.currency-type::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 24px;
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
    border-radius: 3px;
    opacity: 0.8;
    box-shadow: 0 2px 6px rgba(243, 156, 18, 0.3);
    transition: all 0.3s ease;
}

.currency-breakdown-table tbody tr:hover .currency-type::before {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 3px 8px rgba(243, 156, 18, 0.4);
}

.currency-amount,
.currency-credit-amount,
.currency-debit-amount {
    text-align: right;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--text-color);
    font-family: 'Segoe UI', 'SF Mono', 'Monaco', 'Consolas', monospace;
    position: relative;
    transition: all 0.3s ease;
}

.currency-amount::before,
.currency-credit-amount::before,
.currency-debit-amount::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: currentColor;
    opacity: 0;
    transform: scaleX(0);
    transition: all 0.3s ease;
}

.currency-breakdown-table tbody tr:hover .currency-amount::before,
.currency-breakdown-table tbody tr:hover .currency-credit-amount::before,
.currency-breakdown-table tbody tr:hover .currency-debit-amount::before {
    opacity: 0.3;
    transform: scaleX(1);
}

.currency-credit-amount {
    color: #27ae60;
    text-shadow: 0 1px 2px rgba(39, 174, 96, 0.1);
}

.currency-debit-amount {
    color: #e74c3c;
    text-shadow: 0 1px 2px rgba(231, 76, 60, 0.1);
}

.currency-breakdown-row {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.currency-breakdown-row.animate-fade-in {
    opacity: 1;
    transform: translateY(0);
}

.empty-table-message {
    text-align: center;
    color: var(--text-light);
    font-style: normal;
    padding: 60px 32px !important;
    background: linear-gradient(135deg,
        rgba(149, 165, 166, 0.03) 0%,
        rgba(189, 195, 199, 0.05) 50%,
        rgba(149, 165, 166, 0.03) 100%);
    position: relative;
    transition: all 0.3s ease;
}

.empty-table-message::before {
    content: '📊';
    display: block;
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.6;
    filter: grayscale(0.3);
    animation: emptyStateFloat 3s ease-in-out infinite;
}

@keyframes emptyStateFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

.empty-table-message::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(52, 152, 219, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(155, 89, 182, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

.empty-table-message td {
    border: none !important;
    font-size: 1.1rem;
    font-weight: 500;
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

.empty-table-message:hover {
    background: linear-gradient(135deg,
        rgba(149, 165, 166, 0.05) 0%,
        rgba(189, 195, 199, 0.08) 50%,
        rgba(149, 165, 166, 0.05) 100%);
    transform: scale(1.02);
}

/* Currency-specific styling with enhanced visual effects */
.currency-breakdown-row[data-currency="MMK"] .currency-type::before {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 50%, #d35400 100%);
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.4);
}

.currency-breakdown-row[data-currency="MMK"]:hover {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.08) 0%, rgba(230, 126, 34, 0.06) 100%);
}

.currency-breakdown-row[data-currency="USD"] .currency-type::before {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 50%, #1f4e79 100%);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
}

.currency-breakdown-row[data-currency="USD"]:hover {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.08) 0%, rgba(41, 128, 185, 0.06) 100%);
}

.currency-breakdown-row[data-currency="SGD"] .currency-type::before {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 50%, #a93226 100%);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
}

.currency-breakdown-row[data-currency="SGD"]:hover {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.08) 0%, rgba(192, 57, 43, 0.06) 100%);
}

.currency-breakdown-row[data-currency="EUR"] .currency-type::before {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 50%, #7d3c98 100%);
    box-shadow: 0 2px 8px rgba(155, 89, 182, 0.4);
}

.currency-breakdown-row[data-currency="EUR"]:hover {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.08) 0%, rgba(142, 68, 173, 0.06) 100%);
}

.currency-breakdown-row[data-currency="JPY"] .currency-type::before {
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 50%, #880e4f 100%);
    box-shadow: 0 2px 8px rgba(233, 30, 99, 0.4);
}

.currency-breakdown-row[data-currency="JPY"]:hover {
    background: linear-gradient(135deg, rgba(233, 30, 99, 0.08) 0%, rgba(173, 20, 87, 0.06) 100%);
}

.currency-breakdown-row[data-currency="CNY"] .currency-type::before {
    background: linear-gradient(135deg, #ff5722 0%, #d84315 50%, #bf360c 100%);
    box-shadow: 0 2px 8px rgba(255, 87, 34, 0.4);
}

.currency-breakdown-row[data-currency="CNY"]:hover {
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.08) 0%, rgba(216, 67, 21, 0.06) 100%);
}

.currency-breakdown-row[data-currency="THB"] .currency-type::before {
    background: linear-gradient(135deg, #607d8b 0%, #455a64 50%, #37474f 100%);
    box-shadow: 0 2px 8px rgba(96, 125, 139, 0.4);
}

.currency-breakdown-row[data-currency="THB"]:hover {
    background: linear-gradient(135deg, rgba(96, 125, 139, 0.08) 0%, rgba(69, 90, 100, 0.06) 100%);
}

.currency-breakdown-row[data-currency="INR"] .currency-type::before {
    background: linear-gradient(135deg, #795548 0%, #5d4037 50%, #4e342e 100%);
    box-shadow: 0 2px 8px rgba(121, 85, 72, 0.4);
}

.currency-breakdown-row[data-currency="INR"]:hover {
    background: linear-gradient(135deg, rgba(121, 85, 72, 0.08) 0%, rgba(93, 64, 55, 0.06) 100%);
}

/* Zero-value currency styling with enhanced effects */
.currency-breakdown-row.zero-value-currency {
    opacity: 0.5;
    background: linear-gradient(135deg,
        rgba(149, 165, 166, 0.02) 0%,
        rgba(189, 195, 199, 0.03) 100%);
    filter: grayscale(0.3);
    transition: all 0.4s ease;
}

.currency-breakdown-row.zero-value-currency:hover {
    opacity: 0.75;
    background: linear-gradient(135deg,
        rgba(149, 165, 166, 0.06) 0%,
        rgba(189, 195, 199, 0.08) 100%);
    filter: grayscale(0.1);
    transform: translateX(2px) scale(1.002);
}

.currency-breakdown-row.zero-value-currency .currency-type {
    color: var(--text-light);
    opacity: 0.8;
}

.currency-breakdown-row.zero-value-currency .currency-type::before {
    opacity: 0.2;
    background: linear-gradient(135deg, #bdc3c7, #95a5a6) !important;
    box-shadow: 0 1px 3px rgba(149, 165, 166, 0.2);
}

.currency-breakdown-row.zero-value-currency .currency-amount,
.currency-breakdown-row.zero-value-currency .currency-credit-amount,
.currency-breakdown-row.zero-value-currency .currency-debit-amount {
    color: var(--text-light);
    font-style: italic;
    opacity: 0.7;
    font-weight: 500;
}

.breakdown-details p span.animate-count,
.credit-breakdown p span.animate-count,
.debit-breakdown p span.animate-count {
    animation: countUp 0.6s ease-out;
}

/* Additional overflow prevention and number formatting */
.breakdown-card,
.credit-breakdown,
.debit-breakdown {
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
}

.breakdown-details p,
.credit-breakdown p,
.debit-breakdown p {
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* For very long numbers, allow wrapping on mobile */
@media (max-width: 768px) {
    .breakdown-details p,
    .credit-breakdown p,
    .debit-breakdown p {
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
    }

    .breakdown-details p span,
    .credit-breakdown p span,
    .debit-breakdown p span {
        word-break: break-all;
        overflow-wrap: break-word;
    }
}

/* Ensure proper spacing for large numbers */
.breakdown-details p span[data-large-number="true"],
.credit-breakdown p span[data-large-number="true"],
.debit-breakdown p span[data-large-number="true"] {
    font-size: 0.85em;
    line-height: 1.2;
}

/* Container width management */
.breakdown-grid {
    width: 100%;
    max-width: 100%;
    overflow: visible;
}

.breakdown-card {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Long number handling */
.long-number {
    font-size: 0.9em !important;
    line-height: 1.2 !important;
}

/* Abbreviated number styling */
span[data-abbreviated="true"] {
    cursor: help;
    border-bottom: 1px dotted currentColor;
}

span[data-abbreviated="true"]:hover {
    opacity: 0.8;
}



.table-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #f0f4f8;
    padding-bottom: 10px;
}

.table-container h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    margin: 0;
}

/* Export Button Styles */
.export-btn {
    background-color: #27ae60;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 0.9rem;
}

.export-btn:hover {
    background-color: #219653;
}

.export-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'/%3E%3Cpolyline points='7 10 12 15 17 10'/%3E%3Cline x1='12' y1='15' x2='12' y2='3'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
}

.table-responsive {
    overflow-x: auto;
    margin-bottom: 15px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
    color: #333;
}

.data-table thead {
    background-color: #f8f9fa;
}

.data-table th {
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #e9ecef;
}

.data-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
}

.data-table tbody tr:hover {
    background-color: #f5f9ff;
}

.data-table .empty-table-message td {
    text-align: center;
    padding: 30px;
    color: #7f8c8d;
    font-style: italic;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 10px 0;
    border-top: 1px solid #e9ecef;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.pagination-btn {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #e9ecef;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#pageInfo {
    font-size: 0.9rem;
    color: #6c757d;
}

.rows-per-page {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rows-per-page label {
    font-size: 0.9rem;
    color: #6c757d;
}

.rows-per-page select {
    padding: 5px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #fff;
}



/* High-Value Analytics Section Styles */
.high-value-analytics-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 22px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    margin-bottom: 25px;
}

.high-value-analytics-section:hover {
    box-shadow: var(--shadow-md), 0 5px 15px rgba(0, 0, 0, 0.03);
}

.high-value-analytics-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--warning-color), #e67e22);
}

.high-value-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.high-value-header-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 20px;
}

.high-value-analytics-section h3 {
    font-size: 1.2rem;
    margin: 0;
    color: var(--text-color);
    position: relative;
    display: inline-block;
}

.high-value-analytics-section h3::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--warning-color);
    transition: width 0.2s ease;
}

.high-value-analytics-section:hover h3::after {
    width: 60px;
}

.high-value-export-btn {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.2);
}

.high-value-export-btn:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
    transform: translateY(-1px);
}

.high-value-export-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.2);
}

.high-value-export-btn .export-icon {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'/%3E%3Cpolyline points='7 10 12 15 17 10'/%3E%3Cline x1='12' y1='15' x2='12' y2='3'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

/* Transaction Breakdown Export Button */
.transaction-breakdown-export-btn {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(46, 204, 113, 0.2);
}

.transaction-breakdown-export-btn:hover {
    background: linear-gradient(135deg, #27ae60 0%, #219653 100%);
    box-shadow: 0 4px 8px rgba(46, 204, 113, 0.3);
    transform: translateY(-1px);
}

.transaction-breakdown-export-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(46, 204, 113, 0.2);
}

.transaction-breakdown-export-btn .export-icon {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'/%3E%3Cpolyline points='7 10 12 15 17 10'/%3E%3Cline x1='12' y1='15' x2='12' y2='3'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

/* Loading spinner for export buttons */
.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
    display: inline-block;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* TTR Summary Export Button */
.ttr-summary-export-btn {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
}

.ttr-summary-export-btn:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
    transform: translateY(-1px);
}

.ttr-summary-export-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
}

.ttr-summary-export-btn .export-icon {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'/%3E%3Cpolyline points='7 10 12 15 17 10'/%3E%3Cline x1='12' y1='15' x2='12' y2='3'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.high-value-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.high-value-card {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(243, 156, 18, 0.1);
    position: relative;
    overflow: hidden;
}

.high-value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--warning-color), #e67e22);
}

.high-value-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.15);
    border-color: rgba(243, 156, 18, 0.2);
}

.high-value-card h4 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    padding-left: 15px;
}

.high-value-card p {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--warning-color);
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

.high-value-card:hover p {
    transform: scale(1.05);
    color: #e67e22;
}

/* High-Value USD Card */
.high-value-card-usd {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.high-value-card-usd::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #3b82f6, #2563eb);
}

.high-value-card-usd:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.2);
}

.high-value-card-usd h4 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    padding-left: 15px;
}

.high-value-card-usd p {
    font-size: 2.2rem;
    font-weight: 700;
    color: #3b82f6;
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

.high-value-card-usd:hover p {
    transform: scale(1.05);
    color: #2563eb;
}

/* Customer Analytics Section Styles */
.customer-analytics-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 22px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    margin-bottom: 25px;
}

.customer-analytics-section:hover {
    box-shadow: var(--shadow-md), 0 5px 15px rgba(0, 0, 0, 0.03);
}

.customer-analytics-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #9b59b6, #8e44ad);
}

.customer-analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.customer-analytics-header-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 20px;
}

.customer-analytics-section h3 {
    font-size: 1.2rem;
    margin: 0;
    color: var(--text-color);
    position: relative;
    display: inline-block;
}

.customer-analytics-section h3::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: #9b59b6;
    transition: width 0.2s ease;
}

.customer-analytics-section:hover h3::after {
    width: 60px;
}

.customer-analytics-export-btn {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-analytics-export-btn:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
}

/* Customer Alert Banner */
.customer-alert-banner {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    color: white;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.alert-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.alert-text {
    flex: 1;
}

.alert-text strong {
    display: block;
    margin-bottom: 4px;
    font-size: 1.1rem;
}

.alert-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.alert-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Customer Analytics Metrics Grid */
.customer-analytics-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

/* Customer Analytics Cards */
.customer-alert-card {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(231, 76, 60, 0.1);
    position: relative;
    overflow: hidden;
}

.customer-alert-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--danger-color), #c0392b);
}

.customer-alert-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.15);
    border-color: rgba(231, 76, 60, 0.2);
}

.customer-alert-card p {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--danger-color);
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

.customer-mmk-card {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(243, 156, 18, 0.1);
    position: relative;
    overflow: hidden;
}

.customer-mmk-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--warning-color), #e67e22);
}

.customer-mmk-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.15);
    border-color: rgba(243, 156, 18, 0.2);
}

.customer-mmk-card p {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--warning-color);
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

.customer-usd-card {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.customer-usd-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #3b82f6, #2563eb);
}

.customer-usd-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.2);
}

.customer-usd-card p {
    font-size: 2.2rem;
    font-weight: 700;
    color: #3b82f6;
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

.customer-total-card {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(46, 204, 113, 0.1);
    position: relative;
    overflow: hidden;
}

.customer-total-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--success-color), #27ae60);
}

.customer-total-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.15);
    border-color: rgba(46, 204, 113, 0.2);
}

.customer-total-card p {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--success-color);
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

/* Enhanced Customer Analytics Table Styles */
.customer-analytics-details {
    margin-top: 25px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid rgba(155, 89, 182, 0.1);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.customer-analytics-details::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #9b59b6, #8e44ad, #9b59b6);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

.customer-analytics-details:hover {
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(155, 89, 182, 0.15);
    transform: translateY(-2px);
}

.customer-details-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.customer-details-header h4 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
}

/* Enhanced Customer Details Controls with Purple Gradient Theme */
.customer-details-controls {
    display: flex;
    align-items: flex-end;
    gap: 20px;
    flex-wrap: wrap;
    padding: 20px;
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.08) 0%, rgba(142, 68, 173, 0.05) 50%, rgba(125, 60, 152, 0.03) 100%);
    border-radius: 16px;
    border: 1px solid rgba(155, 89, 182, 0.12);
    box-shadow: 0 4px 20px rgba(155, 89, 182, 0.08);
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.customer-details-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #9b59b6 0%, #8e44ad 50%, #7d3c98 100%);
}

/* Modern Filter Container with Enhanced Styling */
.filters-container {
    display: flex;
    align-items: flex-end;
    gap: 18px;
    flex-wrap: wrap;
    flex: 1;
}

/* Enhanced Filter Group Design */
.filter-group.modern {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 170px;
    position: relative;
    background: rgba(255, 255, 255, 0.7);
    padding: 16px;
    border-radius: 12px;
    border: 1px solid rgba(155, 89, 182, 0.15);
    box-shadow: 0 2px 8px rgba(155, 89, 182, 0.06);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.filter-group.modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.12);
    border-color: rgba(155, 89, 182, 0.25);
}

/* Enhanced Filter Labels */
.filter-group.modern label {
    font-size: 0.75rem;
    font-weight: 700;
    color: #6c5ce7;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 2px;
}

.filter-icon {
    font-size: 1rem;
    opacity: 0.9;
    filter: drop-shadow(0 1px 2px rgba(155, 89, 182, 0.3));
}

/* Premium Modern Select Styling */
.modern-select {
    padding: 14px 18px;
    border: 2px solid rgba(155, 89, 182, 0.2);
    border-radius: 10px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    font-size: 0.9rem;
    font-weight: 500;
    color: #2d3748;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239b59b6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 14px center;
    background-repeat: no-repeat;
    background-size: 18px;
    padding-right: 45px;
    box-shadow: 0 2px 4px rgba(155, 89, 182, 0.05);
}

.modern-select:hover {
    border-color: #9b59b6;
    box-shadow: 0 6px 16px rgba(155, 89, 182, 0.18);
    transform: translateY(-1px);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.modern-select:focus {
    outline: none;
    border-color: #8e44ad;
    box-shadow: 0 0 0 4px rgba(155, 89, 182, 0.12), 0 8px 20px rgba(155, 89, 182, 0.2);
    transform: translateY(-1px);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* Enhanced Multi-select styling for source file filter */
.modern-select[multiple] {
    height: auto;
    min-height: 50px;
    max-height: 130px;
    overflow-y: auto;
    padding: 12px 16px;
    background-image: none; /* Remove dropdown arrow for multi-select */
    padding-right: 16px;
    border: 2px solid rgba(155, 89, 182, 0.2);
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 89, 182, 0.3) rgba(155, 89, 182, 0.1);
}

.modern-select[multiple]::-webkit-scrollbar {
    width: 6px;
}

.modern-select[multiple]::-webkit-scrollbar-track {
    background: rgba(155, 89, 182, 0.1);
    border-radius: 3px;
}

.modern-select[multiple]::-webkit-scrollbar-thumb {
    background: rgba(155, 89, 182, 0.3);
    border-radius: 3px;
}

.modern-select[multiple]::-webkit-scrollbar-thumb:hover {
    background: rgba(155, 89, 182, 0.5);
}

.modern-select[multiple] option {
    padding: 8px 12px;
    margin: 3px 0;
    border-radius: 6px;
    background: white;
    color: #2d3748;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.modern-select[multiple] option:checked {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    font-weight: 600;
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 4px rgba(155, 89, 182, 0.3);
}

.modern-select[multiple] option:hover {
    background: rgba(155, 89, 182, 0.12);
    border-color: rgba(155, 89, 182, 0.2);
    transform: translateX(2px);
}

.modern-select[multiple] option:checked:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    transform: translateX(2px);
}

/* Enhanced Action Buttons */
.action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: auto;
}

.btn-modern {
    padding: 14px 22px;
    border: none;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    min-height: 50px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern.refresh {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    border: 2px solid rgba(155, 89, 182, 0.2);
}

.btn-modern.refresh:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
}

.btn-modern .btn-icon {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.btn-modern:hover .btn-icon {
    transform: rotate(180deg);
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* Enhanced Customer Analytics Table - COMPACT VERSION */
.customer-analytics-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    table-layout: fixed; /* Fixed layout for better control */
}

/* Enhanced Table Headers - COMPACT */
.customer-analytics-table th {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    font-size: 0.75rem;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Optimized Column Widths for Compact Layout */
.customer-name-header { width: 22%; min-width: 140px; }
.customer-id-header { width: 12%; min-width: 90px; }
.date-header { width: 11%; min-width: 85px; text-align: center; }
.mmk-amount-header { width: 14%; min-width: 100px; text-align: right; }
.usd-amount-header { width: 14%; min-width: 100px; text-align: right; }
.count-header { width: 8%; min-width: 60px; text-align: center; }
.alert-status-header { width: 11%; min-width: 85px; text-align: center; }
.actions-header { width: 8%; min-width: 70px; text-align: center; }

/* Enhanced Table Cells - COMPACT */
.customer-analytics-table td {
    padding: 10px 8px;
    border-bottom: 1px solid rgba(155, 89, 182, 0.1);
    vertical-align: middle;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.8rem;
}

/* Column-specific cell styling */
.customer-name-cell {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.8rem;
    max-width: 140px;
}

.customer-id-cell {
    font-family: 'Courier New', monospace;
    font-size: 0.7rem;
    color: var(--text-light);
    background: rgba(155, 89, 182, 0.05);
    border-radius: 3px;
    padding: 2px 6px;
    display: inline-block;
    max-width: 90px;
}

.date-cell {
    text-align: center;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.75rem;
    max-width: 85px;
}

.mmk-amount, .usd-amount {
    text-align: right;
    font-weight: 600;
    font-family: 'Segoe UI', sans-serif;
    font-size: 0.8rem;
    max-width: 100px;
}

.mmk-amount {
    color: #f39c12;
}

.usd-amount {
    color: #3b82f6;
}

.count-cell {
    text-align: center;
    font-weight: 600;
    color: var(--text-color);
    background: rgba(46, 204, 113, 0.1);
    border-radius: 8px;
    padding: 2px 6px;
    display: inline-block;
    min-width: 30px;
    font-size: 0.75rem;
    max-width: 60px;
}

.alert-status-cell {
    text-align: center;
}

.actions-cell {
    text-align: center;
}

/* Enhanced Row Hover Effects */
.customer-analytics-table tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.customer-analytics-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.08) 0%, rgba(155, 89, 182, 0.04) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(155, 89, 182, 0.15);
}

.customer-analytics-table tbody tr:nth-child(even) {
    background-color: rgba(155, 89, 182, 0.02);
}

.customer-analytics-table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.08) 0%, rgba(155, 89, 182, 0.04) 100%);
}

/* Enhanced Alert Status Badges */
.alert-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
}

.alert-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.alert-status:hover::before {
    left: 100%;
}

.alert-status.high-alert {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
    border-color: rgba(231, 76, 60, 0.3);
    animation: pulse-red 2s infinite;
}

.alert-status.mmk-alert {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.4);
    border-color: rgba(243, 156, 18, 0.3);
}

.alert-status.usd-alert {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    border-color: rgba(59, 130, 246, 0.3);
}

.alert-status.no-alert {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.4);
    border-color: rgba(46, 204, 113, 0.3);
}

@keyframes pulse-red {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6);
        transform: scale(1.05);
    }
}

/* Enhanced Action Buttons - COMPACT */
.customer-action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 4px;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    min-width: 70px;
    justify-content: center;
}

.customer-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.customer-action-btn:hover::before {
    left: 100%;
}

.customer-action-btn.view-details {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
    border: 2px solid rgba(155, 89, 182, 0.2);
}

.customer-action-btn.view-details:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
}

.customer-action-btn.view-details:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(155, 89, 182, 0.3);
}

.customer-action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn-icon {
    font-size: 1rem;
    display: inline-block;
}

.btn-text {
    font-size: 0.85rem;
    font-weight: 600;
}

/* Customer Analytics Summary */
.customer-analytics-summary {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-top: 1px solid var(--border-color);
}

.customer-analytics-summary .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.customer-analytics-summary .summary-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #9b59b6;
}

.customer-analytics-summary .stat-label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.customer-analytics-summary .stat-value {
    font-weight: 700;
    color: #9b59b6;
    font-size: 1.1rem;
}

/* Enhanced Responsive Design for Customer Analytics - COMPACT */
@media (max-width: 1200px) {
    .customer-analytics-table th,
    .customer-analytics-table td {
        padding: 8px 6px;
        font-size: 0.75rem;
    }

    .customer-name-header, .customer-name-cell { min-width: 120px; max-width: 120px; }
    .customer-id-header, .customer-id-cell { min-width: 80px; max-width: 80px; }
    .date-header, .date-cell { min-width: 75px; max-width: 75px; }
    .mmk-amount-header, .mmk-amount { min-width: 90px; max-width: 90px; }
    .usd-amount-header, .usd-amount { min-width: 90px; max-width: 90px; }
    .count-header, .count-cell { min-width: 50px; max-width: 50px; }
    .alert-status-header, .alert-status-cell { min-width: 75px; max-width: 75px; }
    .actions-header, .actions-cell { min-width: 60px; max-width: 60px; }
}

@media (max-width: 1024px) {
    .customer-analytics-metrics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .customer-details-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .customer-details-controls {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding: 16px;
        gap: 16px;
    }

    .filter-group.modern {
        min-width: 150px;
        padding: 12px;
    }

    .modern-select {
        padding: 12px 16px;
        font-size: 0.85rem;
    }

    .btn-modern {
        padding: 12px 18px;
        min-height: 44px;
    }

    .customer-analytics-table {
        font-size: 0.85rem;
    }

    .customer-action-btn {
        padding: 6px 12px;
        min-width: 80px;
    }

    .btn-text {
        display: none;
    }

    .btn-icon {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .customer-analytics-metrics-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .customer-analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .customer-analytics-header-buttons {
        margin-left: 0;
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 8px;
    }

    .customer-details-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 16px;
        margin-bottom: 20px;
    }

    .filters-container {
        flex-direction: column;
        gap: 12px;
    }

    .filter-group.modern {
        width: 100%;
        min-width: unset;
        padding: 14px;
    }

    .filter-group.modern label {
        font-size: 0.8rem;
        margin-bottom: 6px;
    }

    .modern-select {
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    .action-buttons {
        margin-left: 0;
        justify-content: center;
        width: 100%;
    }

    .btn-modern {
        flex: 1;
        justify-content: center;
        min-height: 48px;
    }

    .customer-analytics-table {
        font-size: 0.75rem;
    }

    .customer-analytics-table th,
    .customer-analytics-table td {
        padding: 6px 4px;
    }

    /* Hide less important columns on mobile */
    .customer-id-header,
    .customer-id-cell,
    .count-header,
    .count-cell {
        display: none;
    }

    .customer-analytics-summary .summary-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .alert-status {
        font-size: 0.7rem;
        padding: 4px 8px;
        min-width: 60px;
    }

    .customer-action-btn {
        padding: 8px;
        min-width: 40px;
        border-radius: 50%;
    }
}

@media (max-width: 480px) {
    .customer-analytics-table th,
    .customer-analytics-table td {
        padding: 8px 4px;
    }

    .customer-name-cell {
        font-size: 0.85rem;
    }

    .mmk-amount, .usd-amount {
        font-size: 0.8rem;
    }

    .date-cell {
        font-size: 0.8rem;
    }

    .alert-status {
        font-size: 0.65rem;
        padding: 3px 6px;
        min-width: 50px;
    }
}

/* Customer Transaction Details View */
.customer-transaction-details {
    margin-top: 25px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid rgba(155, 89, 182, 0.2);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
    animation: slideDown 0.3s ease-out;
}

.customer-transaction-details:hover {
    box-shadow: var(--shadow-md), 0 5px 15px rgba(155, 89, 182, 0.1);
}

.customer-transaction-details-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.customer-transaction-details-header h4 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.customer-transaction-details-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.customer-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.customer-info-label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.customer-info-value {
    color: #9b59b6;
    font-weight: 600;
    font-size: 0.9rem;
    background: rgba(155, 89, 182, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

.customer-transaction-summary-stats {
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.05) 0%, rgba(155, 89, 182, 0.02) 100%);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.summary-stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-stat-item .stat-label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.summary-stat-item .stat-value {
    font-weight: 700;
    color: #9b59b6;
    font-size: 1rem;
}

.customer-transaction-details-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.customer-transaction-details-table th {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    font-size: 0.8rem;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.customer-transaction-details-table th:first-child {
    border-top-left-radius: 0;
}

.customer-transaction-details-table th:last-child {
    border-top-right-radius: 0;
}

.customer-transaction-details-table td {
    padding: 10px 8px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    transition: background-color 0.2s ease;
}

.customer-transaction-details-table tbody tr:hover {
    background-color: rgba(155, 89, 182, 0.05);
}

.customer-transaction-details-table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.customer-transaction-details-table tbody tr:nth-child(even):hover {
    background-color: rgba(155, 89, 182, 0.05);
}

/* Transaction Type Styling */
.transaction-type {
    padding: 3px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    min-width: 40px;
    text-align: center;
}

.transaction-type.hoc {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.transaction-type.ibd {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
}

.transaction-type.wu {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

/* Amount Cell Styling */
.amount-cell-details {
    font-weight: 600;
    text-align: right;
}

.amount-cell-details.high-value {
    color: #e74c3c;
    font-weight: 700;
}

/* Time Cell Styling */
.time-cell {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Role Cell Styling */
.role-cell {
    font-weight: 600;
    color: var(--text-color);
    text-transform: uppercase;
    font-size: 0.8rem;
}

/* Responsive Design for Customer Transaction Details */
@media (max-width: 1024px) {
    .customer-transaction-details-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .customer-transaction-details-controls {
        width: 100%;
        justify-content: space-between;
    }

    .customer-transaction-summary-stats {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .customer-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .customer-transaction-details-table {
        font-size: 0.75rem;
    }

    .customer-transaction-details-table th,
    .customer-transaction-details-table td {
        padding: 6px 4px;
    }

    .customer-transaction-summary-stats {
        padding: 10px 15px;
    }
}

/* Customer Transaction Details Modal */
.customer-transaction-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    overflow: hidden;
    animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.customer-transaction-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    animation: backdropFadeIn 0.3s ease-out;
}

.modal-container {
    position: relative;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 95%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(155, 89, 182, 0.2);
}

/* Modern Modal Styles */
.modal-container.modern {
    max-width: 1000px;
    max-height: 85vh;
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(155, 89, 182, 0.15);
}

.modal-header.modern {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    padding: 20px 24px;
    border-bottom: none;
    position: relative;
    overflow: hidden;
}

.modal-header.modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    pointer-events: none;
}

.title-with-badge {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.modal-title {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
    color: white;
}

.modal-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.modal-customer-info.compact {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 8px;
}

.modal-customer-info.compact .customer-info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 500;
}

.info-icon {
    font-size: 0.9rem;
    opacity: 0.8;
}

.modal-close-btn.modern {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.modal-close-btn.modern:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body.compact {
    padding: 20px 24px;
    background: #f8f9fa;
}

.modal-summary-stats.compact {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

.summary-stat-card.compact {
    background: white;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(155, 89, 182, 0.1);
    transition: all 0.3s ease;
}

.summary-stat-card.compact:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(155, 89, 182, 0.15);
}

.summary-stat-card.compact .stat-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.summary-stat-card.compact .stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
}

.summary-stat-card.compact .stat-value.mmk {
    color: #f39c12;
}

.summary-stat-card.compact .stat-value.usd {
    color: #3b82f6;
}

.summary-stat-card.compact .stat-label {
    font-size: 0.8rem;
    font-weight: 500;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Modal Header */
.modal-header {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.modal-title-section {
    flex: 1;
}

.modal-title {
    margin: 0 0 12px 0;
    font-size: 1.4rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.modal-customer-info {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.customer-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-label {
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.9;
}

.info-value {
    font-size: 0.95rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.modal-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    margin-left: 16px;
}

.modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.close-icon {
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Modal Body */
.modal-body {
    padding: 24px;
    overflow-y: auto;
    max-height: calc(90vh - 200px);
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* Summary Statistics Cards */
.modal-summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.summary-stat-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(155, 89, 182, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.summary-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.summary-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.15);
}

.stat-icon {
    font-size: 1.8rem;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.1), rgba(155, 89, 182, 0.05));
    border-radius: 12px;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stat-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #9b59b6;
}

/* Modal Table Container */
.modal-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(155, 89, 182, 0.1);
    overflow: hidden;
}

/* Compact Modal Table Styles */
.modal-table-container.compact {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(155, 89, 182, 0.08);
}

.modal-table-header.compact {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 12px 16px;
    border-bottom: 1px solid rgba(155, 89, 182, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-table-header.compact h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.table-control-btn.modern {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 4px rgba(155, 89, 182, 0.2);
}

.table-control-btn.modern:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(155, 89, 182, 0.3);
}

.modal-table-wrapper.compact {
    max-height: 400px;
    overflow-y: auto;
    background: white;
}

.modal-transaction-table.compact {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    background: white;
}

.modal-transaction-table.compact th {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    font-size: 0.8rem;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.modal-transaction-table.compact td {
    padding: 8px;
    border-bottom: 1px solid rgba(155, 89, 182, 0.08);
    vertical-align: middle;
    transition: background-color 0.2s ease;
    font-size: 0.8rem;
}

.modal-transaction-table.compact tbody tr:hover {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.04) 0%, rgba(155, 89, 182, 0.02) 100%);
}

.modal-transaction-table.compact tbody tr:nth-child(even) {
    background-color: rgba(155, 89, 182, 0.02);
}

.modal-footer.compact {
    padding: 16px 24px;
    background: #f8f9fa;
    border-top: 1px solid rgba(155, 89, 182, 0.1);
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.modal-action-btn.modern {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-action-btn.modern:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Currency Badge Styles */
.currency-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    display: inline-block;
    min-width: 35px;
    text-align: center;
}

.currency-badge.mmk {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
}

.currency-badge.usd {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.currency-badge.sgd {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

/* Modal Transaction Table Currency Column - White Text Override */
.modal-transaction-table .currency-cell .currency-badge,
.modal-transaction-table.compact .currency-cell .currency-badge {
    color: #ffffff !important;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Transaction Type Badge Styles */
.transaction-type {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.2px;
    display: inline-block;
}

.transaction-type.ctr {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.transaction-type.str {
    background: rgba(155, 89, 182, 0.1);
    color: #9b59b6;
    border: 1px solid rgba(155, 89, 182, 0.2);
}

.transaction-type.etr {
    background: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.2);
}

/* High Value Transaction Highlighting */
.amount-cell-details.high-value {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(231, 76, 60, 0.05) 100%);
    color: #e74c3c;
    font-weight: 700;
    border-left: 3px solid #e74c3c;
    padding-left: 8px;
}

/* Responsive adjustments for compact modal */
@media (max-width: 768px) {
    .modal-summary-stats.compact {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .summary-stat-card.compact {
        padding: 12px;
    }

    .summary-stat-card.compact .stat-value {
        font-size: 1.2rem;
    }

    .modal-transaction-table.compact {
        font-size: 0.75rem;
    }

    .modal-transaction-table.compact th,
    .modal-transaction-table.compact td {
        padding: 6px 4px;
    }

    .currency-badge {
        font-size: 0.65rem;
        padding: 2px 6px;
        min-width: 30px;
    }

    /* Ensure white text in modal currency column on mobile */
    .modal-transaction-table .currency-cell .currency-badge,
    .modal-transaction-table.compact .currency-cell .currency-badge {
        color: #ffffff !important;
        font-weight: 700;
    }

    .transaction-type {
        font-size: 0.65rem;
        padding: 1px 4px;
    }
}

.modal-table-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 16px 20px;
    border-bottom: 1px solid rgba(155, 89, 182, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-table-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
}

.table-controls {
    display: flex;
    gap: 8px;
}

.table-control-btn {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 6px;
}

.table-control-btn:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
}

/* Modal Table Wrapper */
.modal-table-wrapper {
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
}

.modal-transaction-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    background: white;
}

.modal-transaction-table th {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    font-size: 0.8rem;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.modal-transaction-table td {
    padding: 10px 8px;
    border-bottom: 1px solid rgba(155, 89, 182, 0.1);
    vertical-align: middle;
    transition: background-color 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.modal-transaction-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.05) 0%, rgba(155, 89, 182, 0.02) 100%);
}

.modal-transaction-table tbody tr:nth-child(even) {
    background-color: rgba(155, 89, 182, 0.02);
}

/* Column-specific styling */
.time-col { width: 120px; }
.amount-col { width: 120px; text-align: right; }
.currency-col { width: 80px; text-align: center; }
.type-col { width: 80px; text-align: center; }
.role-col { width: 100px; }
.counterparty-col { width: 150px; }
.counterparty-id-col { width: 120px; }
.account-col { width: 120px; }
.transaction-type-col { width: 120px; }
.serial-col { width: 100px; }
.channel-col { width: 80px; }
.location-col { width: 100px; }

/* Modal Footer */
.modal-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 16px 24px;
    border-top: 1px solid rgba(155, 89, 182, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.modal-footer-info {
    flex: 1;
}

.footer-text {
    font-size: 0.9rem;
    color: var(--text-light);
    font-style: italic;
}

.modal-footer-actions {
    display: flex;
    gap: 12px;
}

.modal-action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 100px;
}

.modal-action-btn.secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
}

.modal-action-btn.secondary:hover {
    background: linear-gradient(135deg, #7f8c8d 0%, #6c7b7d 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(127, 140, 141, 0.3);
}

/* Modal Animations */
@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes backdropFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
}

.customer-transaction-modal.closing .modal-container {
    animation: modalSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.customer-transaction-modal.closing .modal-backdrop {
    animation: backdropFadeIn 0.3s ease-out reverse;
}

/* Responsive Modal Design */
@media (max-width: 1024px) {
    .modal-container {
        width: 98%;
        max-height: 95vh;
    }

    .modal-summary-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .modal-table-wrapper {
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    .modal-container {
        width: 100%;
        height: 100%;
        max-height: 100vh;
        border-radius: 0;
    }

    .modal-header {
        padding: 16px 20px;
    }

    .modal-title {
        font-size: 1.2rem;
    }

    .modal-customer-info {
        flex-direction: column;
        gap: 8px;
    }

    .modal-body {
        padding: 16px;
    }

    .modal-summary-stats {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .summary-stat-card {
        padding: 12px;
    }

    .modal-transaction-table {
        font-size: 0.8rem;
    }

    .modal-transaction-table th,
    .modal-transaction-table td {
        padding: 8px 6px;
    }

    .modal-footer {
        padding: 12px 16px;
        flex-direction: column;
        align-items: stretch;
    }

    .modal-footer-actions {
        justify-content: center;
    }
}

/* High-Value USD Breakdown Styles */
.high-value-breakdown-usd {
    margin-top: 20px;
    background-color: rgba(59, 130, 246, 0.02);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
}

.high-value-breakdown-usd .breakdown-header {
    background-color: rgba(59, 130, 246, 0.05);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.high-value-breakdown-usd .breakdown-header:hover {
    background-color: rgba(59, 130, 246, 0.08);
}

/* High-Value Combined Breakdown Styles */
.high-value-breakdown-combined {
    margin-top: 20px;
    background-color: rgba(155, 89, 182, 0.02);
    border: 1px solid rgba(155, 89, 182, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
}

.high-value-breakdown-combined .breakdown-header {
    background-color: rgba(155, 89, 182, 0.05);
    border-bottom: 1px solid rgba(155, 89, 182, 0.1);
}

.high-value-breakdown-combined .breakdown-header:hover {
    background-color: rgba(155, 89, 182, 0.08);
}

.high-value-breakdown-combined .breakdown-toggle {
    color: #9b59b6;
}

.high-value-breakdown-combined .breakdown-toggle:hover {
    background-color: rgba(155, 89, 182, 0.1);
    color: #8e44ad;
}

/* Combined breakdown item styles */
.combined-breakdown-item {
    display: flex;
    flex-direction: column;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(155, 89, 182, 0.05);
    transition: all 0.2s ease;
}

.combined-breakdown-item:last-child {
    border-bottom: none;
}

.combined-breakdown-item:hover {
    background-color: rgba(155, 89, 182, 0.03);
}

.combined-breakdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.combined-date-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
}

.combined-total-count {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    padding: 6px 14px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(155, 89, 182, 0.3);
}

.currency-breakdown {
    display: flex;
    gap: 20px;
    margin-top: 8px;
}

.currency-item {
    flex: 1;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.currency-item.mmk {
    background-color: rgba(243, 156, 18, 0.08);
    border: 1px solid rgba(243, 156, 18, 0.15);
}

.currency-item.usd {
    background-color: rgba(59, 130, 246, 0.08);
    border: 1px solid rgba(59, 130, 246, 0.15);
}

.currency-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.currency-label {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.currency-item.mmk .currency-label {
    color: #e67e22;
}

.currency-item.usd .currency-label {
    color: #3b82f6;
}

.currency-count {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 4px;
}

.currency-item.mmk .currency-count {
    color: var(--warning-color);
}

.currency-item.usd .currency-count {
    color: #3b82f6;
}

.currency-details {
    font-size: 0.75rem;
    color: var(--text-light);
    line-height: 1.3;
}

/* Responsive design for combined breakdown */
@media (max-width: 768px) {
    .currency-breakdown {
        flex-direction: column;
        gap: 10px;
    }

    .combined-breakdown-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .combined-total-count {
        align-self: flex-end;
    }
}

/* High-Value Transaction Details Table Styles */
.high-value-transaction-details {
    margin-top: 25px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
}

.high-value-transaction-details:hover {
    box-shadow: var(--shadow-md), 0 5px 15px rgba(0, 0, 0, 0.03);
}

.transaction-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    flex-wrap: wrap;
    gap: 15px;
}

.transaction-details-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.transaction-details-header h4::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(to bottom, var(--primary-color), #2980b9);
    border-radius: 2px;
}

.transaction-details-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background-color: white;
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;
}

.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.filter-group select:hover {
    border-color: rgba(0, 0, 0, 0.2);
}

.transaction-details-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    background-color: white;
}

.transaction-details-table th {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    color: var(--text-color);
    font-weight: 600;
    padding: 15px 12px;
    text-align: left;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.transaction-details-table td {
    padding: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
    transition: all 0.2s ease;
}

.transaction-details-table tbody tr {
    transition: all 0.2s ease;
}

.transaction-details-table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.02);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.transaction-details-table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.01);
}

.transaction-details-table tbody tr:nth-child(even):hover {
    background-color: rgba(52, 152, 219, 0.02);
}

/* Amount column styling */
.transaction-details-table .amount-cell {
    font-weight: 600;
    text-align: right;
}

.transaction-details-table .amount-cell.mmk {
    color: var(--warning-color);
}

.transaction-details-table .amount-cell.usd {
    color: #3b82f6;
}

/* Currency column styling */
.transaction-details-table .currency-cell {
    font-weight: 600;
    text-align: center;
    padding: 6px 12px;
}

.transaction-details-table .currency-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.currency-badge.mmk {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

.currency-badge.usd {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Date column styling */
.transaction-details-table .date-cell {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--text-light);
}

/* Empty table message */
.empty-table-message td {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-light);
    font-style: italic;
    background-color: rgba(0, 0, 0, 0.01);
}

/* Transaction Summary Styles */
.transaction-summary {
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.summary-stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.summary-stat .stat-label {
    font-size: 0.85rem;
    color: var(--text-light);
    font-weight: 500;
}

.summary-stat .stat-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Make high-value cards clickable */
.high-value-card,
.high-value-card-usd {
    cursor: pointer;
    transition: all 0.3s ease;
}

.high-value-card:hover,
.high-value-card-usd:hover {
    transform: translateY(-3px);
    cursor: pointer;
}

.high-value-card.selected,
.high-value-card-usd.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2), 0 8px 25px rgba(52, 152, 219, 0.15);
}

.high-value-card.selected::before {
    background: linear-gradient(to bottom, var(--primary-color), #2980b9);
}

.high-value-card-usd.selected::before {
    background: linear-gradient(to bottom, var(--primary-color), #2980b9);
}

/* Responsive design for transaction details table */
@media (max-width: 1024px) {
    .transaction-details-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .transaction-details-controls {
        width: 100%;
        justify-content: flex-start;
    }

    .summary-stats {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .transaction-details-header {
        padding: 15px 20px;
    }

    .transaction-details-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-group {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    .filter-group select {
        min-width: 120px;
        flex: 1;
        margin-left: 10px;
    }

    .transaction-details-table {
        font-size: 0.8rem;
    }

    .transaction-details-table th,
    .transaction-details-table td {
        padding: 8px 6px;
    }

    .transaction-details-table th {
        font-size: 0.75rem;
    }

    /* Hide less important columns on mobile */
    .transaction-details-table th:nth-child(3),
    .transaction-details-table td:nth-child(3),
    .transaction-details-table th:nth-child(6),
    .transaction-details-table td:nth-child(6),
    .transaction-details-table th:nth-child(7),
    .transaction-details-table td:nth-child(7) {
        display: none;
    }

    .summary-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .summary-stat {
        padding: 10px 12px;
    }

    .transaction-summary {
        padding: 15px 20px;
    }
}

@media (max-width: 480px) {
    .transaction-details-table th,
    .transaction-details-table td {
        padding: 6px 4px;
    }

    .currency-badge {
        padding: 2px 6px !important;
        font-size: 0.7rem !important;
    }

    .transaction-details-header h4 {
        font-size: 1rem;
    }

    .filter-group label {
        font-size: 0.8rem;
    }

    .filter-group select {
        font-size: 0.85rem;
        padding: 6px 8px;
    }
}

/* High-Value File Metrics Cards */
.high-value-files-processed {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(46, 204, 113, 0.1);
    position: relative;
    overflow: hidden;
}

.high-value-files-processed::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--success-color), #27ae60);
}

.high-value-files-processed:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.15);
    border-color: rgba(46, 204, 113, 0.2);
}

.high-value-files-processed h4 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    padding-left: 15px;
}

.high-value-files-processed p {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--success-color);
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

.high-value-files-processed:hover p {
    transform: scale(1.05);
    color: #27ae60;
}

.high-value-files-removed {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(231, 76, 60, 0.1);
    position: relative;
    overflow: hidden;
}

.high-value-files-removed::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--danger-color), #c0392b);
}

.high-value-files-removed:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.15);
    border-color: rgba(231, 76, 60, 0.2);
}

.high-value-files-removed h4 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    padding-left: 15px;
}

.high-value-files-removed p {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--danger-color);
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

.high-value-files-removed:hover p {
    transform: scale(1.05);
    color: #c0392b;
}

.high-value-files-active {
    background: linear-gradient(135deg, #fff 0%, #fefefe 100%);
    border: 1px solid rgba(52, 152, 219, 0.1);
    position: relative;
    overflow: hidden;
}

.high-value-files-active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), #2980b9);
}

.high-value-files-active:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
    border-color: rgba(52, 152, 219, 0.2);
}

.high-value-files-active h4 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    padding-left: 15px;
}

.high-value-files-active p {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 8px 0;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding-left: 15px;
}

.high-value-files-active:hover p {
    transform: scale(1.05);
    color: #2980b9;
}

.metric-details {
    padding: 10px 15px 5px 15px;
    border-top: 1px solid rgba(243, 156, 18, 0.1);
    margin-top: 15px;
}

.metric-subtitle {
    font-size: 0.85rem;
    color: var(--text-light);
    font-style: italic;
    display: block;
    line-height: 1.4;
}

/* High-Value File Breakdown Styles */
.high-value-breakdown {
    margin-top: 20px;
    background-color: rgba(243, 156, 18, 0.02);
    border: 1px solid rgba(243, 156, 18, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
}

.breakdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: rgba(243, 156, 18, 0.05);
    border-bottom: 1px solid rgba(243, 156, 18, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
}

.breakdown-header:hover {
    background-color: rgba(243, 156, 18, 0.08);
}

.breakdown-header h4 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 600;
}

.breakdown-toggle {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: var(--warning-color);
    font-size: 0.9rem;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.breakdown-toggle:hover {
    background-color: rgba(243, 156, 18, 0.1);
    color: #e67e22;
}

.toggle-icon {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.breakdown-toggle[aria-expanded="true"] .toggle-icon {
    transform: rotate(180deg);
}

.breakdown-content {
    display: none;
    transition: all 0.3s ease;
}

.breakdown-content.expanded {
    display: block;
    max-height: 400px;
    overflow-y: auto;
}

.breakdown-list {
    padding: 0;
}

/* Breakdown Summary Styles */
.breakdown-summary {
    background-color: rgba(243, 156, 18, 0.08);
    border-bottom: 2px solid rgba(243, 156, 18, 0.15);
    padding: 15px 20px;
    margin-bottom: 5px;
}

.summary-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: space-between;
    align-items: center;
}

.stat-item {
    font-size: 0.85rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-item strong {
    color: var(--warning-color);
    font-weight: 700;
}

/* Transaction Details Styles */
.transaction-details-toggle {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.transaction-details-toggle:hover {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.transaction-details-toggle .toggle-icon {
    font-size: 0.8rem;
    transition: transform 0.2s ease;
}

.transaction-details-container {
    margin-top: 10px;
    padding: 0 15px;
    border-left: 3px solid rgba(52, 152, 219, 0.2);
    background-color: rgba(248, 249, 250, 0.8);
}

.transaction-details-table {
    margin: 10px 0;
}

.transaction-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.transaction-details-header h5 {
    margin: 0;
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 600;
}

.transaction-count {
    font-size: 0.85rem;
    color: var(--secondary-text-color);
    background-color: rgba(52, 152, 219, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
}

.transaction-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    background-color: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.transaction-table th {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.transaction-table td {
    padding: 10px 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.transaction-table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.transaction-table tbody tr:last-child td {
    border-bottom: none;
}

.participant-name {
    font-weight: 500;
    color: var(--text-color);
}

.counterparty-name {
    color: var(--secondary-text-color);
    font-style: italic;
}

.transaction-amount {
    font-weight: 600;
    color: var(--success-color);
    text-align: right;
    font-family: 'Courier New', monospace;
}

.report-type {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    text-align: center;
    font-size: 0.8rem;
}

.transaction-details-loading {
    text-align: center;
    padding: 20px;
    color: var(--secondary-text-color);
    font-style: italic;
}

.transaction-details-empty {
    text-align: center;
    padding: 20px;
    color: var(--secondary-text-color);
    font-style: italic;
    background-color: rgba(255, 193, 7, 0.1);
    border-radius: 4px;
}

/* Responsive design for transaction table */
@media (max-width: 768px) {
    .transaction-table {
        font-size: 0.8rem;
    }

    .transaction-table th,
    .transaction-table td {
        padding: 8px 6px;
    }

    .transaction-details-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .transaction-count {
        align-self: flex-end;
    }
}

/* Enhanced File Breakdown Item Styles */
.file-breakdown-item {
    display: flex;
    flex-direction: column;
    padding: 12px 20px;
    border-bottom: 1px solid rgba(243, 156, 18, 0.05);
    transition: all 0.2s ease;
}

.file-breakdown-item:last-child {
    border-bottom: none;
}

.file-breakdown-item:hover {
    background-color: rgba(243, 156, 18, 0.03);
}

.breakdown-item-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.breakdown-item-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-light);
    margin-left: 0;
}

.file-breakdown-name {
    flex: 1;
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
    margin-right: 15px;
    word-break: break-word;
}

.file-breakdown-count {
    background-color: var(--warning-color);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 600;
    min-width: 40px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(243, 156, 18, 0.3);
}

.breakdown-percentage {
    color: var(--warning-color);
    font-weight: 600;
}

.breakdown-rank {
    color: var(--text-light);
    font-style: italic;
}

.breakdown-empty {
    padding: 20px;
    text-align: center;
    color: var(--text-light);
    font-style: italic;
    font-size: 0.9rem;
}

/* Footer Styles */
footer {
    text-align: center;
    margin-top: 30px;
    padding: 20px 0;
    color: #7f8c8d;
}

/* Responsive Styles */
@media (max-width: 768px) {
    /* Upload Section Mobile Styles */
    .upload-main-container {
        flex-direction: column;
        gap: 20px;
        min-height: auto;
    }

    .upload-container {
        padding: 30px 20px;
        min-height: 250px;
    }

    .upload-container h3 {
        font-size: 1.2rem;
    }

    .upload-container p {
        font-size: 0.9rem;
    }

    .file-info {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .file-list {
        min-height: 300px;
    }

    .file-list ul {
        max-height: 250px;
    }

    .file-list li {
        padding: 12px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .file-list li .file-name {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .file-list li .file-status {
        align-self: flex-end;
        margin-right: 0;
    }

    .submit-btn {
        padding: 12px 25px;
        font-size: 1rem;
        width: 100%;
    }

    .upload-progress {
        padding: 20px;
        margin: 15px 0;
    }

    .upload-progress.visible,
    .upload-progress.show {
        display: block !important;
        opacity: 1 !important;
        transform: translateY(0) !important;
    }

    .progress-container {
        height: 20px;
        margin-bottom: 12px;
    }

    .progress-bar {
        border-radius: 8px;
    }

    #progressText {
        font-size: 0.9rem;
        padding: 3px 0;
        line-height: 1.3;
    }

    /* Enhanced mobile metrics layout */
    .metrics-grid, .breakdown-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-top: 20px;
    }

    .metric-card, .breakdown-card {
        padding: 20px 16px;
        min-height: auto;
        border-radius: 10px;
        overflow: visible;
    }

    .breakdown-card h4 {
        font-size: 1.1rem;
        margin-bottom: 16px;
        padding-left: 16px;
        line-height: 1.4;
    }

    .breakdown-card h4::before {
        width: 3px;
        height: 20px;
    }

    .metric-card h4 {
        font-size: 1rem;
        margin-bottom: 12px;
        line-height: 1.3;
    }

    .metric-card p {
        font-size: 1.4rem;
        margin: 8px 0 4px 0;
        line-height: 1.1;
    }

    /* Mobile credit-debit breakdown */
    .credit-debit-breakdown {
        grid-template-columns: 1fr;
        gap: 12px;
        margin-top: 12px;
    }

    .credit-breakdown, .debit-breakdown {
        padding: 12px 10px;
    }

    .credit-breakdown h5, .debit-breakdown h5 {
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .credit-breakdown p, .debit-breakdown p {
        font-size: 0.8rem;
        padding: 5px 8px;
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .credit-breakdown p span, .debit-breakdown p span {
        font-size: 0.75rem;
        max-width: 100%;
        text-align: left;
    }

    .breakdown-details p {
        font-size: 0.85rem;
        padding: 8px 10px;
        flex-direction: column;
        align-items: flex-start;
        gap: 3px;
    }

    .breakdown-details p span {
        font-size: 0.8rem;
        max-width: 100%;
        text-align: left;
    }

    .breakdown-details > p {
        font-size: 0.95rem;
        padding: 10px 12px;
    }

    /* Mobile-specific memory usage optimizations */
    .performance-card {
        min-width: auto; /* Remove min-width constraint on mobile */
    }

    .memory-usage {
        font-size: 1.0rem !important;
        padding: 8px 10px;
        line-height: 1.4;
        white-space: normal;
        word-break: break-word; /* Allow breaking of long memory values */
        overflow-wrap: break-word;
        hyphens: auto;
        min-height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .high-value-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .high-value-header-buttons {
        margin-left: 0;
        justify-content: center;
    }

    .high-value-export-btn {
        width: 100%;
        justify-content: center;
        padding: 12px 20px;
        font-size: 1rem;
    }

    .breakdown-header-container {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .transaction-breakdown-export-btn {
        width: 100%;
        justify-content: center;
        padding: 12px 20px;
        font-size: 1rem;
    }

    .ttr-summary-export-btn {
        padding: 12px 20px;
        font-size: 1rem;
    }

    .high-value-metrics-grid {
        grid-template-columns: 1fr;
    }

    /* Mobile-friendly breakdown summary */
    .summary-stats {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .stat-item {
        font-size: 0.8rem;
    }

    /* Mobile-friendly breakdown items */
    .breakdown-item-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .breakdown-percentage,
    .breakdown-rank {
        font-size: 0.7rem;
    }

    /* Adjust breakdown content max height for mobile */
    .breakdown-content.expanded {
        max-height: 300px;
    }
}

/* Tablet Styles */
@media (max-width: 1024px) and (min-width: 769px) {
    .upload-main-container {
        gap: 20px;
    }

    .upload-container {
        padding: 35px 25px;
    }

    .file-list {
        padding: 20px;
    }

    .file-list ul {
        max-height: 280px;
    }

    /* Tablet-specific metrics improvements */
    .metrics-grid, .breakdown-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 20px;
    }

    .metric-card, .breakdown-card {
        padding: 22px 18px;
        min-height: auto;
        overflow: visible;
    }

    .breakdown-card h4 {
        font-size: 1.2rem;
        margin-bottom: 18px;
        padding-left: 18px;
    }

    .metric-card h4 {
        font-size: 1.05rem;
        margin-bottom: 14px;
    }

    .metric-card p {
        font-size: 1.6rem;
        margin: 10px 0 6px 0;
    }

    /* Tablet credit-debit breakdown */
    .credit-debit-breakdown {
        gap: 14px;
        margin-top: 14px;
    }

    .credit-breakdown, .debit-breakdown {
        padding: 14px 12px;
    }

    .credit-breakdown h5, .debit-breakdown h5 {
        font-size: 0.95rem;
        margin-bottom: 12px;
    }

    .credit-breakdown p, .debit-breakdown p {
        font-size: 0.8rem;
        padding: 5px 8px;
    }

    .credit-breakdown p span, .debit-breakdown p span {
        font-size: 0.75rem;
        max-width: 70%;
    }

    .breakdown-details p {
        font-size: 0.9rem;
        padding: 9px 12px;
    }

    .breakdown-details p span {
        font-size: 0.85rem;
        max-width: 65%;
    }

    .breakdown-details > p {
        font-size: 0.95rem;
        padding: 12px 14px;
    }

    /* Tablet-specific memory usage optimizations */
    .performance-card {
        min-width: 280px;
    }

    .memory-usage {
        font-size: 1.1rem !important;
        padding: 8px 12px;
        line-height: 1.3;
        white-space: normal;
        min-height: 40px;
        word-break: break-word;
        overflow-wrap: break-word;
    }
}

/* Hidden class for elements that should be initially hidden */
.hidden {
    display: none;
}

/* Memory Monitor Styles */
.memory-monitor {
    position: fixed;
    bottom: 10px;
    right: 10px;
    padding: 5px 10px;
    background-color: rgba(52, 152, 219, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.memory-monitor.caution {
    background-color: rgba(241, 196, 15, 0.8);
    color: #333;
}

.memory-monitor.warning {
    background-color: rgba(231, 76, 60, 0.8);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Export Notification Styles */
.export-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    border-left: 4px solid #27ae60;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 0;
    z-index: 1100;
    max-width: 350px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.export-notification.show {
    transform: translateX(0);
}

.export-notification-content {
    display: flex;
    align-items: center;
    padding: 15px;
    position: relative;
}

.export-success-icon {
    background-color: #27ae60;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 14px;
}

.export-notification p {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
    flex: 1;
}

.export-notification-close {
    background: none;
    border: none;
    color: #999;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
}

.export-notification-close:hover {
    color: #333;
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    background-color: #2ecc71;
    color: white;
    border-radius: 6px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    max-width: 300px;
}

.notification.show {
    opacity: 1;
    transform: translateY(0);
}

.notification.error {
    background-color: #e74c3c;
}

.notification.warning {
    background-color: #f39c12;
}

.notification.info {
    background-color: #3498db;
}

.notification-message {
    margin: 0;
    font-weight: 500;
}

.notification-close {
    position: absolute;
    top: 5px;
    right: 5px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.7;
}

.notification-close:hover {
    opacity: 1;
}

/* TTR Monitoring Styles */
:root {
    --ttr-danger-color: #dc3545;
    --ttr-warning-color: #ffc107;
    --ttr-info-color: #17a2b8;
    --ttr-success-color: #28a745;
    --ttr-dark-color: #343a40;
}

/* TTR Header Section */
.ttr-header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    color: white;
    box-shadow: var(--shadow-lg);
}

.ttr-header-container h2 {
    margin: 0 0 8px 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.ttr-subtitle {
    margin: 0 0 20px 0;
    opacity: 0.9;
    font-size: 1rem;
}

.ttr-status-indicators {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #6c757d;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background-color: var(--ttr-success-color);
}

.status-dot.alert {
    background-color: var(--ttr-danger-color);
}

.status-text {
    font-size: 0.9rem;
    font-weight: 500;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* TTR Summary Section */
.ttr-summary-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 22px;
    box-shadow: var(--shadow-md);
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
}

.ttr-summary-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--ttr-danger-color), var(--ttr-warning-color));
}

.ttr-summary-section h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--text-color);
    position: relative;
    display: inline-block;
}

.ttr-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.ttr-metric-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 18px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.ttr-metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.ttr-metric-card.priority-high {
    border-left: 4px solid var(--ttr-danger-color);
}

.ttr-metric-card.priority-medium {
    border-left: 4px solid var(--ttr-warning-color);
}

.ttr-metric-card.priority-low {
    border-left: 4px solid var(--ttr-info-color);
}

.ttr-metric-card.threshold-breaches {
    border-left: 4px solid var(--ttr-dark-color);
}

.ttr-metric-card h4 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
}

.ttr-metric-card p {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 8px 0;
    transition: all 0.2s ease;
}

.ttr-metric-card.priority-high p {
    color: var(--ttr-danger-color);
}

.ttr-metric-card.priority-medium p {
    color: var(--ttr-warning-color);
}

.ttr-metric-card.priority-low p {
    color: var(--ttr-info-color);
}

.ttr-metric-card.threshold-breaches p {
    color: var(--ttr-dark-color);
}

/* TTR Alerts Section */
.ttr-alerts-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 22px;
    box-shadow: var(--shadow-md);
    margin-bottom: 25px;
}

.alerts-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.alerts-header h3 {
    font-size: 1.2rem;
    color: var(--text-color);
    margin: 0;
}

.alerts-controls {
    display: flex;
    gap: 15px;
    align-items: flex-end;
    flex-wrap: wrap;
}

/* TTR Configuration Section */
.ttr-config-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 22px;
    box-shadow: var(--shadow-md);
    margin-bottom: 25px;
}

.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

.config-header h3 {
    font-size: 1.2rem;
    color: var(--text-color);
    margin: 0;
}

.config-toggle {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
    color: var(--text-color);
}

.config-toggle:hover {
    background-color: var(--bg-color);
    border-color: var(--primary-color);
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.config-toggle[aria-expanded="true"] .toggle-icon {
    transform: rotate(180deg);
}

.config-content {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 25px;
}

.config-group {
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 20px;
    border-left: 4px solid var(--primary-color);
}

.config-group h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.config-item {
    margin-bottom: 15px;
}

.config-item:last-child {
    margin-bottom: 0;
}

.config-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.config-item input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: var(--transition);
}

.config-item input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.config-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* TTR Table Enhancements */
.alert-row {
    transition: background-color 0.2s ease;
}

.alert-row:hover {
    background-color: #f8f9fa;
}

.alert-row.priority-high {
    border-left: 4px solid var(--ttr-danger-color);
}

.alert-row.priority-medium {
    border-left: 4px solid var(--ttr-warning-color);
}

.alert-row.priority-low {
    border-left: 4px solid var(--ttr-info-color);
}

.priority-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-badge.priority-high {
    background-color: var(--ttr-danger-color);
    color: white;
}

.priority-badge.priority-medium {
    background-color: var(--ttr-warning-color);
    color: #333;
}

.priority-badge.priority-low {
    background-color: var(--ttr-info-color);
    color: white;
}

.score-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.score-badge.score-high {
    background-color: var(--ttr-danger-color);
    color: white;
}

.score-badge.score-medium {
    background-color: var(--ttr-warning-color);
    color: #333;
}

.score-badge.score-low {
    background-color: var(--ttr-info-color);
    color: white;
}

.alert-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.8rem;
    min-width: auto;
}

/* Table Sorting */
.data-table th.sort-asc::after {
    content: ' ↑';
    color: var(--primary-color);
}

.data-table th.sort-desc::after {
    content: ' ↓';
    color: var(--primary-color);
}

/* Category Header Styles */
.category-header-row {
    background-color: #f8f9fa;
    border-top: 2px solid #e9ecef;
}

.category-header-row:hover {
    background-color: #e9ecef;
}

.category-header {
    padding: 0 !important;
    border-bottom: 1px solid #dee2e6;
}

.category-header-content {
    width: 100%;
}

.category-toggle-btn {
    width: 100%;
    background: none;
    border: none;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 600;
    color: #495057;
    text-align: left;
    transition: all 0.2s ease;
}

.category-toggle-btn:hover {
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--primary-color);
}

.category-icon {
    font-size: 1.1rem;
    min-width: 20px;
}

.category-name {
    flex: 1;
    font-weight: 600;
}

.category-count-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.toggle-icon {
    font-size: 0.8rem;
    color: #6c757d;
    transition: transform 0.2s ease;
    min-width: 12px;
    text-align: center;
}

.category-toggle-btn[aria-expanded="false"] .toggle-icon {
    transform: rotate(-90deg);
}

/* Alert Row Category Styling */
.alert-row.category-threshold {
    border-left-color: #dc3545;
}

.alert-row.category-structuring {
    border-left-color: #fd7e14;
}

.alert-row.category-velocity {
    border-left-color: #ffc107;
}

.alert-row.category-pattern {
    border-left-color: #20c997;
}

.alert-row.category-behavioral {
    border-left-color: #6f42c1;
}

/* Category-specific count badge colors */
.category-header-row[data-category-type="threshold"] .category-count-badge {
    background-color: #dc3545;
}

.category-header-row[data-category-type="structuring"] .category-count-badge {
    background-color: #fd7e14;
}

.category-header-row[data-category-type="velocity"] .category-count-badge {
    background-color: #ffc107;
    color: #333;
}

.category-header-row[data-category-type="pattern"] .category-count-badge {
    background-color: #20c997;
}

.category-header-row[data-category-type="behavioral"] .category-count-badge {
    background-color: #6f42c1;
}

/* Consolidated Alert Styles */
.consolidated-badge {
    background-color: #17a2b8;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-left: 10px;
}

.consolidated-header {
    margin-bottom: 20px;
}

.consolidated-summary {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-top: 10px;
}

.consolidated-count {
    background-color: #e9ecef;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.85rem;
    color: #495057;
}

.consolidated-total {
    background-color: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
}

.consolidated-rules {
    margin: 15px 0;
}

.rules-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.rule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.rule-type {
    flex: 1;
    font-weight: 500;
}

.rule-amount {
    margin: 0 10px;
    color: #495057;
}

.rule-score {
    min-width: 40px;
    text-align: center;
}

.individual-alerts-container {
    max-height: 400px;
    overflow-y: auto;
    margin: 20px 0;
}

.individual-alert-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.individual-alert-header {
    background-color: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.individual-alert-header h4 {
    margin: 0;
    font-size: 1rem;
    color: #495057;
}

.individual-alert-details {
    padding: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 4px 0;
}

.detail-row label {
    font-weight: 600;
    color: #495057;
    min-width: 100px;
}

.detail-row span {
    flex: 1;
    text-align: right;
}

/* Expand button styling */
.expand-alert-btn {
    background-color: #17a2b8;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 0.8rem;
    cursor: pointer;
    margin: 0 2px;
}

.expand-alert-btn:hover {
    background-color: #138496;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background-color: var(--bg-color);
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Alert Details Styles */
.alert-details {
    color: var(--text-color);
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.alert-header h3 {
    margin: 0;
    color: var(--text-color);
}

.alert-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item label {
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.9rem;
}

.info-item span {
    color: var(--text-color);
    font-weight: 500;
}

.alert-description {
    margin-bottom: 20px;
}

.alert-description label {
    display: block;
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.alert-description p {
    margin: 0;
    color: var(--text-color);
    line-height: 1.5;
    background-color: var(--bg-color);
    padding: 12px;
    border-radius: var(--border-radius);
}

.alert-metadata {
    margin-bottom: 20px;
}

.alert-metadata label {
    display: block;
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    background-color: var(--bg-color);
    padding: 15px;
    border-radius: var(--border-radius);
}

.metadata-item {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.metadata-item label {
    font-weight: 500;
    color: var(--text-light);
    font-size: 0.8rem;
    margin-bottom: 0;
}

.metadata-item span {
    color: var(--text-color);
    font-size: 0.9rem;
}

.alert-actions-modal {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Responsive Design for TTR Components */
@media (max-width: 768px) {
    .nav-menu {
        flex-direction: column;
        gap: 5px;
    }

    .nav-link {
        padding: 10px 16px;
        text-align: center;
    }

    .ttr-metrics-grid {
        grid-template-columns: 1fr;
    }

    .alerts-header {
        flex-direction: column;
        align-items: stretch;
    }

    .alerts-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }

    .alert-info-grid {
        grid-template-columns: 1fr;
    }

    .metadata-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .alert-actions {
        flex-direction: column;
        gap: 3px;
    }

    .btn-sm {
        padding: 6px 10px;
        font-size: 0.75rem;
    }

    /* Category header responsive adjustments */
    .category-toggle-btn {
        padding: 10px 12px;
        font-size: 0.9rem;
        gap: 8px;
    }

    .category-icon {
        font-size: 1rem;
    }

    .category-count-badge {
        font-size: 0.7rem;
        padding: 2px 6px;
    }

    .toggle-icon {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .ttr-header-container h2 {
        font-size: 1.4rem;
    }

    .ttr-status-indicators {
        flex-direction: column;
        gap: 10px;
    }

    .config-item input {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Extra small screen metrics optimizations */
    .metrics-section, .breakdown-section {
        padding: 18px 14px;
        margin-bottom: 18px;
    }

    .metrics-section h3, .breakdown-section h3 {
        font-size: 1.1rem;
        margin-bottom: 16px;
    }

    .breakdown-section h3::before {
        font-size: 0.9rem;
        margin-right: 6px;
    }

    .metric-card, .breakdown-card {
        padding: 16px 12px;
        min-height: auto;
        overflow: visible;
    }

    .breakdown-card h4 {
        font-size: 1rem;
        margin-bottom: 14px;
        padding-left: 14px;
    }

    .breakdown-card h4::before {
        width: 3px;
        height: 16px;
    }

    .metric-card h4 {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    .metric-card p {
        font-size: 1.2rem;
        margin: 6px 0 2px 0;
    }

    /* Extra small screen credit-debit breakdown */
    .credit-debit-breakdown {
        grid-template-columns: 1fr;
        gap: 10px;
        margin-top: 10px;
    }

    .credit-breakdown, .debit-breakdown {
        padding: 10px 8px;
    }

    .credit-breakdown h5, .debit-breakdown h5 {
        font-size: 0.85rem;
        margin-bottom: 8px;
    }

    .credit-breakdown h5::before, .debit-breakdown h5::before {
        font-size: 0.8rem;
        margin-right: 4px;
    }

    .credit-breakdown p, .debit-breakdown p {
        font-size: 0.75rem;
        padding: 4px 6px;
        flex-direction: column;
        align-items: flex-start;
        gap: 1px;
    }

    .credit-breakdown p span, .debit-breakdown p span {
        font-size: 0.7rem;
        max-width: 100%;
        text-align: left;
    }

    .breakdown-details p {
        font-size: 0.8rem;
        padding: 6px 8px;
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .breakdown-details p span {
        font-size: 0.75rem;
        max-width: 100%;
        text-align: left;
    }

    .breakdown-details > p {
        font-size: 0.85rem;
        padding: 8px 10px;
    }

    /* Extra small screen memory usage optimizations */
    .performance-card {
        min-width: auto;
    }

    .memory-usage {
        font-size: 0.9rem !important;
        padding: 6px 8px;
        line-height: 1.5;
        white-space: normal;
        word-break: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        text-align: center;
        min-height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 0.01em;
    }
}

/* Performance Monitor Styles */
.performance-card {
    border-left: 4px solid #3498db;
    position: relative;
    min-width: 320px; /* Ensure sufficient width for memory display */
}

.performance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #3498db, #2980b9);
}

/* Enhanced Memory Usage Display */
.memory-usage {
    font-size: 1.3rem !important;
    font-weight: 600;
    padding: 12px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    line-height: 1.4;
    word-break: break-word;
    overflow-wrap: break-word;
    min-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: 0.02em;
}

/* Ensure percentage symbols are properly spaced */
.memory-usage::after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
}

/* Memory usage hover effects */
.performance-card:hover .memory-usage {
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.15);
}

/* Responsive text sizing for memory usage */
@media (max-width: 1200px) {
    .memory-usage {
        font-size: 1.2rem !important;
        white-space: normal;
        line-height: 1.3;
        min-height: 45px;
        padding: 10px 14px;
    }
}

.memory-usage.normal {
    background-color: #d4edda;
    color: #155724;
}

.memory-usage.warning {
    background-color: #fff3cd;
    color: #856404;
}

.memory-usage.critical {
    background-color: #f8d7da;
    color: #721c24;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Transaction Breakdown Section Responsive Design */
@media (max-width: 1024px) {
    .breakdown-section {
        padding: 32px 28px;
        margin-bottom: 32px;
    }

    .breakdown-section h3 {
        font-size: 1.4rem;
        margin-bottom: 28px;
    }

    .breakdown-grid {
        margin-top: 24px;
    }

    .breakdown-card h4 {
        padding: 24px 28px;
        font-size: 1.2rem;
    }

    .breakdown-details {
        padding: 24px 28px;
    }

    .breakdown-details p {
        padding: 12px 16px;
        font-size: 0.95rem;
    }

    .credit-breakdown, .debit-breakdown {
        padding: 18px 16px;
    }
}

/* TTR Summary Report Section */
.ttr-summary-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 32px;
    box-shadow: var(--shadow-md);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.ttr-summary-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg,
        #8e44ad 0%,
        #9b59b6 25%,
        #a569bd 75%,
        #bb8fce 100%);
    box-shadow: 0 2px 8px rgba(142, 68, 173, 0.3);
}

.ttr-summary-section:hover {
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(142, 68, 173, 0.15);
    transform: translateY(-2px);
}

.ttr-summary-section h3 {
    font-size: 1.4rem;
    margin-bottom: 28px;
    color: var(--text-color);
    position: relative;
    display: inline-block;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.ttr-summary-section h3::before {
    content: '📊';
    margin-right: 12px;
    font-size: 1.2rem;
}

.ttr-summary-section h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #8e44ad, #9b59b6);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.ttr-summary-section:hover h3::after {
    width: 120px;
    background: linear-gradient(90deg,
        #8e44ad 0%,
        #9b59b6 25%,
        #a569bd 75%,
        #bb8fce 100%);
    box-shadow:
        0 3px 12px rgba(142, 68, 173, 0.4),
        0 2px 6px rgba(0, 0, 0, 0.15);
}

.ttr-summary-table-container {
    margin-top: 28px;
    position: relative;
    z-index: 2;
}

.ttr-summary-table {
    width: 100%;
    border-collapse: collapse;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(250, 251, 252, 0.95) 100%);
    border-radius: 14px;
    overflow: hidden;
    box-shadow:
        0 6px 24px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(10px);
}

.ttr-summary-table thead {
    background: linear-gradient(135deg,
        #8e44ad 0%,
        #9b59b6 25%,
        #a569bd 75%,
        #bb8fce 100%);
    color: white;
    position: relative;
}

.ttr-summary-table thead::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(255, 255, 255, 0.3) 100%);
}

.ttr-summary-table th {
    padding: 18px 24px;
    text-align: left;
    font-weight: 700;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border-bottom: 2px solid rgba(255, 255, 255, 0.15);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
}

.ttr-summary-table th:first-child {
    border-top-left-radius: 14px;
}

.ttr-summary-table th:last-child {
    border-top-right-radius: 14px;
}

.ttr-summary-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: transparent;
}

.ttr-summary-table tbody tr:hover {
    background: linear-gradient(135deg,
        rgba(142, 68, 173, 0.08) 0%,
        rgba(155, 89, 182, 0.05) 100%);
    transform: translateX(4px);
    box-shadow:
        inset 4px 0 0 rgba(142, 68, 173, 0.4),
        0 2px 8px rgba(142, 68, 173, 0.15);
}

.ttr-summary-table tbody tr:last-child:hover {
    border-bottom-left-radius: 14px;
    border-bottom-right-radius: 14px;
}

.ttr-summary-table td {
    padding: 18px 24px;
    font-size: 0.95rem;
    color: var(--text-color);
    vertical-align: middle;
    position: relative;
    z-index: 2;
    background: transparent;
}

/* TTR Summary Table Cell Styling */
.ttr-report-date {
    font-weight: 700;
    font-size: 1.05rem;
    color: var(--text-color);
    position: relative;
    letter-spacing: 0.5px;
}

.ttr-serial-placeholder {
    color: var(--text-light);
    font-style: italic;
    opacity: 0.7;
}

.ttr-serial-value {
    font-weight: 600;
    color: #8e44ad;
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, rgba(142, 68, 173, 0.1) 0%, rgba(155, 89, 182, 0.05) 100%);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid rgba(142, 68, 173, 0.2);
    text-shadow: 0 1px 2px rgba(142, 68, 173, 0.1);
}

.ttr-serial-range {
    text-align: center;
    font-size: 0.9em;
    white-space: nowrap;
}

/* Special styling for WU Serial column with actual serial numbers */
.ttr-wu-serial-list {
    font-weight: 600;
    color: #27ae60;
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(46, 204, 113, 0.05) 100%);
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid rgba(39, 174, 96, 0.2);
    text-shadow: 0 1px 2px rgba(39, 174, 96, 0.1);
    text-align: left;
    font-size: 0.8em;
    max-width: 400px; /* CRITICAL FIX: Increased from 200px to accommodate full WU serial lists */
    min-width: 150px; /* Ensure minimum readable width */
    word-wrap: break-word;
    white-space: normal;
    line-height: 1.4;
    overflow-wrap: break-word; /* Better word breaking for long serial lists */
    hyphens: none; /* Prevent hyphenation of serial numbers */
}

.ttr-count-value {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--primary-color);
    text-align: center;
}

.ttr-grand-total {
    font-weight: 800;
    font-size: 1.2rem;
    color: var(--success-color);
    text-align: center;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.05));
    border-radius: 6px;
    padding: 8px 12px;
}

/* Currency Breakdown Section Responsive Design */
@media (max-width: 1024px) {
    .currency-breakdown-section {
        padding: 28px 24px;
        margin-bottom: 28px;
    }

    .currency-breakdown-section h3 {
        font-size: 1.3rem;
        margin-bottom: 24px;
    }

    .currency-breakdown-table th,
    .currency-breakdown-table td {
        padding: 14px 18px;
    }

    .currency-type {
        font-size: 0.95rem;
        letter-spacing: 0.6px;
    }

    .currency-amount,
    .currency-credit-amount,
    .currency-debit-amount {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    /* Transaction Breakdown Mobile Design */
    .breakdown-section {
        padding: 24px 20px;
        margin-bottom: 24px;
        border-radius: 14px;
    }

    .breakdown-section h3 {
        font-size: 1.3rem;
        margin-bottom: 24px;
        text-align: center;
    }

    .breakdown-grid {
        display: block;
        margin-top: 20px;
    }

    .breakdown-card {
        display: block;
        margin-bottom: 24px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 250, 252, 0.9) 100%);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.06);
    }

    .breakdown-card h4 {
        display: block;
        padding: 0 0 16px 0;
        font-size: 1.1rem;
        border-bottom: 2px solid rgba(0, 0, 0, 0.06);
        margin-bottom: 16px;
    }

    .breakdown-card h4::before {
        left: 0;
        top: 0;
        position: relative;
        transform: none;
        width: 20px;
        height: 4px;
        display: inline-block;
        margin-right: 12px;
        vertical-align: middle;
    }

    .breakdown-details {
        display: block;
        padding: 0;
    }

    .breakdown-details p {
        margin: 10px 0;
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    .credit-debit-breakdown {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-top: 16px;
        padding-top: 16px;
    }

    .credit-breakdown, .debit-breakdown {
        padding: 16px 14px;
    }

    .credit-breakdown h5, .debit-breakdown h5 {
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .credit-breakdown p, .debit-breakdown p {
        padding: 8px 10px;
        font-size: 0.85rem;
    }

    .currency-breakdown-section {
        padding: 24px 20px;
        margin-bottom: 24px;
        border-radius: 12px;
    }

    .currency-breakdown-section h3 {
        font-size: 1.2rem;
        margin-bottom: 20px;
        text-align: center;
    }

    .currency-breakdown-section h3::before {
        font-size: 1.1rem;
        margin-right: 10px;
    }

    .currency-breakdown-table-container {
        margin-top: 20px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .currency-breakdown-table {
        min-width: 600px;
        border-radius: 10px;
    }

    .currency-breakdown-table th,
    .currency-breakdown-table td {
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    .currency-breakdown-table th {
        font-size: 0.85rem;
        letter-spacing: 0.4px;
    }

    .currency-type {
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }

    .currency-type::before {
        left: -10px;
        width: 4px;
        height: 20px;
    }

    .currency-amount,
    .currency-credit-amount,
    .currency-debit-amount {
        font-size: 0.95rem;
    }

    .empty-table-message {
        padding: 40px 20px !important;
    }

    .empty-table-message::before {
        font-size: 2.5rem;
        margin-bottom: 12px;
    }

    .empty-table-message td {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    /* Transaction Breakdown Extra Small Mobile */
    .breakdown-section {
        padding: 20px 16px;
        margin-bottom: 20px;
        border-radius: 12px;
    }

    .breakdown-section h3 {
        font-size: 1.2rem;
        margin-bottom: 20px;
    }

    .breakdown-section h3::before {
        font-size: 1.2rem;
        margin-right: 12px;
    }

    .breakdown-card {
        padding: 16px;
        margin-bottom: 20px;
    }

    .breakdown-card h4 {
        font-size: 1rem;
        padding: 0 0 12px 0;
        margin-bottom: 12px;
    }

    .breakdown-card h4::before {
        width: 16px;
        height: 3px;
        margin-right: 10px;
    }

    .breakdown-details p {
        padding: 10px 12px;
        font-size: 0.85rem;
    }

    .breakdown-details p span {
        font-size: 0.9rem;
    }

    .credit-breakdown, .debit-breakdown {
        padding: 14px 12px;
    }

    .credit-breakdown h5, .debit-breakdown h5 {
        font-size: 0.85rem;
        margin-bottom: 10px;
    }

    .credit-breakdown p, .debit-breakdown p {
        padding: 6px 8px;
        font-size: 0.8rem;
    }

    .credit-breakdown p span, .debit-breakdown p span {
        font-size: 0.8rem;
    }

    .currency-breakdown-section {
        padding: 20px 16px;
        margin-bottom: 20px;
        border-radius: 10px;
    }

    .currency-breakdown-section h3 {
        font-size: 1.1rem;
        margin-bottom: 18px;
    }

    .currency-breakdown-section h3::before {
        font-size: 1rem;
        margin-right: 8px;
    }

    .currency-breakdown-table {
        min-width: 500px;
        border-radius: 8px;
    }

    .currency-breakdown-table th,
    .currency-breakdown-table td {
        padding: 10px 12px;
        font-size: 0.85rem;
    }

    .currency-breakdown-table th {
        font-size: 0.8rem;
        letter-spacing: 0.3px;
    }

    /* TTR Summary Report Responsive Design */
    .ttr-summary-section {
        padding: 20px 16px;
        margin-bottom: 20px;
        border-radius: 10px;
    }

    .ttr-summary-section h3 {
        font-size: 1.1rem;
        margin-bottom: 18px;
        text-align: center;
    }

    .ttr-summary-section h3::before {
        font-size: 1rem;
        margin-right: 8px;
    }

    .ttr-summary-table-container {
        margin-top: 20px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .ttr-summary-table {
        min-width: 900px; /* CRITICAL FIX: Increased to accommodate full WU serial lists */
        border-radius: 8px;
    }

    .ttr-summary-table th,
    .ttr-summary-table td {
        padding: 10px 12px;
        font-size: 0.85rem;
    }

    .ttr-summary-table th {
        font-size: 0.8rem;
        letter-spacing: 0.3px;
    }

    /* CRITICAL FIX: Mobile-specific styling for WU serial column */
    .ttr-wu-serial-list {
        max-width: 300px; /* Reduced for mobile but still larger than original */
        font-size: 0.75em;
        line-height: 1.3;
        padding: 4px 6px;
    }

    .ttr-report-date {
        font-size: 0.9rem;
        letter-spacing: 0.3px;
    }

    .ttr-count-value {
        font-size: 0.95rem;
    }

    .ttr-grand-total {
        font-size: 1rem;
        padding: 6px 8px;
    }

    .currency-type {
        font-size: 0.85rem;
        letter-spacing: 0.4px;
    }

    .currency-type::before {
        left: -8px;
        width: 3px;
        height: 18px;
    }

    .currency-amount,
    .currency-credit-amount,
    .currency-debit-amount {
        font-size: 0.9rem;
    }

    .empty-table-message {
        padding: 30px 16px !important;
    }

    .empty-table-message::before {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .empty-table-message td {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Enhanced mobile hover effects */
    .currency-breakdown-table tbody tr:hover {
        transform: translateX(2px) scale(1.002);
    }

    .currency-breakdown-section:hover {
        transform: translateY(-2px) scale(1.005);
    }
}

/* TTR File Management Styles */
.ttr-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.ttr-summary-container h3 {
    margin: 0;
    flex: 1;
}

.ttr-file-management {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 20px;
}

/* TTR File Management Panel */
.ttr-file-panel {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.95) 0%, rgba(233, 236, 239, 0.9) 100%);
    border: 1px solid rgba(142, 68, 173, 0.2);
    border-radius: 12px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow:
        0 4px 12px rgba(142, 68, 173, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
}

.ttr-file-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(142, 68, 173, 0.2);
}

.ttr-file-panel-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1.1em;
    font-weight: 600;
}

.ttr-file-list-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 10px;
    padding: 12px 15px;
    background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
    border-radius: 8px;
    font-weight: 600;
    color: white;
    margin-bottom: 10px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.ttr-file-list-container {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 8px;
}

.ttr-file-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 10px;
    padding: 12px 15px;
    border: 1px solid rgba(142, 68, 173, 0.15);
    border-radius: 6px;
    margin-bottom: 5px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 251, 252, 0.9) 100%);
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(142, 68, 173, 0.05);
}

.ttr-file-item:hover {
    background: linear-gradient(135deg, rgba(142, 68, 173, 0.05) 0%, rgba(155, 89, 182, 0.03) 100%);
    transform: translateX(4px);
    box-shadow:
        inset 3px 0 0 rgba(142, 68, 173, 0.4),
        0 4px 8px rgba(142, 68, 173, 0.1);
}

.ttr-file-name {
    font-weight: 500;
    color: #495057;
    word-break: break-word;
}

.ttr-file-processing-status {
    font-size: 0.75em;
    font-weight: 600;
    margin-left: 8px;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ttr-file-processing-status.processed {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ttr-file-processing-status.pending {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.ttr-file-status {
    text-align: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ttr-file-status.included {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ttr-file-status.excluded {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.ttr-file-actions {
    text-align: center;
}

.ttr-empty-file-message {
    text-align: center;
    padding: 30px 20px;
    color: #6c757d;
    font-style: italic;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.6) 100%);
    border-radius: 8px;
    border: 2px dashed rgba(142, 68, 173, 0.2);
}

/* TTR Table Actions Column */
.ttr-actions {
    white-space: nowrap;
    text-align: center;
}

.ttr-remove-file-btn {
    padding: 6px 12px;
    font-size: 0.8em;
    border-radius: 6px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ttr-remove-file-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.ttr-remove-file-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Responsive Design for TTR File Management */
@media (max-width: 768px) {
    .ttr-summary-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .ttr-file-management {
        margin-left: 0;
        justify-content: center;
    }

    .ttr-file-list-header,
    .ttr-file-item {
        grid-template-columns: 1fr;
        gap: 8px;
        text-align: center;
    }

    .ttr-file-panel {
        padding: 15px;
    }

    .ttr-file-panel-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

/* Very small screens - stack TTR buttons vertically */
@media (max-width: 480px) {
    .ttr-file-management {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .ttr-summary-export-btn {
        width: 100%;
        justify-content: center;
        margin-right: 0;
    }

    /* Very small screens - stack high-value buttons vertically */
    .high-value-header-buttons {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .high-value-export-btn {
        width: 100%;
        justify-content: center;
        margin-right: 0;
    }
}