/**
 * TTR File Manager
 * Handles file management specifically for TTR Summary Report section
 * Provides add/remove file functionality while maintaining TTR-specific state
 */

class TTRFileManager {
    constructor() {
        // Track which files are included in TTR Summary Report
        this.includedFiles = new Set();
        this.excludedFiles = new Set();

        // Track cumulative serial number ranges
        this.cumulativeSerialRanges = new Map(); // fileName -> {hoc: {start, end}, ibd: {start, end}, wu: {start, end}}
        this.cumulativeCounters = {
            hoc: 0,
            ibd: 0,
            wu: 0
        };

        // Interval for periodic restoration checks
        this.restorationInterval = null;

        // Initialize event listeners
        this.initializeEventListeners();

        console.log('TTR File Manager initialized');
    }

    /**
     * Initialize event listeners for TTR file management
     */
    initializeEventListeners() {
        // Listen for global file processing events
        document.addEventListener('fileProcessed', (event) => {
            console.log(`🔔 TTR File Manager: Received fileProcessed event for ${event.detail?.fileName}`, event.detail);
            this.handleFileProcessed(event.detail);
        });

        document.addEventListener('fileRemoved', (event) => {
            this.handleFileRemoved(event.detail);
        });

        // Listen for file upload events to update UI immediately when files are uploaded
        document.addEventListener('filesUploaded', (event) => {
            this.handleFilesUploaded(event.detail);
        });

        // TTR-specific UI event listeners
        document.addEventListener('DOMContentLoaded', () => {
            this.setupUIEventListeners();
        });

        // If DOM is already loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupUIEventListeners();
            });
        } else {
            this.setupUIEventListeners();
        }
    }

    /**
     * Setup UI event listeners for TTR file management
     */
    setupUIEventListeners() {
        const manageFilesBtn = document.getElementById('ttrManageFilesBtn');
        const filePanelClose = document.getElementById('ttrFilePanelClose');

        if (manageFilesBtn) {
            manageFilesBtn.addEventListener('click', () => {
                this.showFileManagementPanel();
            });
        }

        if (filePanelClose) {
            filePanelClose.addEventListener('click', () => {
                this.hideFileManagementPanel();
            });
        }
    }

    /**
     * Handle file processed event with automatic restoration and state management
     */
    handleFileProcessed(fileData) {
        console.log(`TTR File Manager: handleFileProcessed called for:`, fileData);

        if (!fileData || !fileData.fileName) {
            console.warn('TTR File Manager: Invalid file data received', fileData);
            return;
        }

        console.log(`TTR File Manager: Processing file ${fileData.fileName} from ${fileData.source || 'unknown'} source`);

        // AUTOMATIC RESTORATION: Restore any missing files before processing new one
        const restoredCount = this.restoreMissingFiles();
        if (restoredCount > 0) {
            console.log(`TTR File Manager: Auto-restored ${restoredCount} missing files during processing`);
        }

        // Ensure the current file exists in processed data
        const processedData = window.fileHandler?.processedData || [];
        const existsInProcessed = processedData.some(f => f.fileName === fileData.fileName);

        if (!existsInProcessed) {
            console.log(`TTR File Manager: File ${fileData.fileName} missing from processed data, adding it`);
            window.fileHandler.processedData.push(fileData);
        }

        // Automatically include new files in TTR Summary Report (remove from excluded if present)
        this.includedFiles.add(fileData.fileName);
        this.excludedFiles.delete(fileData.fileName);

        console.log(`TTR File Manager: File ${fileData.fileName} added to TTR Summary Report`);
        console.log(`TTR File Manager: Current included files:`, Array.from(this.includedFiles));
        console.log(`TTR File Manager: Current excluded files:`, Array.from(this.excludedFiles));

        // Use a delay to ensure the file is properly added to processedData before updating the table
        setTimeout(() => {
            // Verify the file is in processedData before proceeding
            const isInProcessedData = window.fileHandler?.processedData?.some(f => f.fileName === fileData.fileName);
            const hasFileMetrics = window.fileHandler?.fileMetrics?.has(fileData.fileName);

            console.log(`TTR File Manager: File ${fileData.fileName} verification - inProcessedData: ${isInProcessedData}, hasMetrics: ${hasFileMetrics}`);

            if (!isInProcessedData || !hasFileMetrics) {
                console.warn(`TTR File Manager: File ${fileData.fileName} not ready yet, retrying...`);
                // Retry after a longer delay
                setTimeout(() => {
                    this.handleFileProcessedRetry(fileData);
                }, 500);
                return;
            }

            // Recalculate cumulative serial ranges
            this.calculateCumulativeSerialRanges();

            // Update UI
            this.updateFileManagementUI();
            this.updateTTRSummaryReport();
            this.updateManageFilesButtonVisibility();
        }, 200);
    }

    /**
     * Retry handling file processed event with verification
     */
    handleFileProcessedRetry(fileData) {
        const isInProcessedData = window.fileHandler?.processedData?.some(f => f.fileName === fileData.fileName);
        const hasFileMetrics = window.fileHandler?.fileMetrics?.has(fileData.fileName);

        console.log(`TTR File Manager: Retry for ${fileData.fileName} - inProcessedData: ${isInProcessedData}, hasMetrics: ${hasFileMetrics}`);

        if (isInProcessedData && hasFileMetrics) {
            // Recalculate cumulative serial ranges
            this.calculateCumulativeSerialRanges();

            // Update UI
            this.updateFileManagementUI();
            this.updateTTRSummaryReport();
            this.updateManageFilesButtonVisibility();

            console.log(`TTR File Manager: Successfully updated TTR table for ${fileData.fileName}`);
        } else {
            console.error(`TTR File Manager: File ${fileData.fileName} still not ready after retry. Data may be missing.`);

            // Force update anyway to show what we can
            this.updateFileManagementUI();
            this.updateTTRSummaryReport();
            this.updateManageFilesButtonVisibility();
        }
    }

    /**
     * Handle file removed event
     */
    handleFileRemoved(fileData) {
        if (!fileData || !fileData.fileName) {
            console.warn('TTR File Manager: Invalid file removal data received');
            return;
        }

        console.log(`TTR File Manager: Handling removal of file ${fileData.fileName}`);

        // Remove from both sets
        this.includedFiles.delete(fileData.fileName);
        this.excludedFiles.delete(fileData.fileName);

        // Clear any cached ranges for this file
        this.cumulativeSerialRanges.delete(fileData.fileName);

        console.log(`TTR File Manager: File ${fileData.fileName} removed from TTR Summary Report`);
        console.log(`TTR File Manager: Remaining included files:`, Array.from(this.includedFiles));

        // Use a delay to ensure the file handler has completed its removal process
        setTimeout(() => {
            // Recalculate cumulative serial ranges for remaining files
            this.calculateCumulativeSerialRanges();

            // Update UI
            this.updateFileManagementUI();
            this.updateTTRSummaryReport();
            this.updateManageFilesButtonVisibility();
        }, 100);
    }

    /**
     * Handle files uploaded event
     */
    handleFilesUploaded(uploadData) {
        if (!uploadData || !uploadData.files || !Array.isArray(uploadData.files)) {
            console.warn('TTR File Manager: Invalid file upload data received');
            return;
        }

        console.log(`TTR File Manager: ${uploadData.files.length} files uploaded, updating UI`);

        // Update UI to show newly uploaded files in the manage files panel
        this.updateFileManagementUI();
        this.updateManageFilesButtonVisibility();
    }

    /**
     * Include a file in TTR Summary Report
     */
    includeFile(fileName) {
        if (!fileName) return;

        this.includedFiles.add(fileName);
        this.excludedFiles.delete(fileName);

        console.log(`TTR File Manager: File ${fileName} included in TTR Summary Report`);

        // Recalculate cumulative serial ranges
        this.calculateCumulativeSerialRanges();

        // Update UI and report
        this.updateFileManagementUI();
        this.updateTTRSummaryReport();

        // Show notification
        if (window.app && window.app.showNotification) {
            window.app.showNotification(`File "${fileName}" added to TTR Summary Report`, 'success');
        }
    }

    /**
     * Exclude a file from TTR Summary Report
     */
    excludeFile(fileName) {
        if (!fileName) return;

        this.includedFiles.delete(fileName);
        this.excludedFiles.add(fileName);

        console.log(`TTR File Manager: File ${fileName} excluded from TTR Summary Report`);

        // Recalculate cumulative serial ranges
        this.calculateCumulativeSerialRanges();

        // Update UI and report
        this.updateFileManagementUI();
        this.updateTTRSummaryReport();

        // Show notification
        if (window.app && window.app.showNotification) {
            window.app.showNotification(`File "${fileName}" removed from TTR Summary Report`, 'warning');
        }
    }

    /**
     * Get list of files included in TTR Summary Report (ordered by processing order)
     * Includes automatic file restoration
     */
    getIncludedFiles() {
        // AUTOMATIC RESTORATION: Always restore missing files when getting included files
        const restoredCount = this.restoreMissingFiles();
        if (restoredCount > 0) {
            console.log(`TTR: Auto-restored ${restoredCount} missing files in getIncludedFiles()`);
        }

        // Get all processed files from global file handler (after restoration)
        const allProcessedFiles = window.fileHandler?.processedData || [];

        console.log(`TTR: getIncludedFiles() called with ${allProcessedFiles.length} processed files`);
        console.log('TTR: Processed files:', allProcessedFiles.map(f => f.fileName));
        console.log('TTR: Current included files:', Array.from(this.includedFiles));
        console.log('TTR: Current excluded files:', Array.from(this.excludedFiles));

        // Only clean up state if there are actual inconsistencies
        const processedFileNames = new Set(allProcessedFiles.map(f => f.fileName));
        let needsCleanup = false;

        // Check if any included files are no longer processed
        for (const fileName of this.includedFiles) {
            if (!processedFileNames.has(fileName)) {
                needsCleanup = true;
                break;
            }
        }

        // Check if any excluded files are no longer processed
        if (!needsCleanup) {
            for (const fileName of this.excludedFiles) {
                if (!processedFileNames.has(fileName)) {
                    needsCleanup = true;
                    break;
                }
            }
        }

        // Only perform cleanup if needed
        if (needsCleanup) {
            const includedFilesToRemove = [];
            const excludedFilesToRemove = [];

            this.includedFiles.forEach(fileName => {
                if (!processedFileNames.has(fileName)) {
                    includedFilesToRemove.push(fileName);
                    console.log(`TTR: Removing ${fileName} from included files - no longer processed`);
                }
            });

            this.excludedFiles.forEach(fileName => {
                if (!processedFileNames.has(fileName)) {
                    excludedFilesToRemove.push(fileName);
                    console.log(`TTR: Removing ${fileName} from excluded files - no longer processed`);
                }
            });

            includedFilesToRemove.forEach(fileName => {
                this.includedFiles.delete(fileName);
            });

            excludedFilesToRemove.forEach(fileName => {
                this.excludedFiles.delete(fileName);
            });
        }

        // Ensure all processed files are included by default (unless explicitly excluded)
        let autoIncludedCount = 0;
        allProcessedFiles.forEach(fileData => {
            const fileName = fileData.fileName;
            // Auto-include files that aren't explicitly excluded
            if (!this.excludedFiles.has(fileName)) {
                if (!this.includedFiles.has(fileName)) {
                    this.includedFiles.add(fileName);
                    autoIncludedCount++;
                    console.log(`TTR: Auto-included ${fileName}`);
                }
            } else {
                console.log(`TTR: Skipping ${fileName} - explicitly excluded`);
            }
        });

        if (autoIncludedCount > 0) {
            console.log(`TTR: Auto-included ${autoIncludedCount} files`);
        }

        // Filter to only include files that are:
        // 1. In the included files set AND not explicitly excluded AND still in processed files
        const includedFiles = allProcessedFiles.filter(fileData => {
            const fileName = fileData.fileName;
            const isIncluded = this.includedFiles.has(fileName);
            const isExcluded = this.excludedFiles.has(fileName);
            const isProcessed = processedFileNames.has(fileName);
            const shouldInclude = isIncluded && !isExcluded && isProcessed;

            console.log(`TTR: File ${fileName} - included: ${isIncluded}, excluded: ${isExcluded}, processed: ${isProcessed}, shouldInclude: ${shouldInclude}`);

            return shouldInclude;
        });

        console.log(`TTR: getIncludedFiles() returning ${includedFiles.length} files:`, includedFiles.map(f => f.fileName));

        // Sort by processing order (maintain the order files were processed)
        // This ensures consistent cumulative serial number calculation
        return includedFiles.sort((a, b) => {
            const aIndex = allProcessedFiles.findIndex(f => f.fileName === a.fileName);
            const bIndex = allProcessedFiles.findIndex(f => f.fileName === b.fileName);
            return aIndex - bIndex;
        });
    }

    /**
     * Calculate cumulative serial ranges for all included files
     */
    calculateCumulativeSerialRanges() {
        const includedFiles = this.getIncludedFiles();

        console.log(`TTR: Calculating cumulative serial ranges for ${includedFiles.length} files`);

        // Reset cumulative counters
        this.cumulativeCounters = {
            hoc: 0,
            ibd: 0,
            wu: 0
        };

        // Clear existing ranges
        this.cumulativeSerialRanges.clear();

        // Calculate ranges for each file in order
        includedFiles.forEach((fileData, index) => {
            const fileName = fileData.fileName;
            const fileMetrics = window.fileHandler?.fileMetrics?.get(fileName) || {};

            // Get counts for this file
            const hocCount = fileMetrics.hocUniqueSerialCount || 0;
            const ibdUniqueCount = fileMetrics.ibdUniqueSerialCount || 0;
            const wuCount = fileMetrics.wuUniqueSerialCount || 0;

            // TTR Summary Report specific logic: IBD Count = IBD unique serials + WU unique serials
            const ibdCount = ibdUniqueCount + wuCount;

            console.log(`TTR: File ${index + 1} (${fileName}) - HOC: ${hocCount}, IBD: ${ibdCount} (${ibdUniqueCount}+${wuCount}), WU: ${wuCount}`);

            // Calculate ranges for this file
            const ranges = {
                hoc: this.calculateRangeForType('hoc', hocCount),
                ibd: this.calculateRangeForType('ibd', ibdCount),
                wu: this.calculateRangeForType('wu', wuCount)
            };

            // Store ranges for this file
            this.cumulativeSerialRanges.set(fileName, ranges);

            console.log(`TTR: Serial Ranges for ${fileName}:`, {
                hoc: ranges.hoc ? `${ranges.hoc.start}-${ranges.hoc.end}` : 'none',
                ibd: ranges.ibd ? `${ranges.ibd.start}-${ranges.ibd.end} (includes ${ibdUniqueCount} IBD + ${wuCount} WU)` : 'none',
                wu: ranges.wu ? `${ranges.wu.start}-${ranges.wu.end}` : 'none'
            });
        });

        console.log('TTR: Final cumulative counters:', this.cumulativeCounters);
    }

    /**
     * Calculate range for a specific transaction type
     */
    calculateRangeForType(type, count) {
        if (count === 0) {
            return null; // No range for zero count
        }

        const start = this.cumulativeCounters[type] + 1;
        const end = this.cumulativeCounters[type] + count;

        // Update cumulative counter
        this.cumulativeCounters[type] = end;

        return { start, end };
    }

    /**
     * Format serial range for display
     */
    formatSerialRange(type, range) {
        if (!range) {
            return '-';
        }

        const prefix = type === 'hoc' ? 'TTR ' : '';
        return `${prefix}(${range.start}-${range.end})`;
    }

    /**
     * Get serial range for a specific file and type
     */
    getSerialRangeForFile(fileName, type) {
        const ranges = this.cumulativeSerialRanges.get(fileName);
        if (!ranges || !ranges[type]) {
            return '-';
        }
        return this.formatSerialRange(type, ranges[type]);
    }

    /**
     * Show file management panel
     */
    showFileManagementPanel() {
        const panel = document.getElementById('ttrFilePanel');
        if (panel) {
            panel.style.display = 'block';
            this.updateFileManagementUI();
        }
    }

    /**
     * Hide file management panel
     */
    hideFileManagementPanel() {
        const panel = document.getElementById('ttrFilePanel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * Reset TTR state and sync with current processed files (with file restoration)
     */
    resetAndSyncState() {
        console.log('TTR File Manager: Resetting and syncing state...');

        // First, restore any missing files that have metrics but are not in processed data
        this.restoreMissingFiles();

        // Get current processed files after restoration
        const allProcessedFiles = window.fileHandler?.processedData || [];
        const processedFileNames = new Set(allProcessedFiles.map(f => f.fileName));
        console.log(`TTR File Manager: Found ${allProcessedFiles.length} processed files to sync:`, Array.from(processedFileNames));

        // Only clean up metrics for files that don't exist in uploaded files either
        if (window.fileHandler?.fileMetrics) {
            const uploadedFileNames = new Set((window.fileHandler?.files || []).map(f => f.name));
            const staleMetrics = [];

            window.fileHandler.fileMetrics.forEach((metrics, fileName) => {
                // Only remove metrics if file doesn't exist in processed data AND uploaded files
                if (!processedFileNames.has(fileName) && !uploadedFileNames.has(fileName)) {
                    staleMetrics.push(fileName);
                }
            });

            staleMetrics.forEach(fileName => {
                console.log(`TTR File Manager: Removing truly stale metrics for ${fileName}`);
                window.fileHandler.fileMetrics.delete(fileName);
            });

            if (staleMetrics.length > 0) {
                console.log(`TTR File Manager: Cleaned up ${staleMetrics.length} truly stale file metrics`);
            }
        }

        // Completely clear all TTR state - create new Sets to ensure complete reset
        console.log(`TTR File Manager: Clearing state - before: included=${this.includedFiles.size}, excluded=${this.excludedFiles.size}`);
        this.includedFiles = new Set();
        this.excludedFiles = new Set();
        this.cumulativeSerialRanges = new Map();
        this.cumulativeCounters = { hoc: 0, ibd: 0, wu: 0 };
        console.log(`TTR File Manager: State cleared - after: included=${this.includedFiles.size}, excluded=${this.excludedFiles.size}`);

        // Auto-include all processed files
        allProcessedFiles.forEach(fileData => {
            this.includedFiles.add(fileData.fileName);
            console.log(`TTR File Manager: Auto-included ${fileData.fileName}`);
        });

        console.log(`TTR File Manager: State reset complete. Included: ${this.includedFiles.size}, Excluded: ${this.excludedFiles.size}`);
        console.log(`TTR File Manager: Final included files:`, Array.from(this.includedFiles));
        console.log(`TTR File Manager: Final excluded files:`, Array.from(this.excludedFiles));

        // Recalculate everything (now safe since UI update won't corrupt state)
        this.calculateCumulativeSerialRanges();
        this.updateFileManagementUI();
        this.updateTTRSummaryReport();

        return {
            includedCount: this.includedFiles.size,
            excludedCount: this.excludedFiles.size,
            processedCount: allProcessedFiles.length,
            metricsCount: window.fileHandler?.fileMetrics?.size || 0
        };
    }

    /**
     * Restore missing files that have metrics but are not in processed data
     */
    restoreMissingFiles() {
        console.log('TTR File Manager: Checking for missing files to restore...');

        const uploadedFiles = window.fileHandler?.files || [];
        const processedData = window.fileHandler?.processedData || [];
        const fileMetrics = window.fileHandler?.fileMetrics || new Map();

        // Find files that have metrics but are missing from processedData
        const missingFiles = [];
        fileMetrics.forEach((metrics, fileName) => {
            const existsInProcessed = processedData.some(f => f.fileName === fileName);
            const existsInUploaded = uploadedFiles.some(f => f.name === fileName && !f.isRemoved);

            if (!existsInProcessed && existsInUploaded) {
                missingFiles.push(fileName);
            }
        });

        console.log(`TTR File Manager: Found ${missingFiles.length} missing files to restore:`, missingFiles);

        // Restore missing files to processedData
        missingFiles.forEach(fileName => {
            const uploadedFile = uploadedFiles.find(f => f.name === fileName);
            if (uploadedFile) {
                const restoredFile = {
                    fileName: fileName,
                    data: [], // Empty data array - metrics are preserved
                    source: 'restored',
                    uploadedFile: uploadedFile
                };
                window.fileHandler.processedData.push(restoredFile);
                console.log(`TTR File Manager: Restored ${fileName} to processed data`);
            }
        });

        return missingFiles.length;
    }

    /**
     * Get all available files (both uploaded and processed)
     */
    getAllAvailableFiles() {
        const allFiles = [];
        const fileNames = new Set();

        // Get uploaded files (may not be processed yet)
        const uploadedFiles = window.fileHandler?.files || [];
        uploadedFiles.forEach(file => {
            if (!file.isRemoved && !fileNames.has(file.name)) {
                allFiles.push({
                    fileName: file.name,
                    fileSize: file.size,
                    isProcessed: file.isAlreadyProcessed ||
                                (window.dataProcessor && window.dataProcessor.isFileProcessed(file)),
                    source: 'uploaded'
                });
                fileNames.add(file.name);
            }
        });

        // Get processed files (ensure we don't duplicate)
        const processedFiles = window.fileHandler?.processedData || [];
        processedFiles.forEach(fileData => {
            if (!fileNames.has(fileData.fileName)) {
                allFiles.push({
                    fileName: fileData.fileName,
                    fileSize: null, // Size not available in processedData
                    isProcessed: true,
                    source: 'processed'
                });
                fileNames.add(fileData.fileName);
            }
        });

        return allFiles;
    }

    /**
     * Update file management UI with automatic restoration
     */
    updateFileManagementUI() {
        const container = document.getElementById('ttrFileListContainer');
        if (!container) return;

        // AUTOMATIC RESTORATION: Restore missing files before updating UI
        const restoredCount = this.restoreMissingFiles();
        if (restoredCount > 0) {
            console.log(`TTR: Auto-restored ${restoredCount} missing files in updateFileManagementUI()`);
        }

        // Get all available files (both uploaded and processed) after restoration
        const allAvailableFiles = this.getAllAvailableFiles();

        if (allAvailableFiles.length === 0) {
            container.innerHTML = '<div class="ttr-empty-file-message">No files available. Please upload CSV files first.</div>';
            return;
        }

        // Clear container
        container.innerHTML = '';

        // Create file items (DO NOT auto-include files here - this corrupts state)
        allAvailableFiles.forEach(fileData => {
            const fileName = fileData.fileName;
            // Just check current state, don't modify it
            const isIncluded = this.includedFiles.has(fileName) && !this.excludedFiles.has(fileName);

            const fileItem = document.createElement('div');
            fileItem.className = 'ttr-file-item';

            // Add processing status indicator
            const processingStatus = fileData.isProcessed ? 'Processed' : 'Pending';
            const statusClass = fileData.isProcessed ? 'processed' : 'pending';

            fileItem.innerHTML = `
                <div class="ttr-file-name">
                    ${fileName}
                    <span class="ttr-file-processing-status ${statusClass}">(${processingStatus})</span>
                </div>
                <div class="ttr-file-status ${isIncluded ? 'included' : 'excluded'}">
                    ${isIncluded ? 'Included' : 'Excluded'}
                </div>
                <div class="ttr-file-actions">
                    ${isIncluded ?
                        `<button class="btn btn-warning ttr-exclude-btn" data-filename="${fileName}">Remove</button>` :
                        `<button class="btn btn-success ttr-include-btn" data-filename="${fileName}">Add</button>`
                    }
                </div>
            `;

            container.appendChild(fileItem);
        });

        // Add event listeners to action buttons
        container.querySelectorAll('.ttr-include-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const fileName = e.target.getAttribute('data-filename');
                this.includeFile(fileName);
            });
        });

        container.querySelectorAll('.ttr-exclude-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const fileName = e.target.getAttribute('data-filename');
                this.excludeFile(fileName);
            });
        });
    }

    /**
     * Update TTR Summary Report table with automatic restoration
     */
    updateTTRSummaryReport() {
        console.log('TTR File Manager: Updating TTR Summary Report table...');

        // AUTOMATIC RESTORATION: Restore missing files before updating report
        const restoredCount = this.restoreMissingFiles();
        if (restoredCount > 0) {
            console.log(`TTR: Auto-restored ${restoredCount} missing files in updateTTRSummaryReport()`);
        }

        // Ensure TTR section is visible
        this.ensureTTRSectionVisible();

        // Delegate to the data processor's TTR update method
        if (window.dataProcessor && typeof window.dataProcessor.updateTTRSummaryReport === 'function') {
            // Use a small delay to ensure UI updates are processed
            setTimeout(() => {
                console.log('TTR File Manager: Calling dataProcessor.updateTTRSummaryReport()');
                window.dataProcessor.updateTTRSummaryReport();

                // Double-check visibility after update
                setTimeout(() => {
                    this.ensureTTRSectionVisible();
                }, 200);
            }, 100);
        } else {
            console.warn('TTR File Manager: dataProcessor.updateTTRSummaryReport not available');
        }
    }

    /**
     * Ensure TTR section and table are visible
     */
    ensureTTRSectionVisible() {
        // Find TTR section
        const ttrSection = document.querySelector('.ttr-summary-section');
        if (ttrSection) {
            ttrSection.style.display = 'block';
            console.log('TTR File Manager: TTR section made visible');
        }

        // Find TTR table container
        const ttrTableContainer = document.querySelector('.ttr-summary-table-container');
        if (ttrTableContainer) {
            ttrTableContainer.style.display = 'block';
            console.log('TTR File Manager: TTR table container made visible');
        }

        // Find TTR table
        const ttrTable = document.getElementById('ttrSummaryTable');
        if (ttrTable) {
            ttrTable.style.display = 'table';
            console.log('TTR File Manager: TTR table made visible');
        }

        // Find TTR table body
        const ttrTableBody = document.getElementById('ttrSummaryTableBody');
        if (ttrTableBody) {
            ttrTableBody.style.display = 'table-row-group';
            console.log('TTR File Manager: TTR table body made visible');
        }
    }

    /**
     * Force update TTR Summary Report (for debugging)
     */
    forceUpdateTTRSummaryReport() {
        console.log('TTR File Manager: Force updating TTR Summary Report...');

        // Log current state
        const processedFiles = window.fileHandler?.processedData || [];
        const fileMetricsSize = window.fileHandler?.fileMetrics?.size || 0;
        const includedFilesSize = this.includedFiles.size;

        console.log(`TTR Debug State: processedFiles=${processedFiles.length}, fileMetrics=${fileMetricsSize}, includedFiles=${includedFilesSize}`);

        if (processedFiles.length > 0) {
            console.log('Processed files:', processedFiles.map(f => f.fileName));
        }

        if (this.includedFiles.size > 0) {
            console.log('Included files:', Array.from(this.includedFiles));
        }

        // Ensure visibility first
        this.ensureTTRSectionVisible();

        // Force recalculation
        this.calculateCumulativeSerialRanges();

        // Force table update
        if (window.dataProcessor && typeof window.dataProcessor.updateTTRSummaryReport === 'function') {
            window.dataProcessor.updateTTRSummaryReport();

            // Final visibility check
            setTimeout(() => {
                this.ensureTTRSectionVisible();
                console.log('TTR File Manager: Force update completed with visibility check');
            }, 300);
        }
    }

    /**
     * Update visibility of manage files button with automatic restoration
     */
    updateManageFilesButtonVisibility() {
        const manageBtn = document.getElementById('ttrManageFilesBtn');
        if (!manageBtn) return;

        // AUTOMATIC RESTORATION: Restore missing files before checking visibility
        const restoredCount = this.restoreMissingFiles();
        if (restoredCount > 0) {
            console.log(`TTR: Auto-restored ${restoredCount} missing files in updateManageFilesButtonVisibility()`);
        }

        // Check for both uploaded and processed files (after restoration)
        const hasUploadedFiles = (window.fileHandler?.files?.length > 0) &&
                                window.fileHandler.files.some(file => !file.isRemoved);
        const hasProcessedFiles = window.fileHandler?.processedData?.length > 0;
        const hasFiles = hasUploadedFiles || hasProcessedFiles;

        manageBtn.style.display = hasFiles ? 'inline-block' : 'none';
    }

    /**
     * Initialize TTR File Manager after DOM and other components are ready
     */
    initialize() {
        console.log('TTR File Manager: Initializing...');

        // First, restore any missing files
        const restoredCount = this.restoreMissingFiles();
        if (restoredCount > 0) {
            console.log(`TTR File Manager: Restored ${restoredCount} missing files during initialization`);
        }

        // Check if we have any existing processed files and update UI accordingly
        this.updateManageFilesButtonVisibility();

        // If there are already processed files, include them by default
        const processedFiles = window.fileHandler?.processedData || [];
        if (processedFiles.length > 0) {
            processedFiles.forEach(fileData => {
                if (!this.excludedFiles.has(fileData.fileName)) {
                    this.includedFiles.add(fileData.fileName);
                }
            });
            console.log(`TTR File Manager: Initialized with ${this.includedFiles.size} existing files`);
            console.log('TTR File Manager: Included files:', Array.from(this.includedFiles));

            // Calculate cumulative serial ranges for existing files
            this.calculateCumulativeSerialRanges();

            // Update TTR table if files are available
            this.updateTTRSummaryReport();
        }

        console.log('TTR File Manager: Initialization complete');

        // Set up periodic restoration check (every 30 seconds)
        this.setupPeriodicRestoration();
    }

    /**
     * Set up periodic restoration to ensure files are never lost
     */
    setupPeriodicRestoration() {
        // Clear any existing interval
        if (this.restorationInterval) {
            clearInterval(this.restorationInterval);
        }

        // Set up new interval for periodic restoration
        this.restorationInterval = setInterval(() => {
            const restoredCount = this.restoreMissingFiles();
            if (restoredCount > 0) {
                console.log(`TTR: Periodic restoration found and restored ${restoredCount} missing files`);
                // Update UI if files were restored
                this.updateManageFilesButtonVisibility();
                this.updateTTRSummaryReport();
            }
        }, 30000); // Check every 30 seconds

        console.log('TTR File Manager: Periodic restoration check enabled (every 30 seconds)');
    }

    /**
     * Force sync all processed files to included files (for debugging/fixing)
     */
    syncProcessedFiles() {
        const processedFiles = window.fileHandler?.processedData || [];
        let addedCount = 0;

        console.log(`TTR File Manager: Syncing ${processedFiles.length} processed files...`);
        console.log('TTR File Manager: Current included files:', Array.from(this.includedFiles));
        console.log('TTR File Manager: Current excluded files:', Array.from(this.excludedFiles));

        processedFiles.forEach(fileData => {
            if (!this.excludedFiles.has(fileData.fileName) && !this.includedFiles.has(fileData.fileName)) {
                this.includedFiles.add(fileData.fileName);
                addedCount++;
                console.log(`TTR File Manager: Added ${fileData.fileName} to included files`);
            }
        });

        console.log(`TTR File Manager: Synced ${addedCount} processed files to included files`);
        console.log(`TTR File Manager: Total included files: ${this.includedFiles.size}`);

        if (addedCount > 0) {
            this.calculateCumulativeSerialRanges();
            this.updateTTRSummaryReport();
        }

        return addedCount;
    }

    /**
     * Remove a file from TTR table row (called from table row remove buttons)
     */
    removeFileFromTable(fileName) {
        if (!fileName) {
            console.warn('TTR File Manager: No filename provided for table removal');
            return;
        }

        console.log(`TTR File Manager: Removing file ${fileName} from table`);

        // Use enhanced confirmation dialog
        const confirmed = confirm(
            `Remove "${fileName}" from TTR Summary Report?\n\n` +
            'This will remove the file from the TTR Summary Report table and recalculate serial number ranges. ' +
            'The file will remain available in the file management panel and can be re-added later.'
        );

        if (confirmed) {
            // Exclude the file and update everything
            this.excludeFileWithRecalculation(fileName);
        }
    }

    /**
     * Exclude file with full recalculation and state management
     */
    excludeFileWithRecalculation(fileName) {
        if (!fileName) return;

        console.log(`TTR File Manager: Excluding file ${fileName} with recalculation`);

        // Update state
        this.includedFiles.delete(fileName);
        this.excludedFiles.add(fileName);

        // Clear any cached ranges for this file
        this.cumulativeSerialRanges.delete(fileName);

        console.log(`TTR File Manager: File ${fileName} excluded from TTR Summary Report`);
        console.log(`TTR File Manager: Current included files:`, Array.from(this.includedFiles));
        console.log(`TTR File Manager: Current excluded files:`, Array.from(this.excludedFiles));

        // Recalculate cumulative serial ranges for remaining files
        this.calculateCumulativeSerialRanges();

        // Update UI components
        this.updateFileManagementUI();
        this.updateTTRSummaryReport();

        // Show notification
        if (window.app && window.app.showNotification) {
            window.app.showNotification(`File "${fileName}" removed from TTR Summary Report`, 'warning');
        }
    }
}

// Initialize TTR File Manager when script loads
window.ttrFileManager = new TTRFileManager();

// Initialize after a short delay to ensure other components are ready
setTimeout(() => {
    if (window.ttrFileManager && typeof window.ttrFileManager.initialize === 'function') {
        window.ttrFileManager.initialize();
    }
}, 1000);

// Global debugging function for TTR table updates
window.debugTTRTable = function() {
    console.log('=== TTR Table Debug ===');
    if (window.ttrFileManager) {
        window.ttrFileManager.forceUpdateTTRSummaryReport();
    } else {
        console.error('TTR File Manager not available');
    }
};

// Global function to force refresh TTR table (for testing)
window.refreshTTRTable = function() {
    console.log('=== Force Refresh TTR Table ===');
    if (window.ttrFileManager) {
        // Force recalculation and update
        window.ttrFileManager.calculateCumulativeSerialRanges();
        window.ttrFileManager.updateTTRSummaryReport();
        console.log('TTR table refresh completed');
    } else {
        console.error('TTR File Manager not available');
    }
};

// Global function to fix TTR file inclusion issues
window.fixTTRFiles = function() {
    console.log('=== Fixing TTR File Inclusion Issues ===');

    if (!window.ttrFileManager) {
        console.error('TTR File Manager not available');
        return;
    }

    // Clear excluded files first
    window.ttrFileManager.excludedFiles.clear();
    console.log('Cleared all excluded files');

    // Sync all processed files
    const addedCount = window.ttrFileManager.syncProcessedFiles();
    console.log(`Fixed: Added ${addedCount} missing files to TTR Summary Report`);

    // Force update
    window.ttrFileManager.forceUpdateTTRSummaryReport();

    return addedCount;
};

// Global function to test TTR inclusion logic
window.testTTRInclusion = function() {
    console.log('=== Testing TTR Inclusion Logic ===');

    if (!window.ttrFileManager) {
        console.error('TTR File Manager not available');
        return;
    }

    // Get current state
    const processedFiles = window.fileHandler?.processedData || [];
    const includedFiles = Array.from(window.ttrFileManager.includedFiles);
    const excludedFiles = Array.from(window.ttrFileManager.excludedFiles);

    console.log(`Processed files (${processedFiles.length}):`, processedFiles.map(f => f.fileName));
    console.log(`Included files (${includedFiles.length}):`, includedFiles);
    console.log(`Excluded files (${excludedFiles.length}):`, excludedFiles);

    // Test getIncludedFiles
    console.log('--- Testing getIncludedFiles() ---');
    const result = window.ttrFileManager.getIncludedFiles();
    console.log(`getIncludedFiles() returned ${result.length} files:`, result.map(f => f.fileName));

    return {
        processedFiles: processedFiles.length,
        includedFiles: includedFiles.length,
        excludedFiles: excludedFiles.length,
        resultFiles: result.length
    };
};

// Global function to debug TTR state (for testing)
window.debugTTRState = function() {
    console.log('=== TTR State Debug ===');

    if (!window.ttrFileManager) {
        console.error('TTR File Manager not available');
        return;
    }

    const processedData = window.fileHandler?.processedData || [];
    const fileMetrics = window.fileHandler?.fileMetrics || new Map();
    const includedFiles = window.ttrFileManager.includedFiles;
    const excludedFiles = window.ttrFileManager.excludedFiles;

    console.log(`Processed files: ${processedData.length}`);
    console.log('Processed files list:', processedData.map(f => f.fileName));

    console.log(`File metrics: ${fileMetrics.size}`);
    console.log('File metrics keys:', Array.from(fileMetrics.keys()));

    console.log(`Included files: ${includedFiles.size}`);
    console.log('Included files list:', Array.from(includedFiles));

    console.log(`Excluded files: ${excludedFiles.size}`);
    console.log('Excluded files list:', Array.from(excludedFiles));

    // DO NOT call getIncludedFiles() as it modifies state during cleanup
    console.log('Note: getIncludedFiles() not called to avoid state modification');

    console.log('=== End TTR State Debug ===');
};

// Global function to sync processed files to TTR included files (for fixing)
window.syncTTRFiles = function() {
    console.log('=== Sync TTR Files ===');
    if (window.ttrFileManager) {
        const addedCount = window.ttrFileManager.syncProcessedFiles();
        console.log(`Synced ${addedCount} files to TTR included files`);

        // Also run debug to show current state
        window.debugTTRState();
    } else {
        console.error('TTR File Manager not available');
    }
};

// Global function to reset TTR state completely (for fixing state issues)
window.resetTTRState = function() {
    console.log('=== Reset TTR State ===');
    if (window.ttrFileManager) {
        const result = window.ttrFileManager.resetAndSyncState();
        console.log(`TTR state reset completed:`, result);

        // Show current state after reset (without calling getIncludedFiles)
        console.log('=== Final State ===');
        console.log('Processed files:', (window.fileHandler?.processedData || []).map(f => f.fileName));
        console.log('Included files:', Array.from(window.ttrFileManager.includedFiles));
        console.log('Excluded files:', Array.from(window.ttrFileManager.excludedFiles));
        console.log('File metrics:', Array.from(window.fileHandler?.fileMetrics?.keys() || []));

        return result;
    } else {
        console.error('TTR File Manager not available');
        return null;
    }
};

// Emergency function to restore missing files
window.restoreAllFiles = function() {
    console.log('=== Restoring All Files ===');

    // Check uploaded files that might have been processed but lost from processedData
    const uploadedFiles = window.fileHandler?.files || [];
    const processedData = window.fileHandler?.processedData || [];
    const fileMetrics = window.fileHandler?.fileMetrics || new Map();

    console.log('Uploaded files:', uploadedFiles.map(f => ({name: f.name, removed: f.isRemoved, processed: f.isAlreadyProcessed})));
    console.log('Processed data:', processedData.map(f => f.fileName));
    console.log('File metrics:', Array.from(fileMetrics.keys()));

    // Find files that have metrics but are missing from processedData
    const missingFiles = [];
    fileMetrics.forEach((metrics, fileName) => {
        const existsInProcessed = processedData.some(f => f.fileName === fileName);
        if (!existsInProcessed) {
            missingFiles.push(fileName);
        }
    });

    console.log('Missing files found:', missingFiles);

    // Restore missing files to processedData
    missingFiles.forEach(fileName => {
        const uploadedFile = uploadedFiles.find(f => f.name === fileName);
        if (uploadedFile) {
            const restoredFile = {
                fileName: fileName,
                data: [], // Empty data array
                source: 'restored'
            };
            window.fileHandler.processedData.push(restoredFile);
            console.log(`Restored ${fileName} to processed data`);
        }
    });

    // Reset TTR state with restored files
    if (window.ttrFileManager) {
        window.ttrFileManager.resetAndSyncState();
    }

    return {
        restoredFiles: missingFiles.length,
        totalProcessed: window.fileHandler?.processedData?.length || 0
    };
};
