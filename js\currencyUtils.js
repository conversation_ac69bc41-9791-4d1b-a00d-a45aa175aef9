/**
 * Currency Utilities
 * Centralized currency formatting, validation, and conversion utilities
 */

class CurrencyUtils {
    constructor() {
        // Get currency configuration from constants
        this.currencyConfig = window.FIELD_MAPPINGS?.CURRENCY_CONFIG || {};
        this.supportedCurrencies = window.FIELD_MAPPINGS?.SUPPORTED_CURRENCIES || 
            ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
    }

    /**
     * Format currency amount with proper symbol and decimal places
     * @param {number} amount - The amount to format
     * @param {string} currency - Currency code (e.g., 'USD', 'MMK')
     * @returns {string} Formatted currency string
     */
    formatCurrency(amount, currency = 'MMK') {
        if (isNaN(amount) || amount === null || amount === undefined) {
            amount = 0;
        }

        const config = this.currencyConfig[currency];
        if (!config) {
            // Fallback for unsupported currencies
            return `${this.formatNumber(amount)} ${currency}`;
        }

        if (config.useIntlFormat) {
            try {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: currency,
                    minimumFractionDigits: config.decimals,
                    maximumFractionDigits: config.decimals
                }).format(amount);
            } catch (e) {
                // Fallback if Intl formatting fails
                return `${config.symbol}${this.formatNumber(amount, config.decimals)}`;
            }
        } else {
            // Custom formatting for MMK and other non-Intl currencies
            return `${this.formatNumber(amount, config.decimals)} ${config.symbol}`;
        }
    }

    /**
     * Format number with proper decimal places and thousand separators
     * @param {number} number - Number to format
     * @param {number} decimals - Number of decimal places
     * @returns {string} Formatted number string
     */
    formatNumber(number, decimals = 0) {
        if (isNaN(number) || number === null || number === undefined) {
            return '0';
        }

        return Number(number).toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }

    /**
     * Validate currency code
     * @param {string} currency - Currency code to validate
     * @returns {boolean} True if currency is supported
     */
    isValidCurrency(currency) {
        return this.supportedCurrencies.includes(currency);
    }

    /**
     * Get currency configuration
     * @param {string} currency - Currency code
     * @returns {object} Currency configuration object
     */
    getCurrencyConfig(currency) {
        return this.currencyConfig[currency] || null;
    }

    /**
     * Check if amount exceeds high-value threshold for currency
     * @param {number} amount - Transaction amount
     * @param {string} currency - Currency code
     * @returns {boolean} True if amount exceeds threshold
     */
    isHighValueTransaction(amount, currency) {
        const config = this.currencyConfig[currency];
        if (!config) return false;
        
        return amount >= config.highValueThreshold;
    }

    /**
     * Get high-value threshold for currency
     * @param {string} currency - Currency code
     * @returns {number} High-value threshold amount
     */
    getHighValueThreshold(currency) {
        const config = this.currencyConfig[currency];
        return config ? config.highValueThreshold : 0;
    }

    /**
     * Initialize currency metrics structure for all supported currencies
     * @returns {object} Initialized metrics object
     */
    initializeCurrencyMetrics() {
        const metrics = {
            currencyCounts: {},
            currencyAmounts: {},
            currencyCreditAmounts: {},
            currencyDebitAmounts: {},
            highValueTransactionCounts: {}
        };

        this.supportedCurrencies.forEach(currency => {
            metrics.currencyCounts[currency] = 0;
            metrics.currencyAmounts[currency] = 0;
            metrics.currencyCreditAmounts[currency] = 0;
            metrics.currencyDebitAmounts[currency] = 0;
            metrics.highValueTransactionCounts[currency] = 0;
        });

        return metrics;
    }

    /**
     * Update currency metrics with transaction data
     * @param {object} metrics - Metrics object to update
     * @param {object} transaction - Transaction data
     */
    updateCurrencyMetrics(metrics, transaction) {
        const currency = transaction.TRANSACTION_CURRENCY || 'MMK';
        const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
        const isCredit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
        const isDebit = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';

        // Validate currency and default to MMK if invalid
        const validCurrency = this.isValidCurrency(currency) ? currency : 'MMK';

        // Update counts and amounts
        metrics.currencyCounts[validCurrency] = (metrics.currencyCounts[validCurrency] || 0) + 1;
        metrics.currencyAmounts[validCurrency] = (metrics.currencyAmounts[validCurrency] || 0) + amount;

        // Update credit/debit amounts
        if (isCredit) {
            metrics.currencyCreditAmounts[validCurrency] = (metrics.currencyCreditAmounts[validCurrency] || 0) + amount;
        } else if (isDebit) {
            metrics.currencyDebitAmounts[validCurrency] = (metrics.currencyDebitAmounts[validCurrency] || 0) + amount;
        }

        // Check for high-value transactions
        if (this.isHighValueTransaction(amount, validCurrency)) {
            metrics.highValueTransactionCounts[validCurrency] = (metrics.highValueTransactionCounts[validCurrency] || 0) + 1;
        }
    }

    /**
     * Get all supported currencies with their configurations
     * @returns {array} Array of currency objects
     */
    getAllCurrencies() {
        return this.supportedCurrencies.map(code => ({
            code,
            ...this.currencyConfig[code]
        }));
    }

    /**
     * Normalize currency code (uppercase and validate)
     * @param {string} currency - Currency code to normalize
     * @returns {string} Normalized currency code
     */
    normalizeCurrency(currency) {
        if (!currency || typeof currency !== 'string') {
            return 'MMK'; // Default fallback
        }
        
        const normalized = currency.toUpperCase();
        return this.isValidCurrency(normalized) ? normalized : 'MMK';
    }

    /**
     * Get currency symbol
     * @param {string} currency - Currency code
     * @returns {string} Currency symbol
     */
    getCurrencySymbol(currency) {
        const config = this.currencyConfig[currency];
        return config ? config.symbol : currency;
    }

    /**
     * Get currency name
     * @param {string} currency - Currency code
     * @returns {string} Currency name
     */
    getCurrencyName(currency) {
        const config = this.currencyConfig[currency];
        return config ? config.name : currency;
    }

    /**
     * Format amount for display in tables (shorter format)
     * @param {number} amount - Amount to format
     * @param {string} currency - Currency code
     * @returns {string} Formatted amount for table display
     */
    formatForTable(amount, currency = 'MMK') {
        if (isNaN(amount) || amount === null || amount === undefined || amount === 0) {
            return '-';
        }

        const config = this.currencyConfig[currency];
        if (!config) {
            return this.formatNumber(amount) + ' ' + currency;
        }

        // For large amounts, use abbreviated format
        if (amount >= 1000000) {
            const abbreviated = amount / 1000000;
            return `${this.formatNumber(abbreviated, 1)}M ${config.symbol}`;
        } else if (amount >= 1000) {
            const abbreviated = amount / 1000;
            return `${this.formatNumber(abbreviated, 1)}K ${config.symbol}`;
        } else {
            return `${this.formatNumber(amount, config.decimals)} ${config.symbol}`;
        }
    }
}

// Create global instance
window.CurrencyUtils = new CurrencyUtils();
