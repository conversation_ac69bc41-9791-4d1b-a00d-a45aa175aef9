/**
 * Test script for Customer Analytics with existing CSV data
 * Run this in the browser console after loading the main application
 */

// Test function to verify customer analytics functionality
function testCustomerAnalyticsWithData() {
    console.log('=== Testing Customer Analytics with Existing Data ===');
    
    // Check if customer analytics is available
    if (!window.customerAnalytics) {
        console.error('❌ Customer Analytics not available');
        return false;
    }
    
    // Check initialization
    if (!window.customerAnalytics.isInitialized) {
        console.log('🔄 Initializing Customer Analytics...');
        window.customerAnalytics.initialize();
    }
    
    // Check if data processor has data
    if (!window.dataProcessor || !window.dataProcessor.rawData) {
        console.error('❌ Data Processor not available or no data loaded');
        console.log('💡 Please upload a CSV file first');
        return false;
    }
    
    const rawData = window.dataProcessor.rawData;
    console.log(`📊 Found ${rawData.length} transactions in data processor`);
    
    // Process the data through customer analytics
    console.log('🔄 Processing data through Customer Analytics...');
    window.customerAnalytics.processTransactionData(rawData);
    
    // Check results
    const customerCount = window.customerAnalytics.customerData.size;
    const alertCount = window.customerAnalytics.alertCustomers.size;
    
    console.log(`👥 Processed ${customerCount} unique customers`);
    console.log(`🚨 Found ${alertCount} alert customers`);
    
    // Test filter functionality
    console.log('🔄 Testing filter functionality...');
    testFilters();
    
    // Show sample data
    showSampleCustomerData();
    
    // Test modal functionality if there are alerts
    if (alertCount > 0) {
        console.log('🔄 Testing modal functionality...');
        testModalWithRealData();
    }
    
    return true;
}

// Test filter functionality
function testFilters() {
    const filters = ['alert', 'mmk-alert', 'usd-alert'];
    
    filters.forEach(filterValue => {
        console.log(`🔍 Testing filter: ${filterValue}`);
        
        if (window.customerAnalytics.elements.customerAlertFilter) {
            window.customerAnalytics.elements.customerAlertFilter.value = filterValue;
            window.customerAnalytics.elements.customerAlertFilter.dispatchEvent(new Event('change'));
            
            // Get filtered data count
            const filteredData = window.customerAnalytics.getFilteredAndSortedData();
            console.log(`   📊 ${filteredData.length} customers match filter`);
        } else {
            console.warn(`   ⚠️ Filter element not found`);
        }
    });
}

// Show sample customer data
function showSampleCustomerData() {
    console.log('📋 Sample Customer Data:');
    
    let count = 0;
    for (const [customerKey, customerDates] of window.customerAnalytics.customerData) {
        if (count >= 5) break; // Show only first 5
        
        const [customerName, customerId] = customerKey.split('|');
        console.log(`   👤 ${customerName} (${customerId})`);
        
        for (const [date, dateData] of customerDates) {
            const alertStatus = window.customerAnalytics.getAlertStatus(dateData);
            console.log(`      📅 ${date}: MMK=${dateData.mmk.toLocaleString()}, USD=${dateData.usd.toLocaleString()}, Count=${dateData.count}, Alert=${alertStatus}`);
        }
        count++;
    }
}

// Test modal with real data
function testModalWithRealData() {
    // Find first alert customer
    for (const [customerKey, customerDates] of window.customerAnalytics.customerData) {
        for (const [date, dateData] of customerDates) {
            const alertStatus = window.customerAnalytics.getAlertStatus(dateData);
            if (alertStatus !== 'no-alert') {
                const [customerName, customerId] = customerKey.split('|');
                console.log(`🔄 Testing modal with: ${customerName} on ${date}`);
                
                // Test the view details functionality
                window.customerAnalytics.viewCustomerDetails(customerName, customerId, date);
                return;
            }
        }
    }
    
    console.log('⚠️ No alert customers found for modal testing');
}

// Quick test function
function quickTest() {
    console.log('🚀 Quick Customer Analytics Test');
    
    // Check availability
    console.log('Customer Analytics available:', !!window.customerAnalytics);
    console.log('Data Processor available:', !!window.dataProcessor);
    console.log('Raw data count:', window.dataProcessor?.rawData?.length || 0);
    
    // Run debug
    if (window.customerAnalytics) {
        window.customerAnalytics.debugCustomerAnalytics();
    }
}

// Auto-run test if data is available
function autoTest() {
    setTimeout(() => {
        if (window.dataProcessor && window.dataProcessor.rawData && window.dataProcessor.rawData.length > 0) {
            console.log('🎯 Auto-running Customer Analytics test with available data...');
            testCustomerAnalyticsWithData();
        } else {
            console.log('⏳ No data available yet. Upload a CSV file and run testCustomerAnalyticsWithData()');
        }
    }, 2000);
}

// Export functions to global scope
window.testCustomerAnalyticsWithData = testCustomerAnalyticsWithData;
window.quickTestCA = quickTest;
window.autoTestCA = autoTest;

// Instructions
console.log(`
🔧 Customer Analytics Test Functions Available:

1. quickTestCA() - Quick status check
2. testCustomerAnalyticsWithData() - Full test with loaded data
3. autoTestCA() - Auto-test after delay (runs automatically)

📝 Instructions:
1. Load a CSV file in the main application
2. Run testCustomerAnalyticsWithData() in console
3. Check the Customer Analytics section in the UI
4. Test the filter controls and View Details buttons

🐛 If issues occur, check console for detailed logs
`);

// Auto-run the test
autoTest();
