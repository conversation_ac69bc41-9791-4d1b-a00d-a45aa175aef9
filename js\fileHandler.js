/**
 * File Handler Module
 * Handles file uploads, chunked processing, and CSV parsing
 */

// FileHandler class to manage file uploads and processing
class FileHandler {
    constructor() {
        this.files = [];
        this.processedData = [];
        this.totalSize = 0;
        this.processedSize = 0;
        this.chunkSize = 64 * 1024; // Reduced to 64KB chunks for better memory management
        this.isProcessing = false;
        this.worker = null;

        // Sets to track unique serial numbers
        this.hocSerialNumbers = new Set();
        this.ibdSerialNumbers = new Set();
        this.wuSerialNumbers = new Set();
        this.allSerialNumbers = new Set();

        // Map to track metrics per file
        this.fileMetrics = new Map();

        // Map to track serial numbers per file
        this.fileSerialNumbers = new Map();

        // Map to track date-based data per file for table synchronization
        this.fileDateData = new Map();

        // Streaming configuration
        this.streamingConfig = {
            maxMemoryUsage: 100 * 1024 * 1024, // 100MB memory limit
            gcThreshold: 0.8, // Trigger GC at 80% memory usage
            batchSize: 1000, // Process records in batches of 1000
            sampleRate: 0.001 // Keep 0.1% of records as samples
        };

        // Map to store actual transaction data per file for TTR analysis
        this.fileTransactionData = new Map();

        // ADD THIS LINE: Set to track uploaded filenames in the current session
        this.uploadedFileNamesInSession = new Set();

        // Initialization tracking
        this.eventListenersInitialized = false;
        this.boundEventHandlers = null;

        // Initialize web worker
        this.initWorker();

        // Initialize event listeners
        this.initEventListeners();

        // Security configuration
        this.securityConfig = {
            maxFileSize: 500 * 1024 * 1024, // 500MB per file
            maxTotalSize: 3 * 1024 * 1024 * 1024, // 3GB total
            allowedMimeTypes: ['text/csv', 'application/csv', 'text/plain'],
            allowedExtensions: ['.csv'],
            maxFilenameLength: 255,
            dangerousPatterns: [
                /\.\./,  // Path traversal
                /[<>:"|?*]/,  // Invalid filename characters
                /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Windows reserved names
                /^\./,  // Hidden files
                /\.(exe|bat|cmd|scr|pif|com|dll|vbs|js|jar|app|deb|dmg|pkg|rpm)$/i  // Executable extensions
            ]
        };
    }

    // Initialize web worker for background processing with robust path detection
    initWorker() {
        // Check if Web Workers are supported
        if (!window.Worker) {
            console.log('Web Workers not supported, using main thread processing');
            this.worker = null;
            return;
        }

        try {
            // Determine the correct worker path based on current location
            const workerPath = this.getWorkerPath();

            if (!workerPath) {
                console.log('Unable to determine worker path, using main thread processing');
                this.worker = null;
                return;
            }

            console.log(`Attempting to initialize worker with path: ${workerPath}`);

            // Create worker with the determined path
            this.worker = new Worker(workerPath);

            // Set up message handler using centralized handler
            this.worker.onmessage = (e) => {
                this.handleWorkerResult(e.data);
            };

            // Add error handler
            this.worker.onerror = (error) => {
                console.log('Worker encountered an error, falling back to main thread processing');
                // Disable worker on error and fall back to main thread
                this.worker = null;
            };

            console.log('CSV processing worker initialized successfully');
        } catch (error) {
            console.log('Worker initialization failed, using main thread processing');
            this.worker = null;
        }
    }

    // Determine the correct path for the worker file
    getWorkerPath() {
        const currentLocation = window.location;

        // For file:// protocol, use workers directory path
        if (currentLocation.protocol === 'file:') {
            return 'workers/csvWorker.js';
        }

        // For http/https protocols, determine the best path based on context
        const pathname = currentLocation.pathname;
        const isInSubdirectory = pathname.includes('/') && !pathname.endsWith('/');

        // If we're in a subdirectory (like /app/index.html), use relative path
        if (isInSubdirectory) {
            return './workers/csvWorker.js';
        }

        // If we're at root level, use workers directory path
        return 'workers/csvWorker.js';
    }

    // Main thread fallback processing functions (mirror worker functionality)
    processChunkMainThread(chunk, header, isFirstChunk) {
        try {
            // Input validation
            if (!chunk || typeof chunk !== 'string') {
                throw new Error('Invalid chunk: chunk must be a non-empty string');
            }

            if (!header || !Array.isArray(header)) {
                throw new Error('Invalid header: header must be a non-empty array');
            }

            // Split the chunk into lines
            const lines = chunk.split('\n');
            const processedLines = [];
            let startIndex = isFirstChunk ? 1 : 0; // Skip header if this is the first chunk

            // Create field index map for faster lookups
            const fieldIndexMap = new Map();
            const requiredFields = ['REPORTTYPE', 'TRANSACTION_AMOUNT', 'TRANSACTION_CURRENCY',
                                   'ACCOUNT_HOLDER_ACCOUNT_ROLE', 'TRANSACTION_DATE', 'SERIAL_NO',
                                   'CUSTOMER_NAME', 'PARTICIPANT_NAME_CONDUCTOR', 'PARTICIPANT_NAME_COUNTERPARTY',
                                   'PARTICIPANT_ID_NUMBER_CONDUCTOR', 'PARTICIPANT_ID_NUMBER_COUNTERPARTY',
                                   'TRANSACTION_ID', 'CUSTOMER_ID', 'ACCOUNT_NUMBER', 'TRANSACTION_TYPE',
                                   'CHANNEL', 'LOCATION', 'BUSINESS_TYPE'];

            requiredFields.forEach(field => {
                const index = header.indexOf(field);
                if (index !== -1) {
                    fieldIndexMap.set(field, index);
                }
            });

            for (let i = startIndex; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line === '') continue;

                const rowData = this.parseCSVLine(line);

                // Only process rows that have enough data
                if (rowData.length >= header.length) {
                    const rowObject = {};
                    let hasReportType = false;
                    let isValidRow = true;

                    for (const [field, index] of fieldIndexMap) {
                        const value = rowData[index] || '';

                        // Basic data validation
                        if (field === 'TRANSACTION_AMOUNT') {
                            const amount = parseFloat(value);
                            if (isNaN(amount) || amount < 0) {
                                isValidRow = false;
                                break;
                            }
                            rowObject[field] = amount;
                        } else if (field === 'TRANSACTION_CURRENCY') {
                            // Use the supported currencies from constants
                            const supportedCurrencies = window.FIELD_MAPPINGS?.SUPPORTED_CURRENCIES ||
                                ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

                            // Only default to MMK if the value is truly invalid or empty
                            if (!value || value.trim() === '') {
                                rowObject[field] = 'MMK';
                                console.log(`Currency validation (main thread): Empty value, defaulting to MMK`);
                            } else {
                                const upperCurrency = value.toUpperCase().trim();
                                if (!supportedCurrencies.includes(upperCurrency)) {
                                    console.warn(`Currency validation (main thread): Invalid currency "${value}" (normalized: "${upperCurrency}"), defaulting to MMK`);
                                    rowObject[field] = 'MMK';
                                } else {
                                    rowObject[field] = upperCurrency;
                                    // Debug log for specific currencies
                                    if (upperCurrency === 'CNY') {
                                        console.log(`✅ CNY currency detected and preserved: ${value} -> ${upperCurrency}`);
                                    } else if (upperCurrency === 'JPY') {
                                        console.log(`✅ JPY currency detected and preserved: ${value} -> ${upperCurrency}`);
                                    }
                                }
                            }
                        } else {
                            rowObject[field] = value;
                        }

                        if (field === 'REPORTTYPE' && value) {
                            hasReportType = true;
                        }
                    }

                    // Only add rows that have a REPORTTYPE value and are valid
                    if (hasReportType && isValidRow) {
                        processedLines.push(rowObject);
                    }
                }
            }

            return {
                processedLines: processedLines,
                metrics: this.calculateMetricsMainThread(processedLines),
                dateData: this.aggregateByDateMainThread(processedLines)
            };
        } catch (error) {
            console.error('Main thread chunk processing error:', error);
            throw error;
        }
    }

    // Main thread metrics calculation (simplified version of worker function)
    calculateMetricsMainThread(lines) {
        const metrics = {
            totalTransactions: 0,
            totalAmount: 0,
            hocCount: 0,
            hocAmount: 0,
            hocAmountMMK: 0,
            hocAmountUSD: 0,
            ibdCount: 0,
            ibdAmount: 0,
            ibdAmountMMK: 0,
            ibdAmountUSD: 0,
            wuCount: 0,
            wuAmount: 0,
            wuAmountMMK: 0,
            wuAmountUSD: 0,
            currencyCounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyCreditAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            currencyDebitAmounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            // High-value transaction counts by currency
            highValueTransactionCounts: {
                MMK: 0, USD: 0, SGD: 0, EUR: 0, JPY: 0, CNY: 0, THB: 0, INR: 0
            },
            highValueTransactionCount: 0,
            highValueTransactionCountUSD: 0
        };

        const HIGH_VALUE_THRESHOLD = 1000000000; // 1B MMK
        const HIGH_VALUE_THRESHOLD_USD = 10000; // 10K USD

        for (const row of lines) {
            const amount = typeof row.TRANSACTION_AMOUNT === 'string' ?
                parseFloat(row.TRANSACTION_AMOUNT) || 0 :
                (typeof row.TRANSACTION_AMOUNT === 'number' ? row.TRANSACTION_AMOUNT : 0);

            metrics.totalTransactions++;
            metrics.totalAmount += amount;

            // Get currency, only default to MMK if truly empty
            let currency = row.TRANSACTION_CURRENCY;
            if (!currency || currency.trim() === '') {
                currency = 'MMK';
            } else {
                currency = currency.toUpperCase();
            }

            const reportType = row.REPORTTYPE;
            const isCredit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
            const isDebit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';

            // Check for high-value transactions
            if (currency === 'MMK' && amount >= HIGH_VALUE_THRESHOLD) {
                metrics.highValueTransactionCount++;
            } else if (currency === 'USD' && amount >= HIGH_VALUE_THRESHOLD_USD) {
                metrics.highValueTransactionCountUSD++;
            }

            // Update currency counts for all supported currencies
            if (metrics.currencyCounts.hasOwnProperty(currency)) {
                metrics.currencyCounts[currency]++;

                // Track credit/debit amounts by currency
                if (isCredit) {
                    metrics.currencyCreditAmounts[currency] += amount;
                    // For total amount calculation: credit adds to the total
                    metrics.currencyAmounts[currency] += amount;
                } else if (isDebit) {
                    metrics.currencyDebitAmounts[currency] += amount;
                    // For total amount calculation: debit also adds to the total
                    metrics.currencyAmounts[currency] += amount;
                }
            }

            // Update report type counts
            if (reportType === 'HOC') {
                metrics.hocCount++;
                metrics.hocAmount += amount;
                if (currency === 'MMK') {
                    metrics.hocAmountMMK += amount;
                } else if (currency === 'USD') {
                    metrics.hocAmountUSD += amount;
                }
                // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections
            } else if (reportType === 'IBD') {
                // IBD transactions (excluding WU)
                metrics.ibdCount++;
                metrics.ibdAmount += amount;
                if (currency === 'MMK') {
                    metrics.ibdAmountMMK += amount;
                } else if (currency === 'USD') {
                    metrics.ibdAmountUSD += amount;
                }
                // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections
            } else if (reportType === 'WU') {
                // WU (Western Union) transactions - separate metrics (not included in IBD)
                metrics.wuCount = (metrics.wuCount || 0) + 1;
                metrics.wuAmount = (metrics.wuAmount || 0) + amount;
                if (currency === 'MMK') {
                    metrics.wuAmountMMK = (metrics.wuAmountMMK || 0) + amount;
                } else if (currency === 'USD') {
                    metrics.wuAmountUSD = (metrics.wuAmountUSD || 0) + amount;
                }
                // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections
            }
        }

        return metrics;
    }

    // Main thread date aggregation (simplified version of worker function)
    aggregateByDateMainThread(lines) {
        const dateData = {};

        for (const row of lines) {
            let date = 'Unknown';
            if (row.TRANSACTION_DATE) {
                try {
                    let dateStr = row.TRANSACTION_DATE;
                    if (dateStr.includes(' ')) {
                        dateStr = dateStr.split(' ')[0];
                    }

                    if (/^\d{2}-[A-Z]{3}-\d{2}$/.test(dateStr)) {
                        date = dateStr;
                    } else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                        const dateObj = new Date(dateStr);
                        if (!isNaN(dateObj.getTime())) {
                            date = dateObj.toLocaleDateString('en-GB', {
                                day: '2-digit',
                                month: 'short',
                                year: '2-digit'
                            }).replace(/ /g, '-').toUpperCase();
                        }
                    }
                } catch (error) {
                    console.error('Error parsing date:', error);
                    date = 'Unknown';
                }
            }

            if (!dateData[date]) {
                dateData[date] = {
                    hocCreditCount: 0, hocCreditAmountMMK: 0, hocCreditAmountUSD: 0,
                    hocDebitCount: 0, hocDebitAmountMMK: 0, hocDebitAmountUSD: 0,
                    ibdCreditCount: 0, ibdCreditAmountMMK: 0, ibdCreditAmountUSD: 0,
                    ibdDebitCount: 0, ibdDebitAmountMMK: 0, ibdDebitAmountUSD: 0,
                    wuCreditCount: 0, wuCreditAmountMMK: 0, wuCreditAmountUSD: 0,
                    wuDebitCount: 0, wuDebitAmountMMK: 0, wuDebitAmountUSD: 0
                };
            }

            const amount = typeof row.TRANSACTION_AMOUNT === 'string' ?
                parseFloat(row.TRANSACTION_AMOUNT) || 0 :
                (typeof row.TRANSACTION_AMOUNT === 'number' ? row.TRANSACTION_AMOUNT : 0);

            const isCredit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
            const isDebit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
            const currency = row.TRANSACTION_CURRENCY || 'MMK';

            if (row.REPORTTYPE === 'HOC') {
                if (isCredit) {
                    dateData[date].hocCreditCount++;
                    if (currency === 'MMK') {
                        dateData[date].hocCreditAmountMMK += amount;
                    } else if (currency === 'USD') {
                        dateData[date].hocCreditAmountUSD += amount;
                    }
                } else if (isDebit) {
                    dateData[date].hocDebitCount++;
                    if (currency === 'MMK') {
                        dateData[date].hocDebitAmountMMK += amount;
                    } else if (currency === 'USD') {
                        dateData[date].hocDebitAmountUSD += amount;
                    }
                }
            } else if (row.REPORTTYPE === 'IBD' || row.REPORTTYPE === 'WU' || row.REPORTTYPE === 'IBU') {
                if (isCredit) {
                    dateData[date].ibdCreditCount++;
                    if (currency === 'MMK') {
                        dateData[date].ibdCreditAmountMMK += amount;
                    } else if (currency === 'USD') {
                        dateData[date].ibdCreditAmountUSD += amount;
                    }
                } else if (isDebit) {
                    dateData[date].ibdDebitCount++;
                    if (currency === 'MMK') {
                        dateData[date].ibdDebitAmountMMK += amount;
                    } else if (currency === 'USD') {
                        dateData[date].ibdDebitAmountUSD += amount;
                    }
                }
            }
        }

        return dateData;
    }

    // Fallback processing when worker is not available
    processChunkFallback(chunk, header, isFirstChunk, fileName) {
        try {
            console.log(`Processing chunk with main thread fallback for ${fileName}`);

            // Process the chunk using main thread functions
            const result = this.processChunkMainThread(chunk, header, isFirstChunk);

            // Simulate worker message handling
            this.handleWorkerResult({
                action: 'chunkProcessed',
                result: result
            });

        } catch (error) {
            console.error('Main thread fallback processing error:', error);
            // Simulate worker error handling
            this.handleWorkerResult({
                action: 'error',
                error: error.message
            });
        }
    }

    // Handle worker results (both from actual worker and fallback)
    handleWorkerResult(data) {
        const { action, result, error } = data;

        if (action === 'chunkProcessed') {
            // NOTE: Metrics are now updated at the file level, not chunk level
            // This prevents duplicate accumulation of metrics
            console.log('Chunk processed by worker, metrics will be updated at file completion');


        } else if (action === 'error') {
            console.error('Processing error:', error);
        }
    }

    // Set up event listeners for file upload functionality
    initEventListeners() {
        // Prevent multiple event listener binding
        if (this.eventListenersInitialized) {
            console.log('Event listeners already initialized, skipping...');
            return;
        }

        const dropArea = document.getElementById('dropArea');
        const fileInput = document.getElementById('fileInput');
        const submitButton = document.getElementById('submitFiles');

        if (!dropArea || !fileInput || !submitButton) {
            console.warn('Required DOM elements not found, retrying event listener initialization...');
            setTimeout(() => this.initEventListeners(), 500);
            return;
        }

        // Store bound event handlers for potential cleanup
        this.boundEventHandlers = {
            fileInputChange: (e) => this.selectFiles(e.target.files),
            dragOver: (e) => {
                e.preventDefault();
                dropArea.classList.add('drag-over');
            },
            dragLeave: () => dropArea.classList.remove('drag-over'),
            drop: (e) => {
                e.preventDefault();
                dropArea.classList.remove('drag-over');
                if (e.dataTransfer.files.length > 0) {
                    this.selectFiles(e.dataTransfer.files);
                }
            },
            dropAreaClick: () => fileInput.click(),
            submitClick: () => this.processFiles()
        };

        // File input change event
        fileInput.addEventListener('change', this.boundEventHandlers.fileInputChange);

        // Drag and drop events
        dropArea.addEventListener('dragover', this.boundEventHandlers.dragOver);
        dropArea.addEventListener('dragleave', this.boundEventHandlers.dragLeave);
        dropArea.addEventListener('drop', this.boundEventHandlers.drop);

        // Click on drop area to trigger file input
        dropArea.addEventListener('click', this.boundEventHandlers.dropAreaClick);

        // Submit button click event
        submitButton.addEventListener('click', this.boundEventHandlers.submitClick);

        // Mark as initialized
        this.eventListenersInitialized = true;
        console.log('File upload event listeners initialized successfully');
    }

    // Handle the selected files (without processing) - Enhanced with security validation
    selectFiles(fileList) {
        if (this.isProcessing) {
            // Use window.app.showNotification for consistency, if available
            if (window.app) {
                window.app.showNotification('Please wait for the current files to finish processing.', 'warning');
            } else {
                alert('Please wait for the current files to finish processing.');
            }
            return;
        }

        // Convert FileList to Array and perform comprehensive validation
        const fileArray = Array.from(fileList);
        const validationResults = this.validateFiles(fileArray);

        if (validationResults.errors.length > 0) {
            // Show validation errors to user
            const errorMessage = 'File validation failed:\n' + validationResults.errors.join('\n');
            if (window.app) {
                window.app.showNotification(errorMessage, 'error', 8000);
            } else {
                alert(errorMessage);
            }
            return;
        }

        const newFiles = validationResults.validFiles;

        if (newFiles.length === 0) {
            // Use window.app.showNotification
            if (window.app) {
                window.app.showNotification('No valid CSV files found. Please select valid CSV files only.', 'info');
            } else {
                alert('No valid CSV files found. Please select valid CSV files only.');
            }
            return;
        }

        let alreadyProcessedCount = 0;
        const unprocessedFiles = [];
        const duplicateFiles = []; // Track duplicate files for better user feedback

        newFiles.forEach(file => {
            const lowerCaseFileName = file.name.toLowerCase(); // CASE INSENSITIVE

            if (this.uploadedFileNamesInSession.has(lowerCaseFileName)) { // CASE INSENSITIVE CHECK
                // Add to duplicates list for batch notification
                duplicateFiles.push(file.name);
            } else {
                this.uploadedFileNamesInSession.add(lowerCaseFileName); // CASE INSENSITIVE ADD

                // Existing logic for already processed files (by dataProcessor)
                if (window.dataProcessor && window.dataProcessor.isFileProcessed(file)) {
                    alreadyProcessedCount++;
                    // Still add to the list but mark as already processed
                    file.isAlreadyProcessed = true;
                    unprocessedFiles.push(file);
                    console.log(`📄 File marked as already processed: ${file.name}`);
                } else {
                    unprocessedFiles.push(file);
                    console.log(`📄 File added as unprocessed: ${file.name}`);
                }
            }
        });

        // Handle duplicate files with improved messaging
        if (duplicateFiles.length > 0) {
            this.showDuplicateFileNotification(duplicateFiles);
        }

        // Add only the non-duplicate, correctly typed files to the list
        // Important: Only add to this.files if there are new unprocessedFiles to add,
        // otherwise, if all files selected were duplicates, this would add an empty array.
        if (unprocessedFiles.length > 0) {
            console.log('Adding files to array. Current files count:', this.files.length);
            console.log('Files being added:', unprocessedFiles.map(f => f.name));

            // Add files one by one with debugging
            unprocessedFiles.forEach((file, index) => {
                console.log(`Adding file ${index}: ${file.name}`, {
                    size: file.size,
                    type: file.type,
                    isAlreadyProcessed: file.isAlreadyProcessed,
                    isRemoved: file.isRemoved
                });
                this.files.push(file);
            });

            console.log('Files array after addition:', this.files.length);
            console.log('First file after addition:', this.files[0] ? this.files[0].name : 'NO FIRST FILE');
        }

        // Update the file list UI in all cases, as it might need to reflect changes
        // even if no new files are added (e.g. if a file was processed by dataProcessor before)
        this.updateFileList();

        // Verify UI consistency after file addition
        setTimeout(() => {
            this.verifyAndFixUIConsistency('file addition', 'post-addition');
        }, 50);

        // Show the submit button if files are selected and update its state
        this.updateSubmitButtonVisibility();

        // Show notification if some files were already processed by dataProcessor
        if (alreadyProcessedCount > 0 && window.app) {
            window.app.showNotification(
                `${alreadyProcessedCount} file(s) have already been processed by the system. They will be marked in the list.`,
                'info'
            );
        }

        // Show notification if new unprocessed files were added
        const newFilesSuccessfullyAddedToUnprocessed = unprocessedFiles.some(f => !f.isAlreadyProcessed);
        if (newFilesSuccessfullyAddedToUnprocessed && window.app) {
            window.app.showNotification(
                'New unprocessed files detected. You can now click "Process Files" to process them.',
                'success'
            );
        }

        // Dispatch event for TTR File Manager to update UI immediately when files are uploaded
        if (unprocessedFiles.length > 0) {
            const event = new CustomEvent('filesUploaded', {
                detail: {
                    files: unprocessedFiles.map(file => ({
                        name: file.name,
                        size: file.size,
                        isAlreadyProcessed: file.isAlreadyProcessed || false
                    })),
                    timestamp: Date.now()
                }
            });
            document.dispatchEvent(event);
            console.log(`TTR File Manager: filesUploaded event dispatched for ${unprocessedFiles.length} files`);
        }
    }

    // Check if there are any unprocessed files
    hasUnprocessedFiles() {
        // If no files, return false
        if (this.files.length === 0) return false;

        // Check if there are any files that are not processed and not removed
        return this.files.some(file => {
            // CRITICAL FIX: Check isRemoved first
            const isRemoved = file.isRemoved === true ||
                             file.isRemoved === 'true' ||
                             Boolean(file.isRemoved);

            // Only check processed status if NOT removed
            const isProcessed = !isRemoved && (file.isAlreadyProcessed ||
                (window.dataProcessor && window.dataProcessor.isFileProcessed(file)));

            return !isProcessed && !isRemoved;
        });
    }

    // Update the visibility and state of the submit button
    updateSubmitButtonVisibility() {
        const submitContainer = document.getElementById('submitContainer');
        const submitButton = document.getElementById('submitFiles');

        if (!submitContainer || !submitButton) return;

        console.log(`🔘 Submit button visibility check - Total files: ${this.files.length}`);

        // First determine if we should show the container at all
        submitContainer.style.display = this.files.length > 0 ? 'block' : 'none';

        // If no files, nothing more to do
        if (this.files.length === 0) {
            console.log(`🔘 No files - hiding submit container`);
            return;
        }

        // Check if there are any unprocessed files
        const hasUnprocessedFiles = this.hasUnprocessedFiles();

        console.log(`🔘 Submit button state analysis:`);
        console.log(`  - Total files: ${this.files.length}`);
        console.log(`  - Has unprocessed files: ${hasUnprocessedFiles}`);

        // Debug each file's status
        this.files.forEach((file, index) => {
            const isRemoved = file.isRemoved === true ||
                             file.isRemoved === 'true' ||
                             Boolean(file.isRemoved);
            const isProcessed = !isRemoved && (file.isAlreadyProcessed ||
                (window.dataProcessor && window.dataProcessor.isFileProcessed(file)));

            console.log(`  - File ${index} (${file.name}): removed=${isRemoved}, processed=${isProcessed}, unprocessed=${!isProcessed && !isRemoved}`);
        });

        // Create or get the message element
        let messageElement = document.getElementById('submitButtonMessage');
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'submitButtonMessage';
            messageElement.className = 'submit-button-message';
            submitContainer.appendChild(messageElement);
        }

        // Update button state and message
        if (hasUnprocessedFiles) {
            // Enable button and hide message
            submitButton.disabled = false;
            messageElement.style.display = 'none';
            submitButton.classList.remove('disabled-btn');
        } else {
            // Disable button and show message
            submitButton.disabled = true;
            messageElement.style.display = 'block';
            messageElement.textContent = 'All files have been processed. Upload new files to enable processing.';
            submitButton.classList.add('disabled-btn');
        }
    }

    // Update the UI with the list of files
    updateFileList() {
        const fileListElement = document.getElementById('uploadedFilesList');
        fileListElement.innerHTML = '';

        console.log('Updating file list with', this.files.length, 'files');

        this.files.forEach((file, index) => {
            const listItem = document.createElement('li');

            // CRITICAL FIX: Check isRemoved FIRST before isProcessed
            // Multiple checks for isRemoved to catch any edge cases
            const isRemoved = file.isRemoved === true ||
                             file.isRemoved === 'true' ||
                             Boolean(file.isRemoved);

            // Only check processed status if NOT removed (removed takes priority)
            const isProcessed = !isRemoved && (file.isAlreadyProcessed ||
                (window.dataProcessor && window.dataProcessor.isFileProcessed(file)));

            // Enhanced logging for debugging file state issues, especially for first file
            if (index === 0) {
                console.log(`🔍 FIRST FILE DEBUG - File ${index} (${file.name}):`);
                console.log(`  - isRemoved: ${isRemoved} (raw: ${file.isRemoved}, type: ${typeof file.isRemoved})`);
                console.log(`  - isProcessed: ${isProcessed} (calculated AFTER isRemoved check)`);
                console.log(`  - dataProcessor.isFileProcessed: ${window.dataProcessor ? window.dataProcessor.isFileProcessed(file) : 'N/A'}`);
                console.log(`  - file.isAlreadyProcessed: ${file.isAlreadyProcessed}`);
                console.log(`  - File object:`, file);
            } else {
                console.log(`File ${index} (${file.name}): isRemoved=${isRemoved}, isProcessed=${isProcessed}, file.isRemoved=${file.isRemoved}, type=${typeof file.isRemoved}`);
            }

            // CRITICAL FIX: Priority logic - removed status takes precedence over processed
            if (isRemoved) {
                listItem.classList.add('removed-file');
                console.log(`✅ Applied 'removed-file' class to file: ${file.name}${index === 0 ? ' (FIRST FILE)' : ''}`);

                // CRITICAL FIX: Extra verification for first file
                if (index === 0) {
                    console.log('🔧 FIRST FILE: Ensuring removed class sticks...');

                    // Force the class application with a slight delay to ensure it sticks
                    setTimeout(() => {
                        if (!listItem.classList.contains('removed-file')) {
                            console.error('🚨 FIRST FILE: removed-file class was lost! Re-applying...');
                            listItem.classList.add('removed-file');
                        } else {
                            console.log('✅ FIRST FILE: removed-file class verified as applied');
                        }
                    }, 1);
                }
            } else if (isProcessed) {
                listItem.classList.add('processed-file');
                console.log(`Applied 'processed-file' class to file: ${file.name}`);
            }

            // Create file item elements safely to prevent XSS
            const fileNameSpan = document.createElement('span');
            fileNameSpan.className = 'file-name';
            fileNameSpan.textContent = `${file.name} (${this.formatFileSize(file.size)})`;

            const removeButton = document.createElement('button');
            removeButton.className = 'remove-file';
            removeButton.setAttribute('data-index', index.toString());
            removeButton.textContent = isRemoved ? 'Delete' : 'Remove';

            // Clear any existing content
            listItem.innerHTML = '';

            // Add elements to list item
            listItem.appendChild(fileNameSpan);

            // Add status element if needed
            if (isRemoved) {
                const statusSpan = document.createElement('span');
                statusSpan.className = 'file-status removed';
                statusSpan.textContent = 'Removed';
                listItem.appendChild(statusSpan);
            } else if (isProcessed) {
                const statusSpan = document.createElement('span');
                statusSpan.className = 'file-status processed';
                statusSpan.textContent = 'Processed';
                listItem.appendChild(statusSpan);
            }

            listItem.appendChild(removeButton);

            // ENHANCED FIX: Multiple verification passes for class application, especially for first file
            if (isRemoved && !listItem.classList.contains('removed-file')) {
                console.error(`Failed to apply 'removed-file' class to file: ${file.name}${index === 0 ? ' (FIRST FILE)' : ''}`);
                listItem.classList.add('removed-file'); // Force add the class

                // Additional verification for first file
                if (index === 0) {
                    console.log('🔧 FIRST FILE: Force-applying removed-file class with multiple attempts...');

                    // Immediate re-check
                    setTimeout(() => {
                        if (!listItem.classList.contains('removed-file')) {
                            console.error('🚨 FIRST FILE: Class still missing after first attempt!');
                            listItem.classList.add('removed-file');

                            // Force style application directly
                            listItem.style.backgroundColor = 'rgba(231, 76, 60, 0.05)';
                            listItem.style.borderLeft = '4px solid var(--danger-color)';
                            listItem.style.opacity = '0.8';

                            const fileName = listItem.querySelector('.file-name');
                            if (fileName) {
                                fileName.style.textDecoration = 'line-through';
                                fileName.style.color = 'var(--text-light)';
                            }

                            console.log('🔧 FIRST FILE: Applied styles directly as fallback');
                        }
                    }, 5);
                }
            }

            fileListElement.appendChild(listItem);
        });

        // Add event listeners to remove buttons with enhanced debugging and improved index handling
        document.querySelectorAll('.remove-file').forEach((button, buttonIndex) => {
            // Remove any existing event listeners to prevent duplicates
            button.removeEventListener('click', this._handleRemoveClick);

            // Create a bound event handler for this specific button
            const handleRemoveClick = (e) => {
                e.stopPropagation(); // Prevent event bubbling

                // CRITICAL FIX: Always get fresh index from DOM at click time
                const index = parseInt(e.currentTarget.getAttribute('data-index'));

                // Enhanced validation and debugging
                console.log(`Remove button clicked - buttonIndex: ${buttonIndex}, data-index: ${index}, files.length: ${this.files.length}`);

                // Double-check index validity with current files array
                if (index < 0 || index >= this.files.length) {
                    console.error(`Invalid index from button: ${index}, files array length: ${this.files.length}`);
                    console.error(`Button element:`, e.currentTarget);
                    console.error(`Current files:`, this.files.map((f, i) => `${i}: ${f.name}`));

                    // Force UI refresh to fix index mismatch
                    console.log('Forcing UI refresh due to index mismatch...');
                    this.updateFileList();
                    return;
                }

                const file = this.files[index];
                if (!file) {
                    console.error(`No file found at index ${index}`);
                    console.error(`Files array:`, this.files);

                    // Force UI refresh
                    this.updateFileList();
                    return;
                }

                console.log(`Remove button clicked for file ${index} (${file.name}), isRemoved: ${file.isRemoved}`);

                // Show confirmation dialog with enhanced callback
                // CRITICAL FIX: Use file name AND validate index at execution time
                this.showConfirmationDialog(
                    file.isRemoved ?
                        `Permanently delete "${file.name}"?` :
                        `Remove "${file.name}" from dashboard?`,
                    file.isRemoved ?
                        'This will permanently delete the file from the list. This action cannot be undone.' :
                        'This will remove the file data from all metrics and visualizations. The file will remain in the list but marked as removed.',
                    () => {
                        console.log(`Confirmation accepted for file ${index} (${file.name})`);

                        // CRITICAL FIX: Re-validate index at execution time to handle any changes
                        // that might have occurred between click and confirmation
                        const currentIndex = this.files.findIndex(f => f.name === file.name);

                        if (currentIndex !== -1) {
                            console.log(`Found current index ${currentIndex} for file ${file.name} (original index was ${index})`);

                            // Additional safety check: ensure the file at currentIndex is the same file
                            const currentFile = this.files[currentIndex];
                            if (currentFile && currentFile.name === file.name) {
                                this.removeFile(currentIndex);
                            } else {
                                console.error(`File mismatch at index ${currentIndex}. Expected: ${file.name}, Found: ${currentFile ? currentFile.name : 'null'}`);
                                // Force UI refresh and abort operation
                                this.updateFileList();
                            }
                        } else {
                            console.error(`File ${file.name} not found in array during removal confirmation`);
                            // Force UI refresh to sync state
                            this.updateFileList();
                        }
                    }
                );
            };

            // Store the handler reference for potential cleanup
            button._removeHandler = handleRemoveClick;

            // Add the event listener
            button.addEventListener('click', handleRemoveClick);
        });

        console.log('File list update completed');
    }

    // Verify and fix UI consistency after file operations
    verifyAndFixUIConsistency(operationContext = 'unknown', verificationPhase = 'default') {
        console.log(`UI consistency verification (${verificationPhase}) for operation: ${operationContext}`);

        const buttons = document.querySelectorAll('.remove-file');
        const filesCount = this.files.length;
        const buttonsCount = buttons.length;

        console.log(`- Files in array: ${filesCount}`);
        console.log(`- Buttons in DOM: ${buttonsCount}`);

        // Check if button count matches file count
        if (buttonsCount !== filesCount) {
            console.error(`Button count mismatch! Files: ${filesCount}, Buttons: ${buttonsCount}`);
            console.log('Forcing complete UI refresh...');
            this.updateFileList();
            return;
        }

        let indexMismatchDetected = false;
        let uiInconsistencyDetected = false;

        // Verify each button has correct data-index and UI state
        buttons.forEach((button, buttonIndex) => {
            const dataIndex = parseInt(button.getAttribute('data-index'));

            // Check for index mismatches
            if (dataIndex !== buttonIndex) {
                console.error(`Index mismatch at button ${buttonIndex}: data-index=${dataIndex}`);
                indexMismatchDetected = true;
            }

            if (dataIndex >= filesCount) {
                console.error(`Invalid data-index ${dataIndex} >= files.length ${filesCount}`);
                indexMismatchDetected = true;
            }

            // Verify UI state consistency for each file
            if (dataIndex < filesCount) {
                const file = this.files[dataIndex];
                const listItem = button.closest('li');

                if (file && listItem) {
                    // CRITICAL FIX: Use same priority logic as updateFileList
                    const isRemoved = file.isRemoved === true ||
                                     file.isRemoved === 'true' ||
                                     Boolean(file.isRemoved);

                    const hasRemovedClass = listItem.classList.contains('removed-file');
                    const hasRemovedStatus = !!listItem.querySelector('.file-status.removed');
                    const buttonText = button.textContent;
                    const expectedButtonText = isRemoved ? 'Delete' : 'Remove';

                    // Check for UI inconsistencies
                    if (isRemoved !== hasRemovedClass ||
                        isRemoved !== hasRemovedStatus ||
                        buttonText !== expectedButtonText) {

                        console.error(`UI inconsistency for file ${dataIndex} (${file.name}):`);
                        console.error(`  File isRemoved: ${isRemoved}`);
                        console.error(`  Has removed class: ${hasRemovedClass}`);
                        console.error(`  Has removed status: ${hasRemovedStatus}`);
                        console.error(`  Button text: "${buttonText}" (expected: "${expectedButtonText}")`);

                        uiInconsistencyDetected = true;

                        // Fix UI inconsistency immediately with special handling for first file
                        if (isRemoved && !hasRemovedClass) {
                            listItem.classList.add('removed-file');
                            console.log(`Fixed: Added removed-file class to ${file.name}${dataIndex === 0 ? ' (FIRST FILE)' : ''}`);

                            // CRITICAL FIX: Extra aggressive fix for first file
                            if (dataIndex === 0) {
                                console.log('🔧 FIRST FILE: Applying aggressive styling fix...');

                                // Force remove any conflicting classes
                                listItem.classList.remove('processed-file');

                                // Force add removed class multiple times
                                listItem.classList.add('removed-file');

                                // Apply inline styles as backup
                                listItem.style.backgroundColor = 'rgba(231, 76, 60, 0.05)';
                                listItem.style.borderLeft = '4px solid #e74c3c';
                                listItem.style.opacity = '0.8';

                                // Fix file name styling
                                const fileNameElement = listItem.querySelector('.file-name');
                                if (fileNameElement) {
                                    fileNameElement.style.textDecoration = 'line-through';
                                    fileNameElement.style.color = '#6c757d';
                                }

                                console.log('🔧 FIRST FILE: Applied direct styling as backup');
                            }
                        } else if (!isRemoved && hasRemovedClass) {
                            listItem.classList.remove('removed-file');
                            console.log(`Fixed: Removed removed-file class from ${file.name}`);
                        }

                        // Fix status element
                        let statusElement = listItem.querySelector('.file-status');
                        if (isRemoved && !hasRemovedStatus) {
                            if (statusElement) {
                                statusElement.className = 'file-status removed';
                                statusElement.textContent = 'Removed';
                            } else {
                                const fileNameElement = listItem.querySelector('.file-name');
                                if (fileNameElement) {
                                    statusElement = document.createElement('span');
                                    statusElement.className = 'file-status removed';
                                    statusElement.textContent = 'Removed';
                                    fileNameElement.insertAdjacentElement('afterend', statusElement);
                                }
                            }
                            console.log(`Fixed: Added/updated removed status for ${file.name}`);
                        } else if (!isRemoved && statusElement && statusElement.classList.contains('removed')) {
                            if (file.isAlreadyProcessed || (window.dataProcessor && window.dataProcessor.isFileProcessed(file))) {
                                statusElement.className = 'file-status processed';
                                statusElement.textContent = 'Processed';
                            } else {
                                statusElement.remove();
                            }
                            console.log(`Fixed: Updated status for ${file.name}`);
                        }

                        // Fix button text
                        if (buttonText !== expectedButtonText) {
                            button.textContent = expectedButtonText;
                            console.log(`Fixed: Updated button text to "${expectedButtonText}" for ${file.name}`);
                        }
                    }
                }
            }
        });

        // If major issues detected, force complete UI refresh
        if (indexMismatchDetected) {
            console.error(`Index mismatches detected in ${verificationPhase} phase - forcing complete UI refresh`);
            this.updateFileList();
        } else if (uiInconsistencyDetected) {
            console.log(`UI inconsistencies fixed in ${verificationPhase} phase`);
        } else {
            console.log(`UI consistency verified in ${verificationPhase} phase - all good`);
        }
    }

    // Show confirmation dialog
    showConfirmationDialog(title, message, confirmCallback) {
        const dialog = document.getElementById('confirmationDialog');
        const messageElement = document.getElementById('confirmationMessage');
        const titleElement = dialog.querySelector('.confirmation-title');
        const confirmBtn = document.getElementById('confirmBtn');
        const cancelBtn = document.getElementById('cancelBtn');

        // Set dialog content
        titleElement.textContent = title;
        messageElement.textContent = message;

        // Show dialog
        dialog.classList.add('show');

        // Set up event listeners
        const handleConfirm = () => {
            dialog.classList.remove('show');
            confirmCallback();
            // Clean up event listeners
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
        };

        const handleCancel = () => {
            dialog.classList.remove('show');
            // Clean up event listeners
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
        };

        // Add event listeners
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
    }

    // Show enhanced notification for duplicate files
    showDuplicateFileNotification(duplicateFiles) {
        if (!duplicateFiles || duplicateFiles.length === 0) return;

        let message;
        let notificationType = 'error';
        const duration = 5000; // Show for 5 seconds for important duplicate warnings

        if (duplicateFiles.length === 1) {
            // Single duplicate file
            message = `Duplicate file detected: "${duplicateFiles[0]}" has already been uploaded in this session. To upload this file again, please refresh the application first.`;
        } else if (duplicateFiles.length <= 3) {
            // Multiple duplicates (show all names if 3 or fewer)
            const fileList = duplicateFiles.map(name => `"${name}"`).join(', ');
            message = `${duplicateFiles.length} duplicate files detected: ${fileList}. These files have already been uploaded in this session. To upload them again, please refresh the application first.`;
        } else {
            // Many duplicates (show count and first few names)
            const firstThree = duplicateFiles.slice(0, 3).map(name => `"${name}"`).join(', ');
            const remaining = duplicateFiles.length - 3;
            message = `${duplicateFiles.length} duplicate files detected: ${firstThree} and ${remaining} more. These files have already been uploaded in this session. To upload them again, please refresh the application first.`;
        }

        // Show notification using the existing notification system
        if (window.app) {
            window.app.showNotification(message, notificationType, duration);
        } else {
            // Fallback to alert if notification system is not available
            alert(message);
        }

        // Log for debugging
        console.log('Duplicate files detected:', duplicateFiles);
    }

    // Remove a file from the list and update the dashboard
    removeFile(index) {
        if (this.isProcessing) {
            if (window.app) {
                window.app.showNotification('Cannot remove files while processing.', 'error');
            } else {
                alert('Cannot remove files while processing.');
            }
            return;
        }

        // CRITICAL FIX: Ensure this.files exists and is an array
        if (!this.files || !Array.isArray(this.files)) {
            console.error('Files array is undefined or not an array:', this.files);
            if (window.app) {
                window.app.showNotification('Error: Files array is corrupted.', 'error');
            }
            return;
        }

        // Validate index bounds
        if (index < 0 || index >= this.files.length) {
            console.error(`Invalid file index: ${index}. Files array length: ${this.files.length}`);
            if (window.app) {
                window.app.showNotification('Error: Invalid file selection.', 'error');
            }
            return;
        }

        // Get the file before removing it
        const file = this.files[index];
        const fileName = file.name;

        console.log(`removeFile called for index ${index}, file: ${fileName}, current isRemoved: ${file.isRemoved}`);

        // Debug file states before removal
        this.debugFileStates();

        // Check if the file is already marked as removed
        if (file.isRemoved) {
            console.log(`Permanently deleting file at index ${index}: ${fileName}`);
            console.log(`Files array before splice:`, this.files.map((f, i) => `${i}: ${f.name} (removed: ${f.isRemoved})`));

            // Permanently delete the file using splice
            this.files.splice(index, 1);

            console.log(`Files array after splice:`, this.files.map((f, i) => `${i}: ${f.name} (removed: ${f.isRemoved})`));

            // Clean up all file-specific data
            this.fileMetrics.delete(fileName);
            this.fileSerialNumbers.delete(fileName);
            this.fileDateData.delete(fileName);
            this.fileTransactionData.delete(fileName);

            // CRITICAL FIX: Remove from session tracking to allow re-upload
            this.uploadedFileNamesInSession.delete(fileName.toLowerCase());

            // Remove file data from data processor (including high-value transactions)
            if (window.dataProcessor) {
                const fileId = window.dataProcessor.generateFileId(file);
                window.dataProcessor.removeFileData(fileId);

                // Dispatch event for TTR monitoring
                const event = new CustomEvent('fileRemoved', {
                    detail: {
                        fileId: fileId,
                        fileName: fileName,
                        permanent: true
                    }
                });
                document.dispatchEvent(event);
            }

            console.log(`About to update file list UI after permanent deletion of ${fileName}`);

            // CRITICAL FIX: Update the file list UI immediately after array modification
            // This ensures that data-index attributes are regenerated correctly
            this.updateFileList();

            // ENHANCED FIX: Multiple verification passes to ensure UI consistency
            // Immediate verification (very short delay)
            setTimeout(() => {
                this.verifyAndFixUIConsistency(fileName, 'immediate');
            }, 10);

            // Secondary verification (short delay)
            setTimeout(() => {
                this.verifyAndFixUIConsistency(fileName, 'secondary');
            }, 50);

            // Final verification (longer delay to catch any async issues)
            setTimeout(() => {
                this.verifyAndFixUIConsistency(fileName, 'final');
            }, 200);

            // Update submit button visibility
            this.updateSubmitButtonVisibility();



            // Show notification
            if (window.app) {
                window.app.showNotification(`File "${fileName}" permanently deleted.`, 'warning');
            }

            console.log(`File permanently deleted: ${fileName}. Remaining files: ${this.files.length}`);
            return;
        }

        // Mark the file as removed instead of actually removing it
        console.log(`Marking file as removed: ${fileName} (before: isRemoved=${file.isRemoved})`);
        console.log(`File object before modification:`, file);
        console.log(`File at index ${index} before modification:`, this.files[index]);

        // CRITICAL FIX: Instead of replacing with a plain object, just mark the original file as removed
        // This preserves the File object's methods (like slice()) which are needed for re-processing
        const originalFile = this.files[index];

        // Simply set the removed flag on the original File object
        originalFile.isRemoved = true;
        // Clear processed status when marking as removed to prevent conflicts
        originalFile.isAlreadyProcessed = false;

        console.log(`File object after marking as removed:`, this.files[index]);
        console.log(`File marked as removed: ${fileName} (after: isRemoved=${this.files[index].isRemoved})`);

        // Final verification with special handling for first file
        if (this.files[index].isRemoved !== true) {
            console.error(`CRITICAL: Failed to set isRemoved for ${fileName}${index === 0 ? ' (FIRST FILE)' : ''}`);
            // Absolute last resort
            this.files[index].isRemoved = true;
            console.log(`Force set isRemoved: ${this.files[index].isRemoved}`);
        }

        // CRITICAL FIX: Remove from dataProcessor's processed files list
        if (window.dataProcessor) {
            const fileId = window.dataProcessor.generateFileId(originalFile);
            if (window.dataProcessor.processedFiles && window.dataProcessor.processedFiles.has(fileId)) {
                window.dataProcessor.processedFiles.delete(fileId);
                console.log(`🔧 Removed file from dataProcessor processed list: ${fileId}`);
            }
        }

        // CRITICAL FIX: Special verification for first file
        if (index === 0) {
            console.log('🔍 FIRST FILE: Post-replacement verification...');
            console.log(`  - File at index 0:`, this.files[0]);
            console.log(`  - isRemoved value:`, this.files[0].isRemoved);
            console.log(`  - isRemoved type:`, typeof this.files[0].isRemoved);
            console.log(`  - isAlreadyProcessed:`, this.files[0].isAlreadyProcessed);

            // Triple-check the first file's isRemoved status
            if (this.files[0].isRemoved !== true) {
                console.error('🚨 FIRST FILE: isRemoved still not true! Forcing...');
                this.files[0].isRemoved = true;

                // Also try setting it as a string (in case of weird type coercion)
                this.files[0].removed = true;
                this.files[0]['isRemoved'] = true;

                console.log('🔧 FIRST FILE: Applied multiple isRemoved assignments');
            }

            // Verify dataProcessor doesn't think it's processed
            if (window.dataProcessor && window.dataProcessor.isFileProcessed(this.files[0])) {
                console.log('🚨 FIRST FILE: dataProcessor still thinks file is processed!');
                const fileId = window.dataProcessor.generateFileId(this.files[0]);
                window.dataProcessor.processedFiles.delete(fileId);
                console.log('🔧 FIRST FILE: Force-removed from dataProcessor processed list');
            }
        }

        // Get the file metrics and serial numbers before removing from processed data
        const fileMetrics = this.fileMetrics.get(fileName);
        const fileSerialNumbers = this.fileSerialNumbers.get(fileName);

        // Log the file metrics and serial numbers being removed
        if (fileMetrics) {
            console.log(`File metrics being removed for ${fileName}:`, fileMetrics);
        }

        if (fileSerialNumbers) {
            console.log(`File serial numbers being removed - HOC: ${fileSerialNumbers.hoc.size}, IBD: ${fileSerialNumbers.ibd.size}`);

            // Remove file data (including high-value transactions and serial numbers) from the data processor
            if (window.dataProcessor) {
                const fileId = window.dataProcessor.generateFileId(file);
                const removalResult = window.dataProcessor.removeFileData(fileId, fileSerialNumbers);

                // Dispatch event for TTR monitoring
                const event = new CustomEvent('fileRemoved', {
                    detail: {
                        fileId: fileId,
                        fileName: fileName,
                        permanent: false
                    }
                });
                document.dispatchEvent(event);

                // Log detailed removal information
                if (removalResult && removalResult.highValueRemoval) {
                    const { removedTransactions, removedDates, dateDetails } = removalResult.highValueRemoval;
                    console.log(`High-value transaction removal summary for ${fileName}:`);
                    console.log(`- Removed ${removedTransactions || 0} high-value transactions`);
                    console.log(`- Affected ${dateDetails ? dateDetails.length : 0} dates`);
                    console.log(`- Completely cleared ${removedDates || 0} dates`);
                    if (dateDetails && Array.isArray(dateDetails)) {
                        dateDetails.forEach(detail => console.log(`  ${detail}`));
                    }
                }
            }
        } else if (window.dataProcessor) {
            // Even if no serial numbers, still remove high-value transaction data
            const fileId = window.dataProcessor.generateFileId(file);
            const removalResult = window.dataProcessor.removeFileData(fileId);

            // Dispatch event for TTR monitoring
            const event = new CustomEvent('fileRemoved', {
                detail: {
                    fileId: fileId,
                    fileName: fileName,
                    permanent: false
                }
            });
            document.dispatchEvent(event);

            // Log detailed removal information
            if (removalResult && removalResult.highValueRemoval) {
                const { removedTransactions, removedDates, dateDetails } = removalResult.highValueRemoval;
                console.log(`High-value transaction removal summary for ${fileName}:`);
                console.log(`- Removed ${removedTransactions || 0} high-value transactions`);
                console.log(`- Affected ${dateDetails ? dateDetails.length : 0} dates`);
                console.log(`- Completely cleared ${removedDates || 0} dates`);
                if (dateDetails && Array.isArray(dateDetails)) {
                    dateDetails.forEach(detail => console.log(`  ${detail}`));
                }
            }
        }

        // Remove the file data from processedData array
        const fileDataIndex = this.processedData.findIndex(item => item.fileName === fileName);
        if (fileDataIndex !== -1) {
            this.processedData.splice(fileDataIndex, 1);
        }

        console.log(`File marked as removed: ${fileName}`);

        // Debug file states after marking as removed
        this.debugFileStates();

        // Update the file list UI to show the removed status
        console.log(`Updating file list UI after marking ${fileName} as removed`);

        // Force immediate UI update with multiple approaches
        this.updateFileList();

        // ENHANCED FIX: Use the new verification system for file removal too
        // Immediate verification (very short delay)
        setTimeout(() => {
            this.verifyAndFixUIConsistency(`file removal: ${fileName}`, 'immediate');
        }, 10);

        // Secondary verification (short delay)
        setTimeout(() => {
            this.verifyAndFixUIConsistency(`file removal: ${fileName}`, 'secondary');
        }, 50);

        // Final verification (longer delay to catch any async issues)
        setTimeout(() => {
            this.verifyAndFixUIConsistency(`file removal: ${fileName}`, 'final');
        }, 200);

        // Update submit button visibility
        this.updateSubmitButtonVisibility();

        // CRITICAL FIX: Remove from session tracking to allow re-upload
        this.uploadedFileNamesInSession.delete(fileName.toLowerCase());
        console.log(`🔧 Removed ${fileName} from session tracking to allow re-upload`);

        // CRITICAL FIX: Reset TTR retry counter to prevent infinite retry loops
        if (window.dataProcessor && typeof window.dataProcessor.resetTTRRetryCounter === 'function') {
            window.dataProcessor.resetTTRRetryCounter();
        }

        // Update the dashboard with the remaining data
        this.updateDashboardAfterRemoval();

        // Show notification
        if (window.app) {
            window.app.showNotification(`File "${fileName}" removed. Dashboard updated to exclude its data.`, 'warning');
        }
    }

    // Update dashboard after file removal
    updateDashboardAfterRemoval() {
        // Count active (non-removed) files
        const activeFiles = this.files.filter(file => !file.isRemoved);

        // If no active files remain, reset the dashboard
        if (activeFiles.length === 0) {
            this.resetDashboard();
            return;
        }

        // For a more accurate approach, we'll recalculate metrics from scratch
        // based on the remaining active files

        // First, reset the data processor's metrics and serial number sets
        // BUT preserve high-value transaction data for active files
        if (window.dataProcessor) {
            // Store high-value data for active files before reset
            const activeFileHighValueData = new Map();
            activeFiles.forEach(file => {
                const fileId = window.dataProcessor.generateFileId(file);
                const fileHighValueData = window.dataProcessor.fileHighValueCounts.get(fileId);
                if (fileHighValueData) {
                    activeFileHighValueData.set(fileId, fileHighValueData);
                }
            });

            console.log(`Preserved high-value data for ${activeFileHighValueData.size} active files before reset`);

            // Reset metrics but preserve file-specific high-value data
            window.dataProcessor.resetMetrics(true);

            // Reset our local serial number sets too
            this.hocSerialNumbers.clear();
            this.ibdSerialNumbers.clear();
            if (this.wuSerialNumbers) this.wuSerialNumbers.clear();
            this.allSerialNumbers.clear();

            console.log(`Recalculating metrics for ${activeFiles.length} active files`);

            // CRITICAL FIX: Rebuild raw transaction data from remaining active files
            this.rebuildRawDataFromActiveFiles(activeFiles);

            // Re-add metrics for each active file
            activeFiles.forEach(file => {
                const fileName = file.name;
                const fileMetrics = this.fileMetrics.get(fileName);
                const fileSerialNumbers = this.fileSerialNumbers.get(fileName);

                if (fileMetrics) {
                    // Create a deep copy of the metrics to avoid reference issues
                    const metricsCopy = JSON.parse(JSON.stringify(fileMetrics));
                    console.log(`Re-adding metrics for file: ${fileName}`, metricsCopy);
                    window.dataProcessor.updateMetrics(metricsCopy);
                }

                if (fileSerialNumbers) {
                    console.log(`Re-adding serial numbers for file: ${fileName}`);

                    // Add HOC serial numbers
                    fileSerialNumbers.hoc.forEach(serialNo => {
                        this.hocSerialNumbers.add(serialNo);
                        this.allSerialNumbers.add(serialNo);
                    });

                    // Add IBD serial numbers
                    fileSerialNumbers.ibd.forEach(serialNo => {
                        this.ibdSerialNumbers.add(serialNo);
                        this.allSerialNumbers.add(serialNo);
                    });

                    // Add WU serial numbers
                    if (fileSerialNumbers.wu) {
                        if (!this.wuSerialNumbers) this.wuSerialNumbers = new Set();
                        fileSerialNumbers.wu.forEach(serialNo => {
                            this.wuSerialNumbers.add(serialNo);
                            this.allSerialNumbers.add(serialNo);
                        });
                    }
                }
            });

            // Restore high-value transaction data for active files
            activeFileHighValueData.forEach((fileData, fileId) => {
                window.dataProcessor.fileHighValueCounts.set(fileId, fileData);
                console.log(`Restored high-value data for file ID: ${fileId}, count: ${fileData.totalCount}`);
            });

            // Rebuild global date counts from restored active file data
            window.dataProcessor.rebuildDateCountsFromActiveFiles();

            // Update the unique serial counts in the data processor
            const hocSize = this.hocSerialNumbers.size;
            const ibdSize = this.ibdSerialNumbers.size;
            const wuSize = this.wuSerialNumbers ? this.wuSerialNumbers.size : 0;
            const totalSize = this.allSerialNumbers.size;

            console.log(`Updating serial counts after recalculation - HOC: ${hocSize}, IBD: ${ibdSize}, WU: ${wuSize}, Total: ${totalSize}`);

            window.dataProcessor.updateSerialCounts(
                hocSize,
                ibdSize,
                totalSize,
                wuSize
            );

            // Also update WU count in the data processor
            if (window.dataProcessor.summaryMetrics) {
                window.dataProcessor.summaryMetrics.wuUniqueSerialCount = wuSize;
            }

            // CRITICAL FIX: Force currency metrics recalculation from updated raw data
            console.log('🔄 Forcing currency metrics recalculation after file removal...');
            if (typeof window.dataProcessor.recalculateCurrencyMetrics === 'function') {
                window.dataProcessor.recalculateCurrencyMetrics();
                console.log('✅ Currency metrics recalculated from remaining active files');
            }

            // CRITICAL FIX: Explicitly update currency breakdown UI
            setTimeout(() => {
                if (typeof window.dataProcessor.updateCurrencyBreakdown === 'function') {
                    window.dataProcessor.updateCurrencyBreakdown();
                    console.log('✅ Currency breakdown UI updated');
                }
            }, 100);

            // Update the high-value breakdown display to reflect restored data
            window.dataProcessor.updateHighValueFileBreakdownCombined();
        }

        // Update the files processed count
        const filesProcessedElement = document.getElementById('filesProcessed');
        if (filesProcessedElement) {
            const uniqueProcessedCount = window.dataProcessor ?
                window.dataProcessor.getProcessedFilesCount() : 0;

            filesProcessedElement.textContent = `${activeFiles.length} active (${uniqueProcessedCount} unique)`;
        }





        // Log the current state after removal
        if (window.dataProcessor) {
            const metrics = window.dataProcessor.getSummaryMetrics();
            console.log('Dashboard state after file removal:', {
                totalTransactions: metrics.totalTransactions,
                hocCount: metrics.hocCount,
                ibdCount: metrics.ibdCount,
                hocUniqueSerialCount: metrics.hocUniqueSerialCount,
                ibdUniqueSerialCount: metrics.ibdUniqueSerialCount,
                currencyAmounts: metrics.currencyAmounts
            });
        }
    }

    // Rebuild raw transaction data from remaining active files
    rebuildRawDataFromActiveFiles(activeFiles) {
        console.log('🔄 Rebuilding raw transaction data from active files...');

        if (!window.dataProcessor) {
            console.warn('DataProcessor not available for raw data rebuild');
            return;
        }

        // Clear existing raw data
        window.dataProcessor.rawData = [];

        // Collect all transaction data from active files
        const allActiveTransactions = [];
        let totalTransactions = 0;

        activeFiles.forEach(file => {
            const fileName = file.name;
            const fileTransactionData = this.fileTransactionData.get(fileName);

            if (fileTransactionData && Array.isArray(fileTransactionData)) {
                console.log(`Adding ${fileTransactionData.length} transactions from active file: ${fileName}`);
                allActiveTransactions.push(...fileTransactionData);
                totalTransactions += fileTransactionData.length;
            } else {
                console.log(`No transaction data found for active file: ${fileName}`);
            }
        });

        // Update the data processor with the rebuilt raw data
        if (allActiveTransactions.length > 0) {
            window.dataProcessor.rawData = allActiveTransactions;
            console.log(`✅ Raw data rebuilt with ${totalTransactions} transactions from ${activeFiles.length} active files`);
        } else {
            console.log('⚠️ No transaction data found in any active files');
        }
    }

    // Reset dashboard to empty state
    resetDashboard() {
        // Update files processed count
        document.getElementById('filesProcessed').textContent = '0';

        // Reset unique serial number sets
        this.hocSerialNumbers.clear();
        this.ibdSerialNumbers.clear();
        this.wuSerialNumbers.clear();
        this.allSerialNumbers.clear();

        // Clear file metrics and serial numbers maps
        this.fileMetrics.clear();
        this.fileSerialNumbers.clear();

        // Clear file date data map
        this.fileDateData.clear();

        // Clear processed data array
        this.processedData = [];

        // Clear the session file tracking for duplicates
        this.uploadedFileNamesInSession.clear();

        // Reset data processor with empty array
        if (window.dataProcessor) {
            // Clear the processed files tracking
            window.dataProcessor.processedFiles.clear();
            // Clear high-value date tracking
            window.dataProcessor.dateHighValueCounts.clear();
            window.dataProcessor.processData([]);
        }





        // Show notification about reset
        if (window.app) {
            window.app.showNotification('Dashboard reset. All file tracking has been cleared, including duplicate file detection.', 'info');
        }

        console.log('Dashboard reset to empty state. File tracking and duplicate detection cleared.');
    }



    // Debug method to log current file states
    debugFileStates() {
        console.log('=== Current File States ===');
        this.files.forEach((file, index) => {
            const isProcessed = file.isAlreadyProcessed ||
                (window.dataProcessor && window.dataProcessor.isFileProcessed(file));
            console.log(`File ${index}: ${file.name}`, {
                isRemoved: file.isRemoved,
                isProcessed: isProcessed,
                size: file.size,
                type: file.type
            });
        });
        console.log('=== End File States ===');
    }

    // Format file size for display
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Process all files in chunks with memory optimization and browser responsiveness
    async processFiles() {
        // Enhanced processing state validation
        if (this.files.length === 0) {
            console.log('No files to process');
            return;
        }

        if (this.isProcessing) {
            console.warn('File processing already in progress, ignoring duplicate call');
            if (window.app) {
                window.app.showNotification('File processing is already in progress. Please wait...', 'warning');
            }
            return;
        }

        // Generate a unique processing session ID to track this processing run
        const processingSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        console.log(`Starting file processing session: ${processingSessionId} for ${this.files.length} files`);

        // Set processing state with additional validation
        this.isProcessing = true;
        this.currentProcessingSession = processingSessionId;
        this.processedData = [];

        // Add a small delay to ensure UI state is updated
        await new Promise(resolve => setTimeout(resolve, 10));

        // Prevent browser "Page Unresponsive" warnings by yielding control frequently
        this.setupResponsivenessHandling();

        // Filter out already processed files and ensure we only process valid File objects
        const filesToProcess = this.files.filter(file => {
            // CRITICAL FIX: Ensure file is not removed and has slice method (is a proper File object)
            if (file.isRemoved) {
                console.log(`Skipping removed file: ${file.name}`);
                return false;
            }

            if (typeof file.slice !== 'function') {
                console.error(`Invalid file object detected: ${file.name} - missing slice method`);
                return false;
            }

            return !(window.dataProcessor && window.dataProcessor.isFileProcessed(file));
        });

        // Calculate total size of files to process
        this.totalSize = filesToProcess.reduce((total, file) => total + file.size, 0);
        this.processedSize = 0;

        // Count of already processed files
        const alreadyProcessedCount = this.files.length - filesToProcess.length;

        // Show progress UI with enhanced visibility using utility method
        this.showProgressBar();

        // Get progress elements for later use
        const progressElement = document.getElementById('uploadProgress');
        const progressText = document.getElementById('progressText');

        // Update initial text
        if (progressText) {
            progressText.textContent = '0% - Starting file processing...';
        }

        // If all files are already processed, show notification and return
        if (filesToProcess.length === 0) {
            if (window.app) {
                window.app.showNotification(
                    `All ${alreadyProcessedCount} file(s) have already been processed. No new data to process.`,
                    'info'
                );
            }

            // Update the file list to refresh status indicators
            this.updateFileList();

            // Hide progress UI with smooth transition using utility method
            this.hideProgressBar();

            this.isProcessing = false;
            return;
        }

        try {
            // Process files sequentially to reduce memory pressure
            for (const file of filesToProcess) {
                // Track current file for progress display
                this.currentProcessingFile = file.name;

                // Show file-specific progress
                const progressText = document.getElementById('progressText');
                if (progressText) {
                    progressText.textContent = `Processing file: ${file.name}`;
                }

                // Process file and immediately aggregate metrics
                await this.processFileWithMemoryOptimization(file);

                // Mark file as processed after successful processing
                if (window.dataProcessor) {
                    const fileId = window.dataProcessor.markFileAsProcessed(file);

                    // Get the actual transaction data for this file
                    const fileMetrics = this.fileMetrics.get(file.name);
                    const actualTransactionData = this.getTransactionDataForFile(file.name);

                    // Dispatch event for TTR monitoring with actual transaction data
                    // Include processing session ID to prevent duplicate processing
                    const event = new CustomEvent('fileProcessed', {
                        detail: {
                            transactions: actualTransactionData || [],
                            fileId: fileId,
                            fileName: file.name,
                            fileSize: file.size,
                            metrics: fileMetrics,
                            processingSessionId: this.currentProcessingSession,
                            timestamp: Date.now()
                        }
                    });
                    console.log(`🔔 Dispatching fileProcessed event for ${file.name}`, event.detail);
                    document.dispatchEvent(event);

                    console.log(`TTR Event dispatched for file ${file.name} with ${actualTransactionData ? actualTransactionData.length : 0} transactions (Session: ${this.currentProcessingSession})`);
                }

                // Force garbage collection if possible
                if (window.gc) {
                    window.gc();
                } else {
                    // Attempt to trigger garbage collection indirectly
                    const arr = new Array(100).fill('x').map(() => new Array(1000000).join('x'));
                    arr.length = 0;
                }
            }

            // Calculate total processed rows
            const totalProcessedRows = this.processedData.reduce((total, file) => {
                return total + (file.processedRowCount || 0);
            }, 0);

            // Update UI with processed data
            this.updateDashboard();

            // Ensure the final transaction count is accurate
            if (window.dataProcessor) {
                const currentMetrics = window.dataProcessor.getSummaryMetrics();

                // Update metrics with unique serial counts
                const metricsUpdate = {
                    hocUniqueSerialCount: this.hocSerialNumbers.size,
                    ibdUniqueSerialCount: this.ibdSerialNumbers.size,
                    wuUniqueSerialCount: this.wuSerialNumbers ? this.wuSerialNumbers.size : 0,
                    totalUniqueSerialCount: this.allSerialNumbers ? this.allSerialNumbers.size :
                        (this.hocSerialNumbers.size + this.ibdSerialNumbers.size + (this.wuSerialNumbers ? this.wuSerialNumbers.size : 0))
                };

                if (currentMetrics.totalTransactions < totalProcessedRows) {
                    console.log(`Final transaction count fix: ${currentMetrics.totalTransactions} → ${totalProcessedRows}`);
                    console.log(`Unique serial counts - HOC: ${metricsUpdate.hocUniqueSerialCount}, IBD: ${metricsUpdate.ibdUniqueSerialCount}, WU: ${metricsUpdate.wuUniqueSerialCount}, Total across all types: ${metricsUpdate.totalUniqueSerialCount}`);
                    window.dataProcessor.fixTransactionCount(totalProcessedRows, metricsUpdate);
                } else {
                    // Just update the unique serial counts
                    window.dataProcessor.updateMetrics(metricsUpdate);
                }
            }

            // Show success message
            if (window.app) {
                window.app.showNotification(`Successfully processed ${this.files.length} files with ${totalProcessedRows} transactions`, 'success');
            }

        } catch (error) {
            console.error('Error processing files:', error);
            if (window.app) {
                window.app.showNotification('An error occurred while processing the files. Please try again.', 'error');
            } else {
                alert('An error occurred while processing the files. Please try again.');
            }
        } finally {
            // Ensure processing state is always reset
            this.isProcessing = false;
            const sessionId = this.currentProcessingSession;
            this.currentProcessingSession = null;
            console.log(`File processing completed for session ${sessionId}, isProcessing reset to false`);

            // Cleanup responsiveness handling
            this.cleanupResponsivenessHandling();

            // Clear current file tracking
            this.currentProcessingFile = null;

            // Hide progress UI after a short delay with smooth transition
            setTimeout(() => {
                this.hideProgressBar();

                // Update the submit button state after processing
                this.updateSubmitButtonVisibility();

                // Dispatch completion event with session ID
                document.dispatchEvent(new CustomEvent('fileProcessingEnd', {
                    detail: { processingSessionId: sessionId }
                }));
            }, 1000);
        }
    }

    // Process a file with true streaming to minimize memory usage
    async processFileWithMemoryOptimization(file) {
        return new Promise((resolve, reject) => {
            try {
                // Validate file before processing
                if (!file || !file.size) {
                    throw new Error('Invalid file: File is empty or corrupted');
                }

                // Check file size limits
                if (file.size > APP_CONFIG?.FILE_PROCESSING?.MAX_FILE_SIZE || file.size > 3 * 1024 * 1024 * 1024) {
                    throw new Error(`File too large: ${this.formatBytes(file.size)}. Maximum allowed: 3GB`);
                }

                // Use streaming approach for large files
                const streamingThreshold = APP_CONFIG?.FILE_PROCESSING?.STREAMING_THRESHOLD || 50 * 1024 * 1024;
                if (file.size > streamingThreshold) {
                    this.processFileWithStreaming(file)
                        .then(resolve)
                        .catch(error => {
                            console.error('Streaming processing failed:', error);
                            reject(new Error(`Streaming processing failed: ${error.message}`));
                        });
                    return;
                }
            } catch (error) {
                console.error('File validation failed:', error);
                reject(error);
                return;
            }

            // Initialize file-specific metrics
            const fileSpecificMetrics = {
                totalTransactions: 0,
                totalAmount: 0,
                hocCount: 0,
                hocAmount: 0,
                hocAmountMMK: 0,
                hocAmountUSD: 0,
                hocCreditCount: 0,
                hocCreditAmount: 0,
                hocCreditAmountMMK: 0,
                hocCreditAmountUSD: 0,
                hocDebitCount: 0,
                hocDebitAmount: 0,
                hocDebitAmountMMK: 0,
                hocDebitAmountUSD: 0,
                ibdCount: 0,
                ibdAmount: 0,
                ibdAmountMMK: 0,
                ibdAmountUSD: 0,
                ibdCreditCount: 0,
                ibdCreditAmount: 0,
                ibdCreditAmountMMK: 0,
                ibdCreditAmountUSD: 0,
                ibdDebitCount: 0,
                ibdDebitAmount: 0,
                ibdDebitAmountMMK: 0,
                ibdDebitAmountUSD: 0,
                wuCount: 0,
                wuAmount: 0,
                wuAmountMMK: 0,
                wuAmountUSD: 0,
                wuCreditCount: 0,
                wuCreditAmount: 0,
                wuCreditAmountMMK: 0,
                wuCreditAmountUSD: 0,
                wuDebitCount: 0,
                wuDebitAmount: 0,
                wuDebitAmountMMK: 0,
                wuDebitAmountUSD: 0,
                hocUniqueSerialCount: 0, // Will be calculated at the end
                ibdUniqueSerialCount: 0, // Will be calculated at the end
                wuUniqueSerialCount: 0, // Will be calculated at the end
                currencyCounts: {
                    USD: 0,
                    MMK: 0
                },
                currencyAmounts: {
                    USD: 0,
                    MMK: 0
                },
                currencyCreditAmounts: {
                    USD: 0,
                    MMK: 0
                },
                currencyDebitAmounts: {
                    USD: 0,
                    MMK: 0
                },
                highValueTransactionCount: 0,  // Count of transactions > 1B MMK
                highValueTransactionCountUSD: 0  // Count of transactions > 10K USD
            };

            // Initialize file-specific date data for table tracking
            const fileSpecificDateData = {};

            // Store file data for reference
            const fileData = {
                fileName: file.name,
                rowCount: 0,
                sampleRows: [], // Keep a few sample rows for display purposes
                processedRowCount: 0, // Track actual processed rows for debugging
                highValueTransactions: [], // Store high-value MMK transactions for date breakdown
                highValueTransactionsUSD: [] // Store high-value USD transactions for date breakdown
            };

            // Set up streaming with smaller chunks for better memory management
            const CHUNK_SIZE = 512 * 1024; // 512KB chunks
            const reader = new FileReader();
            let offset = 0;
            let header = null;
            let lastPartialLine = '';

            // Function to read the next chunk
            const readNextChunk = () => {
                const slice = file.slice(offset, offset + CHUNK_SIZE);
                reader.readAsText(slice);
            };

            // Handle chunk load
            reader.onload = (e) => {
                try {
                    // Process the chunk
                    const chunk = e.target.result;

                    // Combine with any partial line from previous chunk
                    const combinedText = lastPartialLine + chunk;

                    // Split into lines, but handle the last line carefully
                    const lines = combinedText.split('\n');

                    // The last line might be incomplete if we're not at the end of the file
                    if (offset + CHUNK_SIZE < file.size) {
                        lastPartialLine = lines.pop() || '';
                    } else {
                        lastPartialLine = '';
                    }

                    // If this is the first chunk, extract the header
                    const isFirstChunk = offset === 0;
                    if (isFirstChunk) {
                        header = this.parseCSVLine(lines[0]);
                        console.log(`File header: ${header.join(', ')}`);
                    }

                    // Count valid lines in this chunk (for debugging)
                    let validLinesInChunk = 0;

                    // Process sample rows for display (can be kept for UI purposes if needed)
                    if (isFirstChunk && lines.length > 1) {
                        for (let i = 1; i < Math.min(10, lines.length); i++) {
                            if (lines[i].trim() !== '') {
                                const rowData = this.parseCSVLine(lines[i]);
                                if (rowData.length >= header.length) {
                                    const rowObject = {};
                                    header.forEach((key, index) => {
                                        rowObject[key] = rowData[index] || '';
                                    });
                                    if (rowObject.REPORTTYPE) {
                                        fileData.sampleRows.push(rowObject);
                                    }
                                }
                            }
                        }
                    }

                    // Process the chunk and get chunk-specific metrics
                    const chunkMetricsResult = this.processChunkForMetrics(lines, header, isFirstChunk, fileData.fileName, fileData);
                    validLinesInChunk += chunkMetricsResult.processedRows;

                    // Accumulate chunk metrics into fileSpecificMetrics
                    this.accumulateMetrics(fileSpecificMetrics, chunkMetricsResult.metrics);

                    // Accumulate chunk date data into fileSpecificDateData
                    this.accumulateDateData(fileSpecificDateData, chunkMetricsResult.dateData);

                    // Accumulate transaction data for TTR analysis
                    if (chunkMetricsResult.transactionData && chunkMetricsResult.transactionData.length > 0) {
                        if (!this.fileTransactionData.has(file.name)) {
                            this.fileTransactionData.set(file.name, []);
                        }
                        this.fileTransactionData.get(file.name).push(...chunkMetricsResult.transactionData);

                        // PRODUCTION FIX: Sync chunk data to DataProcessor immediately (but skip TTR updates during streaming)
                        this.syncTransactionDataToDataProcessor(chunkMetricsResult.transactionData, file.name, true); // true = skip TTR during streaming
                    }


                    // Use worker if available, otherwise use main thread processing
                    if (this.worker) {
                        try {
                            this.worker.postMessage({
                                action: 'processChunk',
                                data: {
                                    chunk: combinedText,
                                    header: header,
                                    isFirstChunk: isFirstChunk,
                                    fileName: file.name
                                }
                            });
                        } catch (workerError) {
                            console.error('Error sending data to worker, falling back to main thread:', workerError);
                            // Disable worker and fall back to main thread
                            this.worker = null;
                            this.processChunkFallback(combinedText, header, isFirstChunk, file.name);
                        }
                    } else {
                        // Use main thread processing as fallback
                        this.processChunkFallback(combinedText, header, isFirstChunk, file.name);
                    }

                    // Update row count with actual processed rows
                    fileData.rowCount += lines.length - (isFirstChunk ? 1 : 0); // total lines read
                    fileData.processedRowCount += validLinesInChunk; // valid transactions processed

                    // Update progress
                    offset += CHUNK_SIZE;
                    this.processedSize += chunk.length;
                    this.updateProgress();

                    // Check if there's more to read
                    if (offset < file.size) {
                        // Use setTimeout to prevent call stack overflow and allow GC
                        setTimeout(readNextChunk, 0);
                    } else {
                        // Store processed data with accurate counts
                        this.processedData.push({
                            fileName: fileData.fileName,
                            rowCount: fileData.rowCount,
                            processedRowCount: fileData.processedRowCount,
                            sampleRows: fileData.sampleRows // Sample rows are still useful for quick display if needed
                        });

                        // Finalize HOC and IBD unique serial counts for the file
                        // These sets (this.fileHocSerialNumbers and this.fileIbdSerialNumbers)
                        // should be temporary and specific to this file processing instance.
                        // Let's create them here before storing.
                        const fileHocSerialNumbers = new Set();
                        const fileIbdSerialNumbers = new Set();
                        // This requires iterating through the file's rows again or collecting serials during chunk processing.
                        // For now, we'll assume this.updateMetricsWithRow (called by processChunkForMetrics)
                        // correctly updates global serial number sets (this.hocSerialNumbers, this.ibdSerialNumbers).
                        // We need a way to get serial numbers *only* for the current file.

                        // The most straightforward way is to process rows and collect serials specifically for this file.
                        // The existing this.updateMetricsWithRow updates global sets.
                        // We need a version that updates file-local sets for fileSpecificMetrics.
                        // This is already handled by processChunkForMetrics and accumulateMetrics.
                        // The serial numbers are collected in fileSpecificMetrics.hocSerialNumbers (Set) and fileSpecificMetrics.ibdSerialNumbers (Set)
                        // by the accumulateMetrics function.

                        // Update unique serial counts in fileSpecificMetrics from the sets
                        fileSpecificMetrics.hocUniqueSerialCount = fileSpecificMetrics.hocSerialNumbers ? fileSpecificMetrics.hocSerialNumbers.size : 0;
                        fileSpecificMetrics.ibdUniqueSerialCount = fileSpecificMetrics.ibdSerialNumbers ? fileSpecificMetrics.ibdSerialNumbers.size : 0;
                        fileSpecificMetrics.wuUniqueSerialCount = fileSpecificMetrics.wuSerialNumbers ? fileSpecificMetrics.wuSerialNumbers.size : 0;


                        // Store the fully accumulated fileSpecificMetrics
                        this.fileMetrics.set(file.name, fileSpecificMetrics);

                        // Store the serial numbers specific to this file
                        // These are already part of fileSpecificMetrics if collected correctly
                        this.fileSerialNumbers.set(file.name, {
                             hoc: fileSpecificMetrics.hocSerialNumbers || new Set(),
                             ibd: fileSpecificMetrics.ibdSerialNumbers || new Set(),
                             wu: fileSpecificMetrics.wuSerialNumbers || new Set()
                        });

                        // Store the date data specific to this file for table synchronization
                        this.fileDateData.set(file.name, fileSpecificDateData);


                        // Update global metrics in DataProcessor with the metrics from this file
                        if (window.dataProcessor) {
                            // Mark file as processed and get file ID
                            const fileId = window.dataProcessor.markFileAsProcessed(file);

                            // Update metrics
                            window.dataProcessor.updateMetrics(fileSpecificMetrics);

                            // Add file-specific high-value transaction data for proper file management
                            if (fileSpecificMetrics.highValueTransactionCount > 0 && fileData.highValueTransactions) {
                                console.log(`Adding ${fileData.highValueTransactions.length} high-value MMK transactions for file: ${file.name} (ID: ${fileId})`);
                                window.dataProcessor.addFileHighValueTransactions(fileId, file.name, fileData.highValueTransactions);
                                window.dataProcessor.updateHighValueFileBreakdownCombined();
                                console.log('Combined date breakdown updated with MMK file-specific data');
                            } else if (fileSpecificMetrics.highValueTransactionCount > 0) {
                                console.warn(`High-value MMK transactions detected (${fileSpecificMetrics.highValueTransactionCount}) but no transaction data collected for file: ${file.name}`);
                            }

                            // Add file-specific high-value USD transaction data for proper file management
                            if (fileSpecificMetrics.highValueTransactionCountUSD > 0 && fileData.highValueTransactionsUSD) {
                                console.log(`Adding ${fileData.highValueTransactionsUSD.length} high-value USD transactions for file: ${file.name} (ID: ${fileId})`);
                                window.dataProcessor.addFileHighValueTransactionsUSD(fileId, file.name, fileData.highValueTransactionsUSD);
                                window.dataProcessor.updateHighValueFileBreakdownCombined();
                                console.log('Combined date breakdown updated with USD file-specific data');
                            } else if (fileSpecificMetrics.highValueTransactionCountUSD > 0) {
                                console.warn(`High-value USD transactions detected (${fileSpecificMetrics.highValueTransactionCountUSD}) but no transaction data collected for file: ${file.name}`);
                            }
                        }

                        console.log(`Stored accurate metrics for file: ${file.name}`, fileSpecificMetrics);
                        console.log(`File ${fileData.fileName} processed: ${fileData.processedRowCount} valid rows out of ${fileData.rowCount} total rows`);

                        // Ensure the transaction count is accurate in the UI
                        if (window.dataProcessor) {
                            const currentMetrics = window.dataProcessor.getSummaryMetrics();
                            const totalProcessedRows = this.processedData.reduce((total, file) => {
                                return total + (file.processedRowCount || 0);
                            }, 0);

                            // If the current transaction count is less than the total processed rows,
                            // update it to reflect the actual count
                            if (currentMetrics.totalTransactions < totalProcessedRows) {
                                console.log(`Updating transaction count during file processing: ${currentMetrics.totalTransactions} → ${totalProcessedRows}`);
                                window.dataProcessor.fixTransactionCount(totalProcessedRows);
                            }
                        }

                        resolve();
                    }
                } catch (error) {
                    console.error('Error processing chunk:', error);
                    reject(error);
                }
            };

            // Handle errors
            reader.onerror = (error) => {
                reject(new Error(`Error reading file: ${file.name} - ${error}`));
            };

            // Start reading the first chunk
            readNextChunk();
        });
    }

    // Process a file with true streaming to minimize memory usage
    async processFileWithStreaming(file) {
        return new Promise((resolve, reject) => {
            console.log(`Processing large file with streaming: ${file.name} (${this.formatBytes(file.size)})`);

            // Initialize streaming metrics
            const streamingMetrics = {
                totalTransactions: 0,
                totalAmount: 0,
                hocCount: 0,
                hocAmount: 0,
                hocAmountMMK: 0,
                hocAmountUSD: 0,
                hocCreditCount: 0,
                hocCreditAmount: 0,
                hocCreditAmountMMK: 0,
                hocCreditAmountUSD: 0,
                hocDebitCount: 0,
                hocDebitAmount: 0,
                hocDebitAmountMMK: 0,
                hocDebitAmountUSD: 0,
                ibdCount: 0,
                ibdAmount: 0,
                ibdAmountMMK: 0,
                ibdAmountUSD: 0,
                ibdCreditCount: 0,
                ibdCreditAmount: 0,
                ibdCreditAmountMMK: 0,
                ibdCreditAmountUSD: 0,
                ibdDebitCount: 0,
                ibdDebitAmount: 0,
                ibdDebitAmountMMK: 0,
                ibdDebitAmountUSD: 0,
                wuCount: 0,
                wuAmount: 0,
                wuAmountMMK: 0,
                wuAmountUSD: 0,
                wuCreditCount: 0,
                wuCreditAmount: 0,
                wuCreditAmountMMK: 0,
                wuCreditAmountUSD: 0,
                wuDebitCount: 0,
                wuDebitAmount: 0,
                wuDebitAmountMMK: 0,
                wuDebitAmountUSD: 0,
                hocUniqueSerialCount: 0,
                ibdUniqueSerialCount: 0,
                wuUniqueSerialCount: 0,
                currencyCounts: { USD: 0, MMK: 0 },
                currencyAmounts: { USD: 0, MMK: 0 },
                currencyCreditAmounts: { USD: 0, MMK: 0 },
                currencyDebitAmounts: { USD: 0, MMK: 0 },
                highValueTransactionCount: 0,
                highValueTransactionCountUSD: 0,
                hocSerialNumbers: new Set(),
                ibdSerialNumbers: new Set(),
                wuSerialNumbers: new Set()
            };

            const streamingDateData = {};
            const highValueTransactions = []; // Keep for existing high-value logic if needed elsewhere
            const highValueTransactionsUSD = []; // Collect high-value USD transactions
            const sampleTransactions = [];
            const allTransactionsForFile = []; // Added to collect all transactions for TTR

            // Generate a temporary fileId for streaming processing
            const tempFileId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            let header = null;
            let lineBuffer = '';
            let processedRows = 0;
            let totalRows = 0;
            let offset = 0;
            let batchBuffer = [];

            const reader = new FileReader();

            // Memory monitoring
            const checkMemoryPressure = () => {
                if (window.performance && window.performance.memory) {
                    const memInfo = window.performance.memory;
                    const usageRatio = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;
                    return usageRatio > this.streamingConfig.gcThreshold;
                }
                return false;
            };

            // Process a batch of records
            const processBatch = (records) => {
                for (const record of records) {
                    if (!record.REPORTTYPE) continue;

                    processedRows++;
                    allTransactionsForFile.push(record); // Collect record for TTR

                    // Update metrics incrementally
                    this.updateMetricsWithRow(record, streamingMetrics, true, {
                        highValueTransactions,
                        highValueTransactionsUSD
                    }, file.name, tempFileId); // Pass both high-value transaction collectors and file info
                    this.updateDateData(record, streamingDateData);

                    // Sample collection (keep only a small percentage)
                    if (Math.random() < this.streamingConfig.sampleRate) {
                        sampleTransactions.push(record);
                    }
                }

                // Clear batch buffer to free memory
                batchBuffer.length = 0;

                // Force garbage collection if memory pressure is high
                if (checkMemoryPressure()) {
                    this.triggerGarbageCollection();
                }
            };

            // Read next chunk
            const readNextChunk = () => {
                const slice = file.slice(offset, offset + this.chunkSize);
                reader.readAsText(slice);
            };

            reader.onload = (e) => {
                try {
                    const chunk = e.target.result;
                    const slice = file.slice(offset, offset + this.chunkSize); // Get the slice again to access its size
                    lineBuffer += chunk;

                    // Split into lines
                    const lines = lineBuffer.split('\n');

                    // Keep the last incomplete line in buffer
                    if (offset + this.chunkSize < file.size) {
                        lineBuffer = lines.pop() || '';
                    } else {
                        lineBuffer = '';
                    }

                    // Process header if this is the first chunk
                    if (header === null && lines.length > 0) {
                        header = this.parseCSVLine(lines[0]);
                        lines.shift(); // Remove header from processing
                        console.log(`Streaming header: ${header.join(', ')}`);
                    }

                    // Process lines in batches
                    for (const line of lines) {
                        if (line.trim() === '') continue;

                        totalRows++;
                        const rowData = this.parseCSVLine(line);

                        if (rowData.length >= header.length) {
                            const rowObject = {};
                            header.forEach((key, index) => {
                                rowObject[key] = rowData[index] || '';
                            });

                            batchBuffer.push(rowObject);

                            // Process batch when it reaches the configured size
                            if (batchBuffer.length >= this.streamingConfig.batchSize) {
                                processBatch(batchBuffer);
                            }
                        }
                    }

                    // Update progress
                    // Use slice.size for a more accurate byte count
                    this.processedSize += slice.size;
                    this.updateProgress(); // Call updateProgress to update the visual bar

                    const progress = Math.round((this.processedSize / this.totalSize) * 100);
                    document.getElementById('progressText').textContent =
                        `${progress}% - Streaming ${file.name}: ${processedRows} records processed`;

                    offset += this.chunkSize; // Note: offset is advanced by this.chunkSize

                    // Continue reading or finish
                    if (offset < file.size) {
                        setTimeout(readNextChunk, 0); // Yield control to prevent blocking
                    } else {
                        // Process remaining batch
                        if (batchBuffer.length > 0) {
                            processBatch(batchBuffer);
                        }

                        // Finalize processing
                        this.finalizeStreamingProcessing(file, streamingMetrics, streamingDateData,
                            highValueTransactions, highValueTransactionsUSD, sampleTransactions, processedRows, totalRows, allTransactionsForFile); // Pass allTransactionsForFile
                        resolve();
                    }
                } catch (error) {
                    console.error('Error in streaming chunk processing:', error);
                    reject(error);
                }
            };

            reader.onerror = (error) => {
                reject(new Error(`Error reading file: ${file.name} - ${error}`));
            };

            // Start reading
            readNextChunk();
        });
    }

    // Finalize streaming processing
    finalizeStreamingProcessing(file, metrics, dateData, highValueTransactions, highValueTransactionsUSD, sampleTransactions, processedRows, totalRows, allTransactionsForFile) { // Added allTransactionsForFile
        // Update unique serial counts
        metrics.hocUniqueSerialCount = metrics.hocSerialNumbers.size;
        metrics.ibdUniqueSerialCount = metrics.ibdSerialNumbers.size;
        metrics.wuUniqueSerialCount = metrics.wuSerialNumbers ? metrics.wuSerialNumbers.size : 0;

        // Store processed data with minimal memory footprint
        this.processedData.push({
            fileName: file.name,
            rowCount: totalRows,
            processedRowCount: processedRows,
            sampleRows: sampleTransactions.slice(0, 100) // Keep only 100 samples max
        });

        // CRITICAL FIX: Reset TTR retry counter when new files are processed
        if (window.dataProcessor && typeof window.dataProcessor.resetTTRRetryCounter === 'function') {
            window.dataProcessor.resetTTRRetryCounter();
        }

        // Store metrics
        this.fileMetrics.set(file.name, metrics);

        // Store serial numbers
        this.fileSerialNumbers.set(file.name, {
            hoc: metrics.hocSerialNumbers,
            ibd: metrics.ibdSerialNumbers,
            wu: metrics.wuSerialNumbers || new Set()
        });

        // Store date data
        this.fileDateData.set(file.name, dateData);

        // Store all transactions for TTR
        if (allTransactionsForFile) {
            this.fileTransactionData.set(file.name, allTransactionsForFile);
            console.log(`Stored ${allTransactionsForFile.length} transactions for TTR from streaming file: ${file.name}`);

            // PRODUCTION FIX: Immediately sync transaction data to DataProcessor (file is complete, allow TTR updates)
            this.syncTransactionDataToDataProcessor(allTransactionsForFile, file.name, false); // false = allow TTR updates
        } else {
            console.warn(`No transaction data available for TTR from file: ${file.name}`);
        }

        // Update global metrics
        if (window.dataProcessor) {
            const fileId = window.dataProcessor.markFileAsProcessed(file);
            window.dataProcessor.updateMetrics(metrics);

            if (highValueTransactions.length > 0) {
                console.log(`Adding ${highValueTransactions.length} high-value MMK transactions for streaming file: ${file.name}`);
                window.dataProcessor.addFileHighValueTransactions(fileId, file.name, highValueTransactions);
                window.dataProcessor.updateHighValueFileBreakdownCombined();
            }

            if (highValueTransactionsUSD.length > 0) {
                console.log(`Adding ${highValueTransactionsUSD.length} high-value USD transactions for streaming file: ${file.name}`);
                window.dataProcessor.addFileHighValueTransactionsUSD(fileId, file.name, highValueTransactionsUSD);
                window.dataProcessor.updateHighValueFileBreakdownCombined();
            }

            // MISSING: Dispatch fileProcessed event for streaming processing path
            const event = new CustomEvent('fileProcessed', {
                detail: {
                    transactions: allTransactionsForFile || [],
                    fileId: fileId,
                    fileName: file.name,
                    fileSize: file.size,
                    metrics: metrics,
                    processingSessionId: this.currentProcessingSession,
                    timestamp: Date.now(),
                    source: 'streaming'
                }
            });
            console.log(`🔔 Dispatching fileProcessed event for streaming file ${file.name}`, event.detail);
            document.dispatchEvent(event);
        }



        // Clear large objects to free memory
        metrics.hocSerialNumbers = null;
        metrics.ibdSerialNumbers = null;
        metrics.wuSerialNumbers = null;

        console.log(`Streaming processing completed for ${file.name}: ${processedRows}/${totalRows} records processed`);

        // Trigger a delayed TTR update to ensure all data is synchronized
        setTimeout(() => {
            console.log(`🔄 Final TTR update check for ${file.name}...`);
            console.log(`   - processedData: ${this.processedData.length}, fileMetrics: ${this.fileMetrics.size}`);

            if (this.isTTRDataReady()) {
                console.log('✅ TTR data ready - triggering final update after streaming completion...');
                this.forceTTRSummaryUpdate();
            } else {
                console.warn('⚠️ TTR data still not ready after file completion - will retry');
                // One more retry after additional delay
                setTimeout(() => {
                    if (this.isTTRDataReady()) {
                        console.log('✅ TTR data ready on retry - triggering update...');
                        this.forceTTRSummaryUpdate();
                    } else {
                        console.error('❌ TTR data still not ready after final retry');
                    }
                }, 500);
            }
        }, 500); // Reduced delay since processedData is already available
    }

    // Trigger garbage collection
    triggerGarbageCollection() {
        // Create and destroy temporary objects to encourage GC
        const temp = new Array(1000).fill(null).map(() => ({ data: new Array(1000).join('x') }));
        temp.length = 0;

        console.log('Triggered garbage collection due to memory pressure');
    }

    // Format bytes to human-readable format
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Comprehensive file validation for security
    validateFiles(files) {
        const validFiles = [];
        const errors = [];
        let totalSize = 0;

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const validation = this.validateSingleFile(file);

            if (validation.isValid) {
                validFiles.push(file);
                totalSize += file.size;
            } else {
                errors.push(`${file.name}: ${validation.error}`);
            }
        }

        // Check total size limit
        if (totalSize > this.securityConfig.maxTotalSize) {
            errors.push(`Total file size (${this.formatBytes(totalSize)}) exceeds limit (${this.formatBytes(this.securityConfig.maxTotalSize)})`);
            return { validFiles: [], errors };
        }

        return { validFiles, errors };
    }

    // Validate a single file for security issues
    validateSingleFile(file) {
        // Check if file object is valid
        if (!file || typeof file !== 'object') {
            return { isValid: false, error: 'Invalid file object' };
        }

        // Check filename length
        if (file.name.length > this.securityConfig.maxFilenameLength) {
            return { isValid: false, error: `Filename too long (max ${this.securityConfig.maxFilenameLength} characters)` };
        }

        // Check for dangerous filename patterns
        for (const pattern of this.securityConfig.dangerousPatterns) {
            if (pattern.test(file.name)) {
                return { isValid: false, error: 'Filename contains invalid characters or patterns' };
            }
        }

        // Check file extension
        const extension = this.getFileExtension(file.name);
        if (!this.securityConfig.allowedExtensions.includes(extension)) {
            return { isValid: false, error: `File type not allowed. Only ${this.securityConfig.allowedExtensions.join(', ')} files are permitted` };
        }

        // Check MIME type if available
        if (file.type && !this.securityConfig.allowedMimeTypes.includes(file.type)) {
            return { isValid: false, error: `MIME type '${file.type}' not allowed` };
        }

        // Check file size
        if (file.size > this.securityConfig.maxFileSize) {
            return { isValid: false, error: `File size (${this.formatBytes(file.size)}) exceeds limit (${this.formatBytes(this.securityConfig.maxFileSize)})` };
        }

        // Check for empty files
        if (file.size === 0) {
            return { isValid: false, error: 'Empty files are not allowed' };
        }

        return { isValid: true };
    }

    // Get file extension safely
    getFileExtension(filename) {
        const lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex === -1) {
            return '';
        }
        return filename.substring(lastDotIndex).toLowerCase();
    }

    // PRODUCTION FIX: Sync transaction data to DataProcessor immediately after processing
    syncTransactionDataToDataProcessor(transactionData, fileName, skipTTRDuringStreaming = false) {
        console.log(`🔄 Syncing ${transactionData.length} transactions from ${fileName} to DataProcessor... (skipTTR: ${skipTTRDuringStreaming})`);

        if (!window.dataProcessor) {
            console.warn('⚠️ DataProcessor not available for immediate sync');
            return false;
        }

        // Get existing raw data or initialize empty array
        let existingData = window.dataProcessor.rawData || [];

        // Add new transaction data
        existingData.push(...transactionData);

        // Update DataProcessor with combined data
        window.dataProcessor.rawData = existingData;

        console.log(`✅ DataProcessor now has ${existingData.length} total transactions`);

        // Force recalculation of metrics
        if (typeof window.dataProcessor.recalculateCurrencyMetrics === 'function') {
            window.dataProcessor.recalculateCurrencyMetrics();
            console.log('✅ Currency metrics recalculated');
        }

        // Update UI with proper sequencing to ensure data availability
        if (typeof window.dataProcessor.updateCurrencyBreakdown === 'function') {
            setTimeout(() => {
                window.dataProcessor.updateCurrencyBreakdown();
                console.log('✅ Currency breakdown updated');
            }, 100);
        }

        // Only update TTR Summary Report if not during streaming or if explicitly allowed
        if (!skipTTRDuringStreaming && typeof window.dataProcessor.updateTTRSummaryReport === 'function') {
            setTimeout(() => {
                // Ensure processed data is available before updating TTR
                const hasProcessedData = this.processedData.length > 0;
                const hasFileMetrics = this.fileMetrics.size > 0;

                if (hasProcessedData && hasFileMetrics) {
                    console.log(`✅ TTR data validation passed: ${this.processedData.length} files, ${this.fileMetrics.size} metrics`);
                    window.dataProcessor.updateTTRSummaryReport();
                    console.log('✅ TTR Summary Report updated');
                } else {
                    console.warn(`⚠️ TTR data not ready: processedData=${this.processedData.length}, fileMetrics=${this.fileMetrics.size}`);
                    // Retry after a short delay
                    setTimeout(() => {
                        if (this.processedData.length > 0 && this.fileMetrics.size > 0) {
                            console.log('🔄 Retrying TTR Summary Report update...');
                            window.dataProcessor.updateTTRSummaryReport();
                            console.log('✅ TTR Summary Report updated (retry)');
                        } else {
                            console.error('❌ TTR data still not available after retry');
                        }
                    }, 200);
                }
            }, 200); // Increased delay to ensure data is ready
        } else if (skipTTRDuringStreaming) {
            console.log('⏭️ Skipping TTR update during streaming - will update after file completion');
        }

        return true;
    }

    // Manually sync all transaction data to DataProcessor
    syncTransactionDataToProcessor() {
        console.log('🔄 Manually syncing transaction data to DataProcessor...');

        if (!window.dataProcessor) {
            console.error('❌ DataProcessor not available');
            return false;
        }

        let allTransactions = [];
        let totalFiles = 0;

        // Collect all transaction data from fileTransactionData
        this.fileTransactionData.forEach((transactions, fileName) => {
            if (transactions && transactions.length > 0) {
                allTransactions.push(...transactions);
                totalFiles++;
                console.log(`📁 Added ${transactions.length} transactions from ${fileName}`);
            }
        });

        // If no full transaction data, try to use sample data
        if (allTransactions.length === 0) {
            console.log('⚠️ No full transaction data found, attempting to use sample data...');
            this.processedData.forEach(file => {
                if (file.sampleRows && file.sampleRows.length > 0) {
                    allTransactions.push(...file.sampleRows);
                    totalFiles++;
                    console.log(`📁 Added ${file.sampleRows.length} sample transactions from ${file.fileName}`);
                }
            });
        }

        if (allTransactions.length > 0) {
            console.log(`✅ Syncing ${allTransactions.length} transactions from ${totalFiles} files to DataProcessor`);
            window.dataProcessor.processData(allTransactions);

            // Force recalculation of currency metrics
            if (typeof window.dataProcessor.recalculateCurrencyMetrics === 'function') {
                window.dataProcessor.recalculateCurrencyMetrics();
            }

            // Update UI
            if (typeof window.dataProcessor.updateCurrencyBreakdown === 'function') {
                setTimeout(() => {
                    window.dataProcessor.updateCurrencyBreakdown();
                }, 500);
            }

            // Update TTR Summary Report with data validation
            if (typeof window.dataProcessor.updateTTRSummaryReport === 'function') {
                setTimeout(() => {
                    // Ensure processed data is available before updating TTR
                    const hasProcessedData = this.processedData.length > 0;
                    const hasFileMetrics = this.fileMetrics.size > 0;

                    if (hasProcessedData && hasFileMetrics) {
                        console.log(`✅ TTR bulk sync validation passed: ${this.processedData.length} files, ${this.fileMetrics.size} metrics`);
                        window.dataProcessor.updateTTRSummaryReport();
                        console.log('✅ TTR Summary Report updated (bulk sync)');
                    } else {
                        console.warn(`⚠️ TTR bulk sync data not ready: processedData=${this.processedData.length}, fileMetrics=${this.fileMetrics.size}`);
                    }
                }, 600); // Increased delay for bulk operations
            }

            console.log('🎉 Transaction data sync completed successfully!');
            return true;
        } else {
            console.error('❌ No transaction data available to sync');
            return false;
        }
    }

    // Get transaction data for a specific file (for TTR analysis)
    getTransactionDataForFile(fileName) {
        return this.fileTransactionData.get(fileName) || [];
    }

    // Check if TTR data is ready for processing
    isTTRDataReady() {
        const hasProcessedData = this.processedData.length > 0;
        const hasFileMetrics = this.fileMetrics.size > 0;
        const hasTransactionData = this.fileTransactionData.size > 0;

        console.log(`TTR Data Ready Check: processedData=${this.processedData.length}, fileMetrics=${this.fileMetrics.size}, transactionData=${this.fileTransactionData.size}`);

        return hasProcessedData && hasFileMetrics;
    }

    // Force TTR Summary update with data validation
    forceTTRSummaryUpdate() {
        if (!window.dataProcessor || typeof window.dataProcessor.updateTTRSummaryReport !== 'function') {
            console.warn('DataProcessor or updateTTRSummaryReport not available');
            return false;
        }

        if (this.isTTRDataReady()) {
            console.log('🔄 Forcing TTR Summary update with validated data...');
            window.dataProcessor.updateTTRSummaryReport();
            console.log('✅ TTR Summary update completed');
            return true;
        } else {
            console.warn('⚠️ TTR data not ready for forced update');
            return false;
        }
    }

    // Renamed from processChunkInMainThread to processChunkForMetrics
    // This function now returns metrics for the chunk and does not update global state directly.
    processChunkForMetrics(lines, header, isFirstChunk, fileName, fileData = null) {
        // Initialize metrics for this specific chunk
        const chunkMetrics = {
            totalTransactions: 0,
            totalAmount: 0,
            hocCount: 0,
            hocAmount: 0,
            hocAmountMMK: 0,
            hocAmountUSD: 0,
            hocCreditCount: 0,
            hocCreditAmount: 0,
            hocCreditAmountMMK: 0,
            hocCreditAmountUSD: 0,
            hocDebitCount: 0,
            hocDebitAmount: 0,
            hocDebitAmountMMK: 0,
            hocDebitAmountUSD: 0,
            ibdCount: 0,
            ibdAmount: 0,
            ibdAmountMMK: 0,
            ibdAmountUSD: 0,
            ibdCreditCount: 0,
            ibdCreditAmount: 0,
            ibdCreditAmountMMK: 0,
            ibdCreditAmountUSD: 0,
            ibdDebitCount: 0,
            ibdDebitAmount: 0,
            ibdDebitAmountMMK: 0,
            ibdDebitAmountUSD: 0,
            wuCount: 0,
            wuAmount: 0,
            wuAmountMMK: 0,
            wuAmountUSD: 0,
            wuCreditCount: 0,
            wuCreditAmount: 0,
            wuCreditAmountMMK: 0,
            wuCreditAmountUSD: 0,
            wuDebitCount: 0,
            wuDebitAmount: 0,
            wuDebitAmountMMK: 0,
            wuDebitAmountUSD: 0,
            currencyCounts: { USD: 0, MMK: 0 },
            currencyAmounts: { USD: 0, MMK: 0 },
            currencyCreditAmounts: { USD: 0, MMK: 0 },
            currencyDebitAmounts: { USD: 0, MMK: 0 },
            highValueTransactionCount: 0, // Initialize high-value transaction count (MMK)
            highValueTransactionCountUSD: 0, // Initialize high-value transaction count (USD)
            // Temporary sets for unique serials within this chunk for this file
            hocSerialNumbers: new Set(),
            ibdSerialNumbers: new Set(),
            wuSerialNumbers: new Set()
        };

        const dateData = {}; // For table data, can still be updated globally or returned
        const transactionData = []; // Store actual transaction objects for TTR analysis
        const startIndex = isFirstChunk ? 1 : 0;
        let validRowsProcessed = 0;

        for (let i = startIndex; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line === '') continue;

            const rowData = this.parseCSVLine(line);
            if (rowData.length >= header.length) {
                const rowObject = {};
                header.forEach((key, index) => {
                    rowObject[key] = rowData[index] || '';
                });

                if (rowObject.REPORTTYPE) {
                    validRowsProcessed++;
                    // Store transaction data for TTR analysis
                    transactionData.push(rowObject);
                    // Update chunkMetrics with this row
                    this.updateMetricsWithRow(rowObject, chunkMetrics, true, fileData, fileName, null); // Pass true for isChunkOrFileSpecific and fileData
                    // Update date-based aggregation for table (can remain global for now)
                    this.updateDateData(rowObject, dateData);
                }
            }
        }

        if (validRowsProcessed > 0) {
            // console.log(`File ${fileName}: Processed chunk with ${validRowsProcessed} valid transactions`);
        }



        return {
            metrics: chunkMetrics,
            processedRows: validRowsProcessed,
            dateData: dateData,
            transactionData: transactionData
        };
    }

    // Helper function to accumulate metrics from a chunk into a target metrics object
    accumulateMetrics(targetMetrics, chunkMetrics) {
        const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);

        targetMetrics.totalTransactions = safeAdd(targetMetrics.totalTransactions, chunkMetrics.totalTransactions);
        targetMetrics.totalAmount = safeAdd(targetMetrics.totalAmount, chunkMetrics.totalAmount);
        targetMetrics.hocCount = safeAdd(targetMetrics.hocCount, chunkMetrics.hocCount);
        targetMetrics.hocAmount = safeAdd(targetMetrics.hocAmount, chunkMetrics.hocAmount);
        targetMetrics.hocAmountMMK = safeAdd(targetMetrics.hocAmountMMK, chunkMetrics.hocAmountMMK);
        targetMetrics.hocAmountUSD = safeAdd(targetMetrics.hocAmountUSD, chunkMetrics.hocAmountUSD);
        targetMetrics.hocCreditCount = safeAdd(targetMetrics.hocCreditCount, chunkMetrics.hocCreditCount);
        targetMetrics.hocCreditAmount = safeAdd(targetMetrics.hocCreditAmount, chunkMetrics.hocCreditAmount);
        targetMetrics.hocCreditAmountMMK = safeAdd(targetMetrics.hocCreditAmountMMK, chunkMetrics.hocCreditAmountMMK);
        targetMetrics.hocCreditAmountUSD = safeAdd(targetMetrics.hocCreditAmountUSD, chunkMetrics.hocCreditAmountUSD);
        targetMetrics.hocDebitCount = safeAdd(targetMetrics.hocDebitCount, chunkMetrics.hocDebitCount);
        targetMetrics.hocDebitAmount = safeAdd(targetMetrics.hocDebitAmount, chunkMetrics.hocDebitAmount);
        targetMetrics.hocDebitAmountMMK = safeAdd(targetMetrics.hocDebitAmountMMK, chunkMetrics.hocDebitAmountMMK);
        targetMetrics.hocDebitAmountUSD = safeAdd(targetMetrics.hocDebitAmountUSD, chunkMetrics.hocDebitAmountUSD);
        targetMetrics.ibdCount = safeAdd(targetMetrics.ibdCount, chunkMetrics.ibdCount);
        targetMetrics.ibdAmount = safeAdd(targetMetrics.ibdAmount, chunkMetrics.ibdAmount);
        targetMetrics.ibdAmountMMK = safeAdd(targetMetrics.ibdAmountMMK, chunkMetrics.ibdAmountMMK);
        targetMetrics.ibdAmountUSD = safeAdd(targetMetrics.ibdAmountUSD, chunkMetrics.ibdAmountUSD);
        targetMetrics.ibdCreditCount = safeAdd(targetMetrics.ibdCreditCount, chunkMetrics.ibdCreditCount);
        targetMetrics.ibdCreditAmount = safeAdd(targetMetrics.ibdCreditAmount, chunkMetrics.ibdCreditAmount);
        targetMetrics.ibdCreditAmountMMK = safeAdd(targetMetrics.ibdCreditAmountMMK, chunkMetrics.ibdCreditAmountMMK);
        targetMetrics.ibdCreditAmountUSD = safeAdd(targetMetrics.ibdCreditAmountUSD, chunkMetrics.ibdCreditAmountUSD);
        targetMetrics.ibdDebitCount = safeAdd(targetMetrics.ibdDebitCount, chunkMetrics.ibdDebitCount);
        targetMetrics.ibdDebitAmount = safeAdd(targetMetrics.ibdDebitAmount, chunkMetrics.ibdDebitAmount);
        targetMetrics.ibdDebitAmountMMK = safeAdd(targetMetrics.ibdDebitAmountMMK, chunkMetrics.ibdDebitAmountMMK);
        targetMetrics.ibdDebitAmountUSD = safeAdd(targetMetrics.ibdDebitAmountUSD, chunkMetrics.ibdDebitAmountUSD);

        // Accumulate WU metrics (PATCH: Ensure WU metrics are properly accumulated)
        targetMetrics.wuCount = safeAdd(targetMetrics.wuCount || 0, chunkMetrics.wuCount || 0);
        targetMetrics.wuAmount = safeAdd(targetMetrics.wuAmount || 0, chunkMetrics.wuAmount || 0);
        targetMetrics.wuAmountMMK = safeAdd(targetMetrics.wuAmountMMK || 0, chunkMetrics.wuAmountMMK || 0);
        targetMetrics.wuAmountUSD = safeAdd(targetMetrics.wuAmountUSD || 0, chunkMetrics.wuAmountUSD || 0);
        targetMetrics.wuCreditCount = safeAdd(targetMetrics.wuCreditCount || 0, chunkMetrics.wuCreditCount || 0);
        targetMetrics.wuCreditAmount = safeAdd(targetMetrics.wuCreditAmount || 0, chunkMetrics.wuCreditAmount || 0);
        targetMetrics.wuCreditAmountMMK = safeAdd(targetMetrics.wuCreditAmountMMK || 0, chunkMetrics.wuCreditAmountMMK || 0);
        targetMetrics.wuCreditAmountUSD = safeAdd(targetMetrics.wuCreditAmountUSD || 0, chunkMetrics.wuCreditAmountUSD || 0);
        targetMetrics.wuDebitCount = safeAdd(targetMetrics.wuDebitCount || 0, chunkMetrics.wuDebitCount || 0);
        targetMetrics.wuDebitAmount = safeAdd(targetMetrics.wuDebitAmount || 0, chunkMetrics.wuDebitAmount || 0);
        targetMetrics.wuDebitAmountMMK = safeAdd(targetMetrics.wuDebitAmountMMK || 0, chunkMetrics.wuDebitAmountMMK || 0);
        targetMetrics.wuDebitAmountUSD = safeAdd(targetMetrics.wuDebitAmountUSD || 0, chunkMetrics.wuDebitAmountUSD || 0);

        targetMetrics.currencyCounts.USD = safeAdd(targetMetrics.currencyCounts.USD, chunkMetrics.currencyCounts.USD);
        targetMetrics.currencyCounts.MMK = safeAdd(targetMetrics.currencyCounts.MMK, chunkMetrics.currencyCounts.MMK);
        targetMetrics.currencyAmounts.USD = safeAdd(targetMetrics.currencyAmounts.USD, chunkMetrics.currencyAmounts.USD);
        targetMetrics.currencyAmounts.MMK = safeAdd(targetMetrics.currencyAmounts.MMK, chunkMetrics.currencyAmounts.MMK);

        // Accumulate currency credit/debit amounts
        targetMetrics.currencyCreditAmounts.USD = safeAdd(targetMetrics.currencyCreditAmounts.USD || 0, chunkMetrics.currencyCreditAmounts.USD || 0);
        targetMetrics.currencyCreditAmounts.MMK = safeAdd(targetMetrics.currencyCreditAmounts.MMK || 0, chunkMetrics.currencyCreditAmounts.MMK || 0);
        targetMetrics.currencyDebitAmounts.USD = safeAdd(targetMetrics.currencyDebitAmounts.USD || 0, chunkMetrics.currencyDebitAmounts.USD || 0);
        targetMetrics.currencyDebitAmounts.MMK = safeAdd(targetMetrics.currencyDebitAmounts.MMK || 0, chunkMetrics.currencyDebitAmounts.MMK || 0);

        // Accumulate high-value transaction count
        targetMetrics.highValueTransactionCount = safeAdd(targetMetrics.highValueTransactionCount, chunkMetrics.highValueTransactionCount);
        targetMetrics.highValueTransactionCountUSD = safeAdd(targetMetrics.highValueTransactionCountUSD, chunkMetrics.highValueTransactionCountUSD);

        // Accumulate unique serial numbers
        if (!targetMetrics.hocSerialNumbers) targetMetrics.hocSerialNumbers = new Set();
        if (!targetMetrics.ibdSerialNumbers) targetMetrics.ibdSerialNumbers = new Set();
        if (!targetMetrics.wuSerialNumbers) targetMetrics.wuSerialNumbers = new Set();

        chunkMetrics.hocSerialNumbers.forEach(sn => targetMetrics.hocSerialNumbers.add(sn));
        chunkMetrics.ibdSerialNumbers.forEach(sn => targetMetrics.ibdSerialNumbers.add(sn));
        chunkMetrics.wuSerialNumbers.forEach(sn => targetMetrics.wuSerialNumbers.add(sn));
    }

    // Helper function to accumulate date data from a chunk into a target date data object
    accumulateDateData(targetDateData, chunkDateData) {
        if (!chunkDateData || Object.keys(chunkDateData).length === 0) return;

        const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);

        for (const [date, data] of Object.entries(chunkDateData)) {
            if (!targetDateData[date]) {
                // New date entry - create a copy
                targetDateData[date] = {
                    hocCreditCount: data.hocCreditCount || 0,
                    hocCreditAmountMMK: data.hocCreditAmountMMK || 0,
                    hocCreditAmountUSD: data.hocCreditAmountUSD || 0,
                    hocDebitCount: data.hocDebitCount || 0,
                    hocDebitAmountMMK: data.hocDebitAmountMMK || 0,
                    hocDebitAmountUSD: data.hocDebitAmountUSD || 0,
                    ibdCreditCount: data.ibdCreditCount || 0,
                    ibdCreditAmountMMK: data.ibdCreditAmountMMK || 0,
                    ibdCreditAmountUSD: data.ibdCreditAmountUSD || 0,
                    ibdDebitCount: data.ibdDebitCount || 0,
                    ibdDebitAmountMMK: data.ibdDebitAmountMMK || 0,
                    ibdDebitAmountUSD: data.ibdDebitAmountUSD || 0,
                    wuCreditCount: data.wuCreditCount || 0,
                    wuCreditAmountMMK: data.wuCreditAmountMMK || 0,
                    wuCreditAmountUSD: data.wuCreditAmountUSD || 0,
                    wuDebitCount: data.wuDebitCount || 0,
                    wuDebitAmountMMK: data.wuDebitAmountMMK || 0,
                    wuDebitAmountUSD: data.wuDebitAmountUSD || 0,
                    // Legacy fields for backward compatibility
                    hocCreditAmount: data.hocCreditAmount || 0,
                    hocDebitAmount: data.hocDebitAmount || 0,
                    ibdCreditAmount: data.ibdCreditAmount || 0,
                    ibdDebitAmount: data.ibdDebitAmount || 0
                };
            } else {
                // Accumulate into existing date entry
                targetDateData[date].hocCreditCount = safeAdd(targetDateData[date].hocCreditCount, data.hocCreditCount);
                targetDateData[date].hocCreditAmountMMK = safeAdd(targetDateData[date].hocCreditAmountMMK, data.hocCreditAmountMMK || 0);
                targetDateData[date].hocCreditAmountUSD = safeAdd(targetDateData[date].hocCreditAmountUSD, data.hocCreditAmountUSD || 0);
                targetDateData[date].hocDebitCount = safeAdd(targetDateData[date].hocDebitCount, data.hocDebitCount);
                targetDateData[date].hocDebitAmountMMK = safeAdd(targetDateData[date].hocDebitAmountMMK, data.hocDebitAmountMMK || 0);
                targetDateData[date].hocDebitAmountUSD = safeAdd(targetDateData[date].hocDebitAmountUSD, data.hocDebitAmountUSD || 0);
                targetDateData[date].ibdCreditCount = safeAdd(targetDateData[date].ibdCreditCount, data.ibdCreditCount);
                targetDateData[date].ibdCreditAmountMMK = safeAdd(targetDateData[date].ibdCreditAmountMMK, data.ibdCreditAmountMMK || 0);
                targetDateData[date].ibdCreditAmountUSD = safeAdd(targetDateData[date].ibdCreditAmountUSD, data.ibdCreditAmountUSD || 0);
                targetDateData[date].ibdDebitCount = safeAdd(targetDateData[date].ibdDebitCount, data.ibdDebitCount);
                targetDateData[date].ibdDebitAmountMMK = safeAdd(targetDateData[date].ibdDebitAmountMMK, data.ibdDebitAmountMMK || 0);
                targetDateData[date].ibdDebitAmountUSD = safeAdd(targetDateData[date].ibdDebitAmountUSD, data.ibdDebitAmountUSD || 0);
                targetDateData[date].wuCreditCount = safeAdd(targetDateData[date].wuCreditCount, data.wuCreditCount || 0);
                targetDateData[date].wuCreditAmountMMK = safeAdd(targetDateData[date].wuCreditAmountMMK, data.wuCreditAmountMMK || 0);
                targetDateData[date].wuCreditAmountUSD = safeAdd(targetDateData[date].wuCreditAmountUSD, data.wuCreditAmountUSD || 0);
                targetDateData[date].wuDebitCount = safeAdd(targetDateData[date].wuDebitCount, data.wuDebitCount || 0);
                targetDateData[date].wuDebitAmountMMK = safeAdd(targetDateData[date].wuDebitAmountMMK, data.wuDebitAmountMMK || 0);
                targetDateData[date].wuDebitAmountUSD = safeAdd(targetDateData[date].wuDebitAmountUSD, data.wuDebitAmountUSD || 0);
                // Legacy fields for backward compatibility
                targetDateData[date].hocCreditAmount = safeAdd(targetDateData[date].hocCreditAmount, data.hocCreditAmount);
                targetDateData[date].hocDebitAmount = safeAdd(targetDateData[date].hocDebitAmount, data.hocDebitAmount);
                targetDateData[date].ibdCreditAmount = safeAdd(targetDateData[date].ibdCreditAmount, data.ibdCreditAmount);
                targetDateData[date].ibdDebitAmount = safeAdd(targetDateData[date].ibdDebitAmount, data.ibdDebitAmount);
            }
        }
    }


    // Update metrics directly with a row.
    // Added isChunkOrFileSpecific flag to control serial number collection.
    // If true, it adds to metrics.hocSerialNumbers/ibdSerialNumbers (for chunk/file specific counts).
    // Otherwise, it adds to this.hocSerialNumbers/ibdSerialNumbers (for global counts).
    updateMetricsWithRow(row, metrics, isChunkOrFileSpecific = false, fileData = null, fileName = null, fileId = null) {
        const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);
        const amount = parseFloat(row.TRANSACTION_AMOUNT) || 0;

        metrics.totalTransactions++;
        metrics.totalAmount = safeAdd(metrics.totalAmount, amount);

        // Check if this is a high-value transaction (>=1B MMK or >=10K USD)
        const HIGH_VALUE_THRESHOLD = 1000000000; // 1B MMK
        const HIGH_VALUE_THRESHOLD_USD = 10000; // 10K USD

        if (row.TRANSACTION_CURRENCY === 'MMK' && amount >= HIGH_VALUE_THRESHOLD) {
            metrics.highValueTransactionCount = safeAdd(metrics.highValueTransactionCount || 0, 1);

            // Collect high-value transaction for date breakdown and export
            if (fileData && fileData.highValueTransactions) {
                fileData.highValueTransactions.push({
                    TRANSACTION_DATE: row.TRANSACTION_DATE,
                    TRANSACTION_AMOUNT: row.TRANSACTION_AMOUNT,
                    TRANSACTION_CURRENCY: row.TRANSACTION_CURRENCY,
                    REPORTTYPE: row.REPORTTYPE,
                    ACCOUNT_HOLDER_ACCOUNT_ROLE: row.ACCOUNT_HOLDER_ACCOUNT_ROLE,
                    // Customer information - support multiple field names
                    CUSTOMER_ID: row.CUSTOMER_ID || row.PARTICIPANT_ID || 'Unknown',
                    CUSTOMER_NAME: row.CUSTOMER_NAME || row.PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                    PARTICIPANT_NAME_CONDUCTOR: row.PARTICIPANT_NAME_CONDUCTOR || row.CUSTOMER_NAME || 'Unknown',
                    PARTICIPANT_NAME_COUNTERPARTY: row.PARTICIPANT_NAME_COUNTERPARTY || 'N/A',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: row.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A',
                    PARTICIPANT_ID_NUMBER_COUNTERPARTY: row.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A',
                    // Account information
                    ACCOUNT_NUMBER: row.ACCOUNT_NUMBER || row.ACCOUNT_NO || 'Unknown',
                    // Transaction details
                    TRANSACTION_TYPE: row.TRANSACTION_TYPE || 'Unknown',
                    TRANSACTION_DESCRIPTION: row.TRANSACTION_DESCRIPTION || row.DESCRIPTION || 'N/A',
                    // Additional fields that might be useful
                    SERIAL_NO: row.SERIAL_NO || '',
                    BRANCH_CODE: row.BRANCH_CODE || '',
                    REFERENCE_NUMBER: row.REFERENCE_NUMBER || row.REF_NO || '',
                    // File information for table display
                    fileName: fileName || 'Unknown',
                    fileId: fileId || 'Unknown'
                });

                console.log(`High-value MMK transaction collected: ${amount} ${row.TRANSACTION_CURRENCY} from ${row.CUSTOMER_NAME || 'Unknown'}`);
            } else {
                console.warn('fileData.highValueTransactions not available for storing high-value MMK transaction');
            }
        } else if (row.TRANSACTION_CURRENCY === 'USD' && amount >= HIGH_VALUE_THRESHOLD_USD) {
            metrics.highValueTransactionCountUSD = safeAdd(metrics.highValueTransactionCountUSD || 0, 1);

            // Collect high-value USD transaction for date breakdown and export
            if (fileData && fileData.highValueTransactionsUSD) {
                fileData.highValueTransactionsUSD.push({
                    TRANSACTION_DATE: row.TRANSACTION_DATE,
                    TRANSACTION_AMOUNT: row.TRANSACTION_AMOUNT,
                    TRANSACTION_CURRENCY: row.TRANSACTION_CURRENCY,
                    REPORTTYPE: row.REPORTTYPE,
                    ACCOUNT_HOLDER_ACCOUNT_ROLE: row.ACCOUNT_HOLDER_ACCOUNT_ROLE,
                    // Customer information - support multiple field names
                    CUSTOMER_ID: row.CUSTOMER_ID || row.PARTICIPANT_ID || 'Unknown',
                    CUSTOMER_NAME: row.CUSTOMER_NAME || row.PARTICIPANT_NAME_CONDUCTOR || 'Unknown',
                    PARTICIPANT_NAME_CONDUCTOR: row.PARTICIPANT_NAME_CONDUCTOR || row.CUSTOMER_NAME || 'Unknown',
                    PARTICIPANT_NAME_COUNTERPARTY: row.PARTICIPANT_NAME_COUNTERPARTY || 'N/A',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: row.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A',
                    PARTICIPANT_ID_NUMBER_COUNTERPARTY: row.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A',
                    // Account information
                    ACCOUNT_NUMBER: row.ACCOUNT_NUMBER || row.ACCOUNT_NO || 'Unknown',
                    // Transaction details
                    TRANSACTION_TYPE: row.TRANSACTION_TYPE || 'Unknown',
                    TRANSACTION_DESCRIPTION: row.TRANSACTION_DESCRIPTION || row.DESCRIPTION || 'N/A',
                    // Additional fields that might be useful
                    SERIAL_NO: row.SERIAL_NO || '',
                    BRANCH_CODE: row.BRANCH_CODE || '',
                    REFERENCE_NUMBER: row.REFERENCE_NUMBER || row.REF_NO || '',
                    // File information for table display
                    fileName: fileName || 'Unknown',
                    fileId: fileId || 'Unknown'
                });

                console.log(`High-value USD transaction collected: ${amount} ${row.TRANSACTION_CURRENCY} from ${row.CUSTOMER_NAME || 'Unknown'}`);
            } else {
                console.warn('fileData.highValueTransactionsUSD not available for storing high-value USD transaction');
            }
        }

        const isCredit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
        const isDebit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';
        const serialNo = row.SERIAL_NUMBER || row.SERIAL_NO || '';

        const hocTargetSet = isChunkOrFileSpecific ? metrics.hocSerialNumbers : this.hocSerialNumbers;
        const ibdTargetSet = isChunkOrFileSpecific ? metrics.ibdSerialNumbers : this.ibdSerialNumbers;
        const allTargetSet = isChunkOrFileSpecific ? null : this.allSerialNumbers; // allSerialNumbers is global only

        if (row.REPORTTYPE === 'HOC') {
            metrics.hocCount++;
            metrics.hocAmount = safeAdd(metrics.hocAmount, amount);

            // Update currency-specific HOC amounts
            const currency = row.TRANSACTION_CURRENCY;
            if (currency === 'MMK') {
                metrics.hocAmountMMK = safeAdd(metrics.hocAmountMMK || 0, amount);
            } else if (currency === 'USD') {
                metrics.hocAmountUSD = safeAdd(metrics.hocAmountUSD || 0, amount);
            }
            // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

            if (serialNo && hocTargetSet) hocTargetSet.add(serialNo);
            if (serialNo && allTargetSet) allTargetSet.add(serialNo);

            if (isCredit) {
                metrics.hocCreditCount++;
                metrics.hocCreditAmount = safeAdd(metrics.hocCreditAmount, amount);

                // Update currency-specific HOC credit amounts
                const currency = row.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    metrics.hocCreditAmountMMK = safeAdd(metrics.hocCreditAmountMMK || 0, amount);
                } else if (currency === 'USD') {
                    metrics.hocCreditAmountUSD = safeAdd(metrics.hocCreditAmountUSD || 0, amount);
                } else if (currency === 'CNY') {
                    metrics.hocCreditAmountCNY = safeAdd(metrics.hocCreditAmountCNY || 0, amount);
                } else if (currency === 'EUR') {
                    metrics.hocCreditAmountEUR = safeAdd(metrics.hocCreditAmountEUR || 0, amount);
                } else if (currency === 'INR') {
                    metrics.hocCreditAmountINR = safeAdd(metrics.hocCreditAmountINR || 0, amount);
                } else if (currency === 'JPY') {
                    metrics.hocCreditAmountJPY = safeAdd(metrics.hocCreditAmountJPY || 0, amount);
                } else if (currency === 'SGD') {
                    metrics.hocCreditAmountSGD = safeAdd(metrics.hocCreditAmountSGD || 0, amount);
                } else if (currency === 'THB') {
                    metrics.hocCreditAmountTHB = safeAdd(metrics.hocCreditAmountTHB || 0, amount);
                }
            } else if (isDebit) {
                metrics.hocDebitCount++;
                metrics.hocDebitAmount = safeAdd(metrics.hocDebitAmount, amount);

                // Update currency-specific HOC debit amounts
                const currency = row.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    metrics.hocDebitAmountMMK = safeAdd(metrics.hocDebitAmountMMK || 0, amount);
                } else if (currency === 'USD') {
                    metrics.hocDebitAmountUSD = safeAdd(metrics.hocDebitAmountUSD || 0, amount);
                } else if (currency === 'CNY') {
                    metrics.hocDebitAmountCNY = safeAdd(metrics.hocDebitAmountCNY || 0, amount);
                } else if (currency === 'EUR') {
                    metrics.hocDebitAmountEUR = safeAdd(metrics.hocDebitAmountEUR || 0, amount);
                } else if (currency === 'INR') {
                    metrics.hocDebitAmountINR = safeAdd(metrics.hocDebitAmountINR || 0, amount);
                } else if (currency === 'JPY') {
                    metrics.hocDebitAmountJPY = safeAdd(metrics.hocDebitAmountJPY || 0, amount);
                } else if (currency === 'SGD') {
                    metrics.hocDebitAmountSGD = safeAdd(metrics.hocDebitAmountSGD || 0, amount);
                } else if (currency === 'THB') {
                    metrics.hocDebitAmountTHB = safeAdd(metrics.hocDebitAmountTHB || 0, amount);
                }
            }
        } else if (row.REPORTTYPE === 'IBD') {
            metrics.ibdCount++;
            metrics.ibdAmount = safeAdd(metrics.ibdAmount, amount);

            // Update currency-specific IBD amounts
            if (row.TRANSACTION_CURRENCY === 'MMK') {
                metrics.ibdAmountMMK = safeAdd(metrics.ibdAmountMMK || 0, amount);
            } else if (row.TRANSACTION_CURRENCY === 'USD') {
                metrics.ibdAmountUSD = safeAdd(metrics.ibdAmountUSD || 0, amount);
            }

            if (serialNo && ibdTargetSet) ibdTargetSet.add(serialNo);
            if (serialNo && allTargetSet) allTargetSet.add(serialNo);

            if (isCredit) {
                metrics.ibdCreditCount++;
                metrics.ibdCreditAmount = safeAdd(metrics.ibdCreditAmount, amount);

                // Update currency-specific IBD credit amounts
                const currency = row.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    metrics.ibdCreditAmountMMK = safeAdd(metrics.ibdCreditAmountMMK || 0, amount);
                } else if (currency === 'USD') {
                    metrics.ibdCreditAmountUSD = safeAdd(metrics.ibdCreditAmountUSD || 0, amount);
                } else if (currency === 'CNY') {
                    metrics.ibdCreditAmountCNY = safeAdd(metrics.ibdCreditAmountCNY || 0, amount);
                } else if (currency === 'EUR') {
                    metrics.ibdCreditAmountEUR = safeAdd(metrics.ibdCreditAmountEUR || 0, amount);
                } else if (currency === 'INR') {
                    metrics.ibdCreditAmountINR = safeAdd(metrics.ibdCreditAmountINR || 0, amount);
                } else if (currency === 'JPY') {
                    metrics.ibdCreditAmountJPY = safeAdd(metrics.ibdCreditAmountJPY || 0, amount);
                } else if (currency === 'SGD') {
                    metrics.ibdCreditAmountSGD = safeAdd(metrics.ibdCreditAmountSGD || 0, amount);
                } else if (currency === 'THB') {
                    metrics.ibdCreditAmountTHB = safeAdd(metrics.ibdCreditAmountTHB || 0, amount);
                }
            } else if (isDebit) {
                metrics.ibdDebitCount++;
                metrics.ibdDebitAmount = safeAdd(metrics.ibdDebitAmount, amount);

                // Update currency-specific IBD debit amounts
                const currency = row.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    metrics.ibdDebitAmountMMK = safeAdd(metrics.ibdDebitAmountMMK || 0, amount);
                } else if (currency === 'USD') {
                    metrics.ibdDebitAmountUSD = safeAdd(metrics.ibdDebitAmountUSD || 0, amount);
                } else if (currency === 'CNY') {
                    metrics.ibdDebitAmountCNY = safeAdd(metrics.ibdDebitAmountCNY || 0, amount);
                } else if (currency === 'EUR') {
                    metrics.ibdDebitAmountEUR = safeAdd(metrics.ibdDebitAmountEUR || 0, amount);
                } else if (currency === 'INR') {
                    metrics.ibdDebitAmountINR = safeAdd(metrics.ibdDebitAmountINR || 0, amount);
                } else if (currency === 'JPY') {
                    metrics.ibdDebitAmountJPY = safeAdd(metrics.ibdDebitAmountJPY || 0, amount);
                } else if (currency === 'SGD') {
                    metrics.ibdDebitAmountSGD = safeAdd(metrics.ibdDebitAmountSGD || 0, amount);
                } else if (currency === 'THB') {
                    metrics.ibdDebitAmountTHB = safeAdd(metrics.ibdDebitAmountTHB || 0, amount);
                }
            }
        } else if (row.REPORTTYPE === 'WU') {
            // WU (Western Union) transactions - separate metrics (not included in IBD)
            metrics.wuCount = safeAdd(metrics.wuCount || 0, 1);
            metrics.wuAmount = safeAdd(metrics.wuAmount || 0, amount);

            // Update currency-specific WU amounts
            if (row.TRANSACTION_CURRENCY === 'MMK') {
                metrics.wuAmountMMK = safeAdd(metrics.wuAmountMMK || 0, amount);
            } else if (row.TRANSACTION_CURRENCY === 'USD') {
                metrics.wuAmountUSD = safeAdd(metrics.wuAmountUSD || 0, amount);
            }

            // Track unique serial numbers for WU
            if (serialNo && isChunkOrFileSpecific) {
                if (!metrics.wuSerialNumbers) metrics.wuSerialNumbers = new Set();
                metrics.wuSerialNumbers.add(serialNo);
            } else if (serialNo) {
                if (!this.wuSerialNumbers) this.wuSerialNumbers = new Set();
                this.wuSerialNumbers.add(serialNo);
            }
            if (serialNo && allTargetSet) allTargetSet.add(serialNo);

            // Update WU credit/debit counts
            if (isCredit) {
                metrics.wuCreditCount = safeAdd(metrics.wuCreditCount || 0, 1);
                metrics.wuCreditAmount = safeAdd(metrics.wuCreditAmount || 0, amount);

                // Update currency-specific WU credit amounts
                const currency = row.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    metrics.wuCreditAmountMMK = safeAdd(metrics.wuCreditAmountMMK || 0, amount);
                } else if (currency === 'USD') {
                    metrics.wuCreditAmountUSD = safeAdd(metrics.wuCreditAmountUSD || 0, amount);
                } else if (currency === 'CNY') {
                    metrics.wuCreditAmountCNY = safeAdd(metrics.wuCreditAmountCNY || 0, amount);
                } else if (currency === 'EUR') {
                    metrics.wuCreditAmountEUR = safeAdd(metrics.wuCreditAmountEUR || 0, amount);
                } else if (currency === 'INR') {
                    metrics.wuCreditAmountINR = safeAdd(metrics.wuCreditAmountINR || 0, amount);
                } else if (currency === 'JPY') {
                    metrics.wuCreditAmountJPY = safeAdd(metrics.wuCreditAmountJPY || 0, amount);
                } else if (currency === 'SGD') {
                    metrics.wuCreditAmountSGD = safeAdd(metrics.wuCreditAmountSGD || 0, amount);
                } else if (currency === 'THB') {
                    metrics.wuCreditAmountTHB = safeAdd(metrics.wuCreditAmountTHB || 0, amount);
                }
            } else if (isDebit) {
                metrics.wuDebitCount = safeAdd(metrics.wuDebitCount || 0, 1);
                metrics.wuDebitAmount = safeAdd(metrics.wuDebitAmount || 0, amount);

                // Update currency-specific WU debit amounts
                const currency = row.TRANSACTION_CURRENCY;
                if (currency === 'MMK') {
                    metrics.wuDebitAmountMMK = safeAdd(metrics.wuDebitAmountMMK || 0, amount);
                } else if (currency === 'USD') {
                    metrics.wuDebitAmountUSD = safeAdd(metrics.wuDebitAmountUSD || 0, amount);
                } else if (currency === 'CNY') {
                    metrics.wuDebitAmountCNY = safeAdd(metrics.wuDebitAmountCNY || 0, amount);
                } else if (currency === 'EUR') {
                    metrics.wuDebitAmountEUR = safeAdd(metrics.wuDebitAmountEUR || 0, amount);
                } else if (currency === 'INR') {
                    metrics.wuDebitAmountINR = safeAdd(metrics.wuDebitAmountINR || 0, amount);
                } else if (currency === 'JPY') {
                    metrics.wuDebitAmountJPY = safeAdd(metrics.wuDebitAmountJPY || 0, amount);
                } else if (currency === 'SGD') {
                    metrics.wuDebitAmountSGD = safeAdd(metrics.wuDebitAmountSGD || 0, amount);
                } else if (currency === 'THB') {
                    metrics.wuDebitAmountTHB = safeAdd(metrics.wuDebitAmountTHB || 0, amount);
                }
            }
        }

        // REMOVED: Currency amount calculations to prevent double counting
        // Currency amounts are now calculated only in DataProcessor.calculateMetrics()
        // This prevents double counting when both FileHandler and DataProcessor process the same data

        // Note: Currency counts and credit/debit amounts are still tracked for file-specific metrics
        // but the main currency totals are calculated centrally in DataProcessor
    }

    // Update date-based aggregation for table
    updateDateData(row, dateData) {
        // Extract date from transaction date using a standardized approach
        let date = 'Unknown';
        if (row.TRANSACTION_DATE) {
            try {
                // Extract just the date part if there's a space (time component)
                let dateStr = row.TRANSACTION_DATE;
                if (dateStr.includes(' ')) {
                    dateStr = dateStr.split(' ')[0];
                }

                // Format: "26-JAN-25" (DD-MMM-YY)
                if (/^\d{2}-[A-Z]{3}-\d{2}$/.test(dateStr)) {
                    date = dateStr;
                }
                // Format: "2023-01-01" (ISO format YYYY-MM-DD)
                else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                    // Convert to DD-MMM-YY format for consistency
                    const dateObj = new Date(dateStr);
                    if (!isNaN(dateObj.getTime())) {
                        date = dateObj.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short',
                            year: '2-digit'
                        }).replace(/ /g, '-').toUpperCase();
                    } else {
                        date = dateStr;
                    }
                }
                // Try to parse as date if it's in another format
                else {
                    const dateObj = new Date(dateStr);
                    if (!isNaN(dateObj.getTime())) {
                        date = dateObj.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short',
                            year: '2-digit'
                        }).replace(/ /g, '-').toUpperCase();
                    } else {
                        date = dateStr;
                    }
                }
            } catch (error) {
                console.error('Error parsing date:', error);
                date = 'Unknown';
            }
        }

        // Initialize date entry if it doesn't exist
        if (!dateData[date]) {
            dateData[date] = {
                hocCreditCount: 0,
                hocCreditAmountMMK: 0,
                hocCreditAmountUSD: 0,
                hocDebitCount: 0,
                hocDebitAmountMMK: 0,
                hocDebitAmountUSD: 0,
                ibdCreditCount: 0,
                ibdCreditAmountMMK: 0,
                ibdCreditAmountUSD: 0,
                ibdDebitCount: 0,
                ibdDebitAmountMMK: 0,
                ibdDebitAmountUSD: 0,
                wuCreditCount: 0,
                wuCreditAmountMMK: 0,
                wuCreditAmountUSD: 0,
                wuDebitCount: 0,
                wuDebitAmountMMK: 0,
                wuDebitAmountUSD: 0,
                // Legacy fields for backward compatibility
                hocCreditAmount: 0,
                hocDebitAmount: 0,
                ibdCreditAmount: 0,
                ibdDebitAmount: 0
            };
        }

        // Get transaction amount as number with validation
        const amount = typeof row.TRANSACTION_AMOUNT === 'string' ?
            parseFloat(row.TRANSACTION_AMOUNT) || 0 :
            (typeof row.TRANSACTION_AMOUNT === 'number' ? row.TRANSACTION_AMOUNT : 0);
        const currency = row.TRANSACTION_CURRENCY || 'MMK';

        // Determine transaction type
        const isCredit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
        const isDebit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';

        // Update counts and amounts based on transaction type
        if (row.REPORTTYPE === 'HOC') {
            if (isCredit) {
                dateData[date].hocCreditCount++;
                dateData[date].hocCreditAmount += amount; // Legacy total
                if (currency === 'MMK') {
                    dateData[date].hocCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].hocCreditAmountUSD += amount;
                }
            } else if (isDebit) {
                dateData[date].hocDebitCount++;
                dateData[date].hocDebitAmount += amount; // Legacy total
                if (currency === 'MMK') {
                    dateData[date].hocDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].hocDebitAmountUSD += amount;
                }
            }
        } else if (row.REPORTTYPE === 'IBD') {
            if (isCredit) {
                dateData[date].ibdCreditCount++;
                dateData[date].ibdCreditAmount += amount; // Legacy total
                if (currency === 'MMK') {
                    dateData[date].ibdCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].ibdCreditAmountUSD += amount;
                }
            } else if (isDebit) {
                dateData[date].ibdDebitCount++;
                dateData[date].ibdDebitAmount += amount; // Legacy total
                if (currency === 'MMK') {
                    dateData[date].ibdDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].ibdDebitAmountUSD += amount;
                }
            }
        } else if (row.REPORTTYPE === 'WU' || row.REPORTTYPE === 'IBU') {
            // For WU and IBU transactions (IBU treated as equivalent to WU),
            // add to separate WU metrics for table data only (excluding WU IBD transactions)
            // Note: Other parts of the system continue to include WU in IBD metrics
            if (isCredit) {
                dateData[date].wuCreditCount++;
                if (currency === 'MMK') {
                    dateData[date].wuCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].wuCreditAmountUSD += amount;
                }
            } else if (isDebit) {
                dateData[date].wuDebitCount++;
                if (currency === 'MMK') {
                    dateData[date].wuDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].wuDebitAmountUSD += amount;
                }
            }
        }
    }

    // Update global metrics with a file's metrics - This function is no longer needed here
    // as global metrics are updated in DataProcessor after a file is fully processed.
    // updateGlobalMetrics(fileMetrics) { ... }



    // Legacy method for backward compatibility
    async processFileInChunks(file) {
        console.warn('Using legacy processFileInChunks method - consider upgrading to memory-optimized version');
        return this.processFileWithMemoryOptimization(file).then(() => {
            // Return empty array to maintain compatibility
            return [];
        });
    }

    // Parse a CSV line into an array of values with security sanitization
    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];

            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(this.sanitizeCSVField(current.trim()));
                current = '';
            } else {
                current += char;
            }
        }

        // Add the last field with sanitization
        result.push(this.sanitizeCSVField(current.trim()));

        return result;
    }

    // Sanitize CSV field content to prevent injection attacks
    sanitizeCSVField(field) {
        if (typeof field !== 'string') {
            return String(field);
        }

        // Remove or escape potentially dangerous characters
        let sanitized = field
            .replace(/[<>]/g, '') // Remove HTML tags
            .replace(/javascript:/gi, '') // Remove javascript: protocol
            .replace(/data:/gi, '') // Remove data: protocol
            .replace(/vbscript:/gi, '') // Remove vbscript: protocol
            .replace(/on\w+\s*=/gi, '') // Remove event handlers
            .trim();

        // Escape formula injection characters at the beginning
        if (sanitized.match(/^[=+\-@]/)) {
            sanitized = "'" + sanitized;
        }

        return sanitized;
    }

    // Enhanced utility method to show progress bar with improved visibility and debugging
    showProgressBar() {
        const progressElement = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        console.log('FileHandler: Showing progress bar with enhanced visibility...');

        if (progressElement) {
            // Clear any existing styles and classes first
            progressElement.classList.remove('visible', 'show', 'force-show');
            progressElement.removeAttribute('style');

            // Force immediate visibility with multiple approaches
            progressElement.style.cssText = `
                display: block !important;
                opacity: 1 !important;
                transform: translateY(0) !important;
                visibility: visible !important;
                position: relative !important;
                z-index: 1000 !important;
                margin: 20px 0 !important;
            `;

            // Add all visibility classes for maximum compatibility
            progressElement.classList.add('visible', 'show', 'force-show');

            // Force multiple reflows to ensure rendering
            progressElement.offsetHeight;
            progressElement.scrollTop;
            progressElement.getBoundingClientRect();

            // Verify visibility after setting
            const computedStyle = window.getComputedStyle(progressElement);
            console.log('FileHandler: Progress element computed display:', computedStyle.display);
            console.log('FileHandler: Progress element computed opacity:', computedStyle.opacity);
            console.log('FileHandler: Progress element computed visibility:', computedStyle.visibility);
        } else {
            console.error('FileHandler: Progress element not found! Check HTML structure.');
            return false;
        }

        // Initialize progress bar with enhanced settings
        if (progressBar) {
            progressBar.style.cssText = `
                width: 0% !important;
                display: block !important;
                height: 100% !important;
                transition: width 0.3s ease !important;
            `;
            console.log('FileHandler: Progress bar initialized with enhanced styles');
        } else {
            console.error('FileHandler: Progress bar element not found!');
            return false;
        }

        // Initialize progress text with enhanced visibility
        if (progressText) {
            progressText.style.cssText = `
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
            `;
            progressText.textContent = '0% - Initializing file processing...';
            progressText.classList.add('loading');
            console.log('FileHandler: Progress text initialized with loading animation');
        } else {
            console.error('FileHandler: Progress text element not found!');
            return false;
        }

        // Final verification
        setTimeout(() => {
            const isVisible = progressElement.offsetHeight > 0 &&
                            progressElement.offsetWidth > 0 &&
                            window.getComputedStyle(progressElement).display !== 'none';

            if (isVisible) {
                console.log('✅ Progress bar successfully shown and verified visible');
            } else {
                console.error('❌ Progress bar failed to show properly - attempting fallback');
                this.showProgressBarFallback();
            }
        }, 100);

        console.log('Progress bar show operation completed');
        return true;
    }

    // Fallback method for showing progress bar when primary method fails
    showProgressBarFallback() {
        console.log('FileHandler: Attempting fallback progress bar display...');

        const progressElement = document.getElementById('uploadProgress');
        if (progressElement) {
            // Use the most aggressive approach possible
            progressElement.style.setProperty('display', 'block', 'important');
            progressElement.style.setProperty('opacity', '1', 'important');
            progressElement.style.setProperty('visibility', 'visible', 'important');
            progressElement.style.setProperty('position', 'relative', 'important');
            progressElement.style.setProperty('z-index', '9999', 'important');

            // Force immediate render
            progressElement.offsetHeight;

            console.log('Fallback progress bar display attempted');
        }
    }

    // Enhanced utility method to hide progress bar with smooth transition
    hideProgressBar() {
        const progressElement = document.getElementById('uploadProgress');
        const progressText = document.getElementById('progressText');

        console.log('FileHandler: Hiding progress bar...');

        if (progressElement) {
            // Remove loading animation from text
            if (progressText) {
                progressText.classList.remove('loading');
            }

            // First fade out with transition
            progressElement.style.opacity = '0';
            progressElement.style.transform = 'translateY(-10px)';

            // Then hide completely after transition
            setTimeout(() => {
                progressElement.style.display = 'none';
                progressElement.style.visibility = 'hidden';
                progressElement.classList.remove('visible', 'show', 'force-show');

                // Reset for next use
                progressElement.removeAttribute('style');

                console.log('Progress bar hidden and reset for next use');
            }, 300);
        }

        console.log('Progress bar hide operation completed');
    }

    // Enhanced update progress UI with improved visibility and error handling
    updateProgress() {
        // Throttle progress updates during streaming to reduce console warnings
        const now = Date.now();
        if (!this.lastProgressUpdate) {
            this.lastProgressUpdate = now;
        }

        // During streaming, limit updates to every 200ms to reduce warnings
        const isStreamingUpdate = this.isProcessing && this.processedSize > 0;
        const throttleInterval = isStreamingUpdate ? 200 : 50;

        if (now - this.lastProgressUpdate < throttleInterval) {
            return; // Skip this update to reduce frequency
        }
        this.lastProgressUpdate = now;

        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const progressElement = document.getElementById('uploadProgress');

        // Calculate percentage with safety checks
        const percentage = this.totalSize > 0 ?
            Math.min(100, Math.max(0, Math.round((this.processedSize / this.totalSize) * 100))) : 0;

        // Only log progress updates every 5% to reduce console spam
        if (!this.lastLoggedPercentage || percentage - this.lastLoggedPercentage >= 5 || percentage === 100) {
            console.log(`Updating progress: ${percentage}% (${this.processedSize}/${this.totalSize} bytes)`);
            this.lastLoggedPercentage = percentage;
        }

        // Ensure progress container is visible with enhanced checks
        if (progressElement) {
            const computedStyle = window.getComputedStyle(progressElement);
            const isHidden = computedStyle.display === 'none' ||
                           computedStyle.visibility === 'hidden' ||
                           computedStyle.opacity === '0' ||
                           !progressElement.classList.contains('visible');

            if (isHidden) {
                console.log('Progress element was hidden, re-showing...');
                this.showProgressBar();
            }
        } else {
            console.error('Progress element not found during update');
            return;
        }

        // Update progress bar with enhanced validation
        if (progressBar) {
            // For frequent updates during streaming, disable transitions temporarily
            const isStreamingUpdate = this.isProcessing && this.processedSize > 0;

            if (isStreamingUpdate) {
                // Disable transition for smooth streaming updates
                progressBar.style.cssText = `
                    display: block !important;
                    width: ${percentage}% !important;
                    height: 100% !important;
                    transition: none !important;
                `;
            } else {
                // Use transition for initial and final updates
                progressBar.style.cssText = `
                    display: block !important;
                    width: ${percentage}% !important;
                    height: 100% !important;
                    transition: width 0.3s ease !important;
                `;
            }

            // Only validate width for non-streaming updates to avoid transition conflicts
            if (!isStreamingUpdate) {
                setTimeout(() => {
                    const actualWidth = progressBar.style.width;
                    if (actualWidth !== `${percentage}%`) {
                        console.warn(`Progress bar width mismatch: expected ${percentage}%, got ${actualWidth}. Retrying...`);
                        progressBar.style.setProperty('width', `${percentage}%`, 'important');
                    }
                }, 350); // Wait for transition to complete (300ms + buffer)
            }

            // Only log progress bar updates every 5% to reduce console spam
            if (!this.lastLoggedPercentage || percentage - this.lastLoggedPercentage >= 5 || percentage === 100) {
                console.log(`Progress bar updated to ${percentage}%`);
            }
        } else {
            console.error('Progress bar element not found during update');
        }

        // Update progress text with enhanced information
        if (progressText) {
            const currentFile = this.currentProcessingFile || 'files';
            const fileInfo = currentFile !== 'files' ? ` (${currentFile})` : '';
            const newText = `${percentage}% - Processing${fileInfo}...`;

            progressText.style.cssText = `
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
            `;
            progressText.textContent = newText;

            // Add loading animation if not already present
            if (!progressText.classList.contains('loading')) {
                progressText.classList.add('loading');
            }

            // Only log progress text updates every 5% to reduce console spam
            if (!this.lastLoggedPercentage || percentage - this.lastLoggedPercentage >= 5 || percentage === 100) {
                console.log(`Progress text updated: "${newText}"`);
            }
        } else {
            console.error('Progress text element not found during update');
        }

        // Force multiple reflows to ensure immediate visual update
        if (progressElement) {
            progressElement.offsetHeight;
            progressElement.getBoundingClientRect();
        }
        if (progressBar) {
            progressBar.offsetHeight;
            progressBar.getBoundingClientRect();
        }
        if (progressText) {
            progressText.offsetHeight;
        }

        // Dispatch custom event for external monitoring
        document.dispatchEvent(new CustomEvent('progressUpdated', {
            detail: { percentage, processedSize: this.processedSize, totalSize: this.totalSize }
        }));
    }

    // Get all processed data
    getAllData() {
        return this.processedData;
    }

    // Update dashboard with processed data
    updateDashboard() {
        // Count active (non-removed) files
        const activeFiles = this.files.filter(file => !file.isRemoved);

        // Update files processed count with validation
        const filesProcessedElement = document.getElementById('filesProcessed');
        if (filesProcessedElement) {
            // Show both active files and total unique processed files
            const uniqueProcessedCount = window.dataProcessor ?
                window.dataProcessor.getProcessedFilesCount() : 0;

            // Update the display to show both counts
            filesProcessedElement.textContent = `${activeFiles.length} active (${uniqueProcessedCount} unique)`;
        }

        // Calculate total rows processed across all files
        const totalRowsProcessed = this.processedData.reduce((total, file) => {
            return total + (file.processedRowCount || 0);
        }, 0);

        // Log detailed processing information
        console.log(`Dashboard updated with ${activeFiles.length} active files out of ${this.files.length} total files containing approximately ${totalRowsProcessed} transactions`);

        // Update the file list to refresh status indicators
        this.updateFileList();

        // If we have a data processor, ensure the transaction count is accurate
        if (window.dataProcessor) {
            // Get current metrics
            const currentMetrics = window.dataProcessor.getSummaryMetrics();

            // Check if we need to update the transaction count
            if (currentMetrics.totalTransactions < totalRowsProcessed) {
                console.log(`Fixing transaction count: ${currentMetrics.totalTransactions} → ${totalRowsProcessed}`);

                // Create a metrics update to fix the transaction count
                const metricsUpdate = {
                    totalTransactions: totalRowsProcessed,
                    // Keep other metrics proportional to maintain data integrity
                    totalAmount: currentMetrics.totalAmount * (totalRowsProcessed / Math.max(currentMetrics.totalTransactions, 1)),
                    hocCount: Math.round(currentMetrics.hocCount * (totalRowsProcessed / Math.max(currentMetrics.totalTransactions, 1))),
                    ibdCount: Math.round(currentMetrics.ibdCount * (totalRowsProcessed / Math.max(currentMetrics.totalTransactions, 1))),
                    // Add other metrics as needed with similar scaling
                };

                // Apply the fix - this will update the UI without resetting accumulated metrics
                window.dataProcessor.fixTransactionCount(totalRowsProcessed, metricsUpdate);
            }

            // Only process sample data if we're not using a worker AND we have no metrics yet
            if (!this.worker && currentMetrics.totalTransactions === 0) {
                // For backward compatibility with non-worker mode, create a representative dataset
                const allData = [];

                // Use all sample rows from all files to represent the full dataset
                this.processedData.forEach(file => {
                    if (file.sampleRows && file.sampleRows.length > 0) {
                        allData.push(...file.sampleRows);
                    }
                });

                // Try to get full transaction data first, fall back to sample data
                let dataToProcess = [];
                let dataSource = 'unknown';

                // First, try to get full transaction data from fileTransactionData
                if (this.fileTransactionData.size > 0) {
                    console.log('🔍 Attempting to use full transaction data from fileTransactionData');
                    this.fileTransactionData.forEach((transactions, fileName) => {
                        if (transactions && transactions.length > 0) {
                            dataToProcess.push(...transactions);
                            console.log(`Added ${transactions.length} transactions from ${fileName}`);
                        }
                    });
                    dataSource = 'full transaction data';
                }

                // If no full data available, use sample data as fallback
                if (dataToProcess.length === 0 && allData.length > 0) {
                    console.log('⚠️ No full transaction data available, using sample data as fallback');
                    dataToProcess = allData;
                    dataSource = 'sample data';
                }

                // Process the data
                if (dataToProcess.length > 0) {
                    console.log(`✅ Processing ${dataToProcess.length} records from ${dataSource}`);
                    window.dataProcessor.processData(dataToProcess);

                    // Only fix transaction count if using sample data
                    if (dataSource === 'sample data') {
                        window.dataProcessor.fixTransactionCount(totalRowsProcessed);
                    }

                    // PRODUCTION FIX: Force currency metrics recalculation and UI update
                    if (typeof window.dataProcessor.recalculateCurrencyMetrics === 'function') {
                        window.dataProcessor.recalculateCurrencyMetrics();
                        console.log('✅ Currency metrics recalculated in finalization');
                    }

                    if (typeof window.dataProcessor.updateCurrencyBreakdown === 'function') {
                        setTimeout(() => {
                            window.dataProcessor.updateCurrencyBreakdown();
                            console.log('✅ Currency breakdown updated in finalization');
                        }, 200);
                    }

                    // Update TTR Summary Report
                    if (typeof window.dataProcessor.updateTTRSummaryReport === 'function') {
                        setTimeout(() => {
                            window.dataProcessor.updateTTRSummaryReport();
                            console.log('✅ TTR Summary Report updated in finalization');
                        }, 250);
                    }
                } else {
                    console.warn('❌ No transaction data available for processing');
                }
            }

            // Log the current metrics for debugging
            const metrics = window.dataProcessor.getSummaryMetrics();
            console.log('Current metrics:', {
                totalTransactions: metrics.totalTransactions,
                hocCount: metrics.hocCount,
                ibdCount: metrics.ibdCount
            });
        }
    }

    // Setup browser responsiveness handling to prevent "Page Unresponsive" warnings
    setupResponsivenessHandling() {
        // Enhanced progress updates with more frequent UI refreshes
        this.lastProgressUpdate = Date.now();
        this.progressUpdateInterval = 100; // Update every 100ms minimum
        this.processingStartTime = Date.now();

        // Setup heartbeat to keep browser responsive
        this.responsivenessHeartbeat = setInterval(() => {
            this.updateResponsivenessIndicators();
        }, 250); // Every 250ms

        console.log('Browser responsiveness handling activated');
    }

    // Update responsiveness indicators and yield control to browser
    updateResponsivenessIndicators() {
        const now = Date.now();

        // Update progress text with current status
        const progressElement = document.getElementById('progressText');
        if (progressElement && this.isProcessing) {
            const elapsed = Math.round((now - this.processingStartTime) / 1000);
            const status = this.currentProcessingStatus || 'Processing files...';
            progressElement.textContent = `${status} (${elapsed}s elapsed)`;
        }

        // Update memory usage if available
        if (window.performanceMonitor) {
            window.performanceMonitor.updateMemoryMetrics();
        }

        // Yield control to browser to prevent unresponsive warnings
        if (this.isProcessing) {
            // Force a small delay to allow browser to process other events
            setTimeout(() => {
                // This empty timeout allows the browser to handle other tasks
            }, 0);
        }
    }

    // Enhanced progress update with responsiveness handling
    updateProcessingProgress(current, total, fileName = '') {
        const now = Date.now();

        // Throttle updates but ensure responsiveness
        if (now - this.lastProgressUpdate < this.progressUpdateInterval) {
            return;
        }

        this.lastProgressUpdate = now;

        // Calculate progress percentage
        const percentage = Math.round((current / total) * 100);

        // Update progress bar
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }

        // Update status text with detailed information
        this.currentProcessingStatus = fileName ?
            `Processing: ${fileName} (${percentage}%)` :
            `Processing files... ${percentage}%`;

        const progressText = document.getElementById('progressText');
        if (progressText) {
            progressText.textContent = this.currentProcessingStatus;
        }

        // Force browser to render updates
        if (progressBar && progressText) {
            // Trigger reflow to ensure immediate visual update
            progressBar.offsetHeight;
            progressText.offsetHeight;
        }
    }

    // Cleanup responsiveness handling
    cleanupResponsivenessHandling() {
        if (this.responsivenessHeartbeat) {
            clearInterval(this.responsivenessHeartbeat);
            this.responsivenessHeartbeat = null;
        }

        this.currentProcessingStatus = null;
        this.isProcessing = false;
        console.log('Browser responsiveness handling deactivated');
    }





    // Force reset a file's processed status (for single file issues)
    forceResetFileProcessedStatus(fileIndex = 0) {
        console.log(`🔄 FORCE RESET FILE PROCESSED STATUS - File ${fileIndex}`);

        if (fileIndex < 0 || fileIndex >= this.files.length) {
            console.error(`❌ Invalid file index: ${fileIndex}`);
            return;
        }

        const file = this.files[fileIndex];
        console.log(`📄 Resetting processed status for: ${file.name}`);

        // Clear processed status
        file.isAlreadyProcessed = false;

        // Remove from dataProcessor if it thinks it's processed
        if (window.dataProcessor) {
            const fileId = window.dataProcessor.generateFileId(file);
            if (window.dataProcessor.processedFiles.has(fileId)) {
                window.dataProcessor.processedFiles.delete(fileId);
                console.log(`🔧 Removed from dataProcessor processed list: ${fileId}`);
            }
        }

        // Update UI
        this.updateFileList();
        this.updateSubmitButtonVisibility();

        console.log(`✅ File ${fileIndex} (${file.name}) reset to unprocessed state`);
    }

    // Check if single file is being treated as active
    checkSingleFileStatus() {
        console.log('🔍 SINGLE FILE STATUS CHECK');
        console.log('===========================');

        if (this.files.length === 0) {
            console.log('❌ No files uploaded');
            return;
        }

        if (this.files.length === 1) {
            const file = this.files[0];
            const isRemoved = file.isRemoved === true;
            const isProcessed = file.isAlreadyProcessed ||
                (window.dataProcessor && window.dataProcessor.isFileProcessed(file));

            console.log(`📄 Single file analysis: ${file.name}`);
            console.log(`  - isRemoved: ${isRemoved}`);
            console.log(`  - isAlreadyProcessed: ${file.isAlreadyProcessed}`);
            console.log(`  - dataProcessor.isFileProcessed: ${window.dataProcessor ? window.dataProcessor.isFileProcessed(file) : 'N/A'}`);
            console.log(`  - Final isProcessed: ${isProcessed}`);
            console.log(`  - Should be active: ${!isProcessed && !isRemoved}`);

            const hasUnprocessed = this.hasUnprocessedFiles();
            console.log(`  - hasUnprocessedFiles(): ${hasUnprocessed}`);

            if (!hasUnprocessed && !isRemoved) {
                console.log('🚨 ISSUE: Single file is not considered unprocessed!');
                console.log('💡 Try: forceResetFileProcessedStatus(0)');
            } else {
                console.log('✅ Single file status looks correct');
            }
        } else {
            console.log(`📁 Multiple files (${this.files.length}) - checking each:`);
            this.files.forEach((file, index) => {
                const isRemoved = file.isRemoved === true;
                const isProcessed = file.isAlreadyProcessed ||
                    (window.dataProcessor && window.dataProcessor.isFileProcessed(file));
                console.log(`  ${index}: ${file.name} - removed: ${isRemoved}, processed: ${isProcessed}`);
            });
        }
    }

    // Specific diagnostic for TTR_11Aug2024.csv file
    diagnoseTTRFile() {
        console.log('🔍 TTR_11Aug2024.csv DIAGNOSTIC');
        console.log('================================');

        // Check if file is in the files array
        const ttrFile = this.files.find(file => file.name === 'TTR_11Aug2024.csv');

        if (!ttrFile) {
            console.log('❌ TTR_11Aug2024.csv not found in files array');
            console.log('📁 Current files:');
            this.files.forEach((file, index) => {
                console.log(`  ${index}: ${file.name}`);
            });
            return;
        }

        console.log('✅ TTR_11Aug2024.csv found in files array');
        console.log('📄 File details:');
        console.log(`  - Name: ${ttrFile.name}`);
        console.log(`  - Size: ${ttrFile.size} bytes (${this.formatFileSize(ttrFile.size)})`);
        console.log(`  - Type: ${ttrFile.type}`);
        console.log(`  - Last Modified: ${new Date(ttrFile.lastModified)}`);

        // Check file status flags
        console.log('🏷️ File status flags:');
        console.log(`  - isRemoved: ${ttrFile.isRemoved} (type: ${typeof ttrFile.isRemoved})`);
        console.log(`  - isAlreadyProcessed: ${ttrFile.isAlreadyProcessed} (type: ${typeof ttrFile.isAlreadyProcessed})`);

        // Check dataProcessor status
        if (window.dataProcessor) {
            const isProcessedByDataProcessor = window.dataProcessor.isFileProcessed(ttrFile);
            console.log(`  - dataProcessor.isFileProcessed: ${isProcessedByDataProcessor}`);

            // Check if file ID exists in processed files
            const fileId = window.dataProcessor.generateFileId(ttrFile);
            console.log(`  - Generated file ID: ${fileId}`);
            console.log(`  - In processedFiles set: ${window.dataProcessor.processedFiles.has(fileId)}`);

            // Show all processed file IDs for comparison
            console.log(`  - All processed file IDs:`, Array.from(window.dataProcessor.processedFiles));
        } else {
            console.log(`  - dataProcessor: Not available`);
        }

        // Calculate final status using the same logic as updateFileList
        const isRemoved = ttrFile.isRemoved === true ||
                         ttrFile.isRemoved === 'true' ||
                         Boolean(ttrFile.isRemoved);

        const isProcessed = !isRemoved && (ttrFile.isAlreadyProcessed ||
            (window.dataProcessor && window.dataProcessor.isFileProcessed(ttrFile)));

        console.log('🎯 Final status calculation:');
        console.log(`  - isRemoved (priority): ${isRemoved}`);
        console.log(`  - isProcessed (after isRemoved check): ${isProcessed}`);
        console.log(`  - Should be ACTIVE: ${!isProcessed && !isRemoved}`);
        console.log(`  - Expected UI state: ${isRemoved ? 'REMOVED (red)' : isProcessed ? 'PROCESSED (green)' : 'ACTIVE (normal)'}`);

        // Check submit button status
        const hasUnprocessed = this.hasUnprocessedFiles();
        console.log(`🔘 Submit button status:`);
        console.log(`  - hasUnprocessedFiles(): ${hasUnprocessed}`);
        console.log(`  - Submit button should be: ${hasUnprocessed ? 'ENABLED' : 'DISABLED'}`);

        // Check UI state
        console.log('🎨 Current UI state:');
        const fileListElement = document.getElementById('uploadedFilesList');
        if (fileListElement) {
            const listItems = fileListElement.querySelectorAll('li');
            const ttrListItem = Array.from(listItems).find(item => {
                const fileNameSpan = item.querySelector('.file-name');
                return fileNameSpan && fileNameSpan.textContent.includes('TTR_11Aug2024.csv');
            });

            if (ttrListItem) {
                const hasRemovedClass = ttrListItem.classList.contains('removed-file');
                const hasProcessedClass = ttrListItem.classList.contains('processed-file');
                const statusElement = ttrListItem.querySelector('.file-status');
                const statusText = statusElement?.textContent || 'None';
                const buttonText = ttrListItem.querySelector('.remove-file')?.textContent || 'None';

                console.log(`  - CSS classes: ${hasRemovedClass ? 'removed-file' : ''} ${hasProcessedClass ? 'processed-file' : ''}`);
                console.log(`  - Status text: "${statusText}"`);
                console.log(`  - Button text: "${buttonText}"`);
                console.log(`  - UI matches expected: ${
                    isRemoved ? (hasRemovedClass && statusText === 'Removed' && buttonText === 'Delete') :
                    isProcessed ? (hasProcessedClass && statusText === 'Processed' && buttonText === 'Remove') :
                    (!hasRemovedClass && !hasProcessedClass && statusText === 'None' && buttonText === 'Remove')
                }`);
            } else {
                console.log('  - ❌ TTR file not found in UI');
            }
        } else {
            console.log('  - ❌ File list element not found');
        }

        // Provide fix suggestions
        if (!hasUnprocessed && !isRemoved) {
            console.log('');
            console.log('🔧 SUGGESTED FIXES:');
            console.log('1. forceResetFileProcessedStatus(0) - Reset processed status');
            console.log('2. testSingleFileActivation() - Auto-fix activation');
            console.log('3. Manual upload: Remove and re-upload the file');
        } else {
            console.log('');
            console.log('✅ TTR file appears to be correctly configured as active');
        }
    }
}

// Create a global instance of the FileHandler (with duplicate prevention)
if (!window.fileHandler) {
    window.fileHandler = new FileHandler();
    console.log('FileHandler instance created and assigned to window.fileHandler');
} else {
    console.log('FileHandler instance already exists, skipping creation');
}

// Production build - debug methods removed for security and performance

// Enhanced progress bar testing functions with comprehensive diagnostics
window.testProgressBar = () => {
    console.log('🧪 Testing progress bar functionality with enhanced diagnostics...');

    if (!window.fileHandler) {
        console.error('❌ FileHandler not initialized');
        return;
    }

    // Test 1: Basic visibility test
    console.log('Test 1: Basic visibility test');
    const showResult = window.fileHandler.showProgressBar();

    if (!showResult) {
        console.error('❌ Failed to show progress bar');
        return;
    }

    // Test 2: Element verification
    setTimeout(() => {
        console.log('Test 2: Element verification');
        const progressElement = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        if (!progressElement || !progressBar || !progressText) {
            console.error('❌ Missing progress elements');
            return;
        }

        const computedStyle = window.getComputedStyle(progressElement);
        console.log('Progress element computed styles:', {
            display: computedStyle.display,
            opacity: computedStyle.opacity,
            visibility: computedStyle.visibility,
            transform: computedStyle.transform
        });

        // Test 3: Progress animation
        console.log('Test 3: Progress animation');
        let progress = 0;
        const interval = setInterval(() => {
            progress += 10;

            progressBar.style.width = `${progress}%`;
            progressText.textContent = `${progress}% - Testing progress bar animation...`;

            console.log(`Progress updated to ${progress}%`);

            if (progress >= 100) {
                clearInterval(interval);
                console.log('✅ Progress animation test completed');

                // Test 4: Hide functionality
                setTimeout(() => {
                    console.log('Test 4: Hide functionality');
                    window.fileHandler.hideProgressBar();
                    console.log('✅ All progress bar tests completed successfully');
                }, 1500);
            }
        }, 300);
    }, 200);
};

window.showProgressBar = () => {
    if (window.fileHandler) {
        window.fileHandler.showProgressBar();
    } else {
        console.log('FileHandler not initialized');
    }
};

window.hideProgressBar = () => {
    if (window.fileHandler) {
        window.fileHandler.hideProgressBar();
    } else {
        console.log('FileHandler not initialized');
    }
};

// Diagnostic function for progress bar issues
window.diagnoseProgressBar = () => {
    console.log('🔍 PROGRESS BAR DIAGNOSTIC REPORT');
    console.log('================================');

    // Check HTML elements
    const progressElement = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    console.log('1. HTML Elements Check:');
    console.log(`   - uploadProgress element: ${progressElement ? '✅ Found' : '❌ Missing'}`);
    console.log(`   - progressBar element: ${progressBar ? '✅ Found' : '❌ Missing'}`);
    console.log(`   - progressText element: ${progressText ? '✅ Found' : '❌ Missing'}`);

    if (!progressElement) {
        console.error('❌ Critical: uploadProgress element not found in DOM');
        return;
    }

    // Check computed styles
    const computedStyle = window.getComputedStyle(progressElement);
    console.log('2. Computed Styles:');
    console.log(`   - display: ${computedStyle.display}`);
    console.log(`   - opacity: ${computedStyle.opacity}`);
    console.log(`   - visibility: ${computedStyle.visibility}`);
    console.log(`   - transform: ${computedStyle.transform}`);
    console.log(`   - z-index: ${computedStyle.zIndex}`);

    // Check inline styles
    console.log('3. Inline Styles:');
    console.log(`   - display: ${progressElement.style.display || 'not set'}`);
    console.log(`   - opacity: ${progressElement.style.opacity || 'not set'}`);
    console.log(`   - visibility: ${progressElement.style.visibility || 'not set'}`);

    // Check CSS classes
    console.log('4. CSS Classes:');
    console.log(`   - classList: ${Array.from(progressElement.classList).join(', ') || 'none'}`);
    console.log(`   - has 'visible': ${progressElement.classList.contains('visible')}`);
    console.log(`   - has 'show': ${progressElement.classList.contains('show')}`);
    console.log(`   - has 'force-show': ${progressElement.classList.contains('force-show')}`);

    // Check dimensions
    const rect = progressElement.getBoundingClientRect();
    console.log('5. Element Dimensions:');
    console.log(`   - width: ${rect.width}px`);
    console.log(`   - height: ${rect.height}px`);
    console.log(`   - top: ${rect.top}px`);
    console.log(`   - left: ${rect.left}px`);

    // Check if element is actually visible
    const isVisible = rect.width > 0 && rect.height > 0 &&
                     computedStyle.display !== 'none' &&
                     computedStyle.visibility !== 'hidden' &&
                     parseFloat(computedStyle.opacity) > 0;

    console.log('6. Visibility Assessment:');
    console.log(`   - Element is visible: ${isVisible ? '✅ YES' : '❌ NO'}`);

    if (!isVisible) {
        console.log('7. Suggested Fixes:');
        console.log('   - Run: window.testProgressBar() to test functionality');
        console.log('   - Run: window.showProgressBar() to force show');
        console.log('   - Check CSS for conflicting styles');
        console.log('   - Verify no parent elements are hiding the progress bar');
    }

    console.log('================================');
};

// Global TTR debugging function
window.debugTTRDataFlow = () => {
    console.log('🔍 TTR DATA FLOW DIAGNOSTIC REPORT');
    console.log('==================================');

    // Check FileHandler state
    console.log('1. FileHandler State:');
    if (window.fileHandler) {
        console.log(`   - processedData length: ${window.fileHandler.processedData?.length || 0}`);
        console.log(`   - fileMetrics size: ${window.fileHandler.fileMetrics?.size || 0}`);
        console.log(`   - fileTransactionData size: ${window.fileHandler.fileTransactionData?.size || 0}`);
        console.log(`   - TTR data ready: ${window.fileHandler.isTTRDataReady ? window.fileHandler.isTTRDataReady() : 'method not available'}`);

        if (window.fileHandler.processedData?.length > 0) {
            console.log('   - Processed files:', window.fileHandler.processedData.map(f => f.fileName));
        }
    } else {
        console.log('   - ❌ FileHandler not available');
    }

    // Check DataProcessor state
    console.log('2. DataProcessor State:');
    if (window.dataProcessor) {
        console.log(`   - rawData length: ${window.dataProcessor.rawData?.length || 0}`);
        console.log(`   - updateTTRSummaryReport available: ${typeof window.dataProcessor.updateTTRSummaryReport === 'function'}`);
        console.log(`   - forceTTRSummaryUpdate available: ${typeof window.dataProcessor.forceTTRSummaryUpdate === 'function'}`);
    } else {
        console.log('   - ❌ DataProcessor not available');
    }

    // Check TTR File Manager state
    console.log('3. TTR File Manager State:');
    if (window.ttrFileManager) {
        console.log(`   - includedFiles size: ${window.ttrFileManager.includedFiles?.size || 0}`);
        console.log(`   - excludedFiles size: ${window.ttrFileManager.excludedFiles?.size || 0}`);
        console.log(`   - getIncludedFiles available: ${typeof window.ttrFileManager.getIncludedFiles === 'function'}`);
        console.log(`   - forceUpdateTTRSummaryReport available: ${typeof window.ttrFileManager.forceUpdateTTRSummaryReport === 'function'}`);

        if (window.ttrFileManager.getIncludedFiles) {
            const includedFiles = window.ttrFileManager.getIncludedFiles();
            console.log(`   - Included files count: ${includedFiles.length}`);
            if (includedFiles.length > 0) {
                console.log('   - Included files:', includedFiles.map(f => f.fileName));
            }
        }
    } else {
        console.log('   - ❌ TTR File Manager not available');
    }

    // Check TTR table element
    console.log('4. TTR Table Element:');
    const ttrTableBody = document.getElementById('ttrSummaryTableBody');
    if (ttrTableBody) {
        console.log(`   - ✅ TTR table body found`);
        console.log(`   - Children count: ${ttrTableBody.children.length}`);
        console.log(`   - innerHTML length: ${ttrTableBody.innerHTML.length}`);
    } else {
        console.log('   - ❌ TTR table body not found');
    }

    // Suggested actions
    console.log('5. Suggested Actions:');
    console.log('   - Run: window.fileHandler.forceTTRSummaryUpdate() to force update');
    console.log('   - Run: window.ttrFileManager.forceUpdateTTRSummaryReport() to force TTR manager update');
    console.log('   - Run: window.dataProcessor.forceTTRSummaryUpdate() to force data processor update');
    console.log('   - Run: window.refreshTTRTable() to refresh TTR table');

    console.log('==================================');
};
