<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Calculation Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        .button.danger {
            background: #dc3545;
        }
        .results {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HOC/IBD/WU Currency Calculation Fix Test</h1>
        <p>This page tests the currency calculation fixes for the Financial Transaction Dashboard.</p>
        
        <div class="test-section">
            <h3>Test Functions</h3>
            <button class="button" onclick="runTest()">Run Currency Calculation Test</button>
            <button class="button warning" onclick="fixCalculations()">Fix Currency Calculations</button>
            <button class="button danger" onclick="fixNetAmounts()">Fix Net Amounts (Credit-Debit)</button>
            <button class="button danger" onclick="fixWUProcessing()">Fix WU Transaction Processing</button>
            <button class="button success" onclick="validateCalculations()">Validate Calculations</button>
            <button class="button success" onclick="validateCalculations(true)">Silent Validate</button>
            <button class="button" onclick="diagnoseWU()">Diagnose WU Transactions</button>
            <button class="button" onclick="showCurrentMetrics()">Show Current Metrics</button>
        </div>

        <div class="test-section">
            <h3>Instructions</h3>
            <ol>
                <li>First, load some CSV files in the main dashboard</li>
                <li>Come back to this test page</li>
                <li>Click "Diagnose WU Transactions" to check if WU transactions are being processed correctly</li>
                <li><strong>If WU shows 0 but diagnosis finds WU transactions:</strong> Click "Fix WU Transaction Processing"</li>
                <li>Click "Run Currency Calculation Test" to check if calculations are correct</li>
                <li>If you see discrepancy warnings, click "Fix Net Amounts" to fix the credit-debit calculation</li>
                <li>If other issues are found, click "Fix Currency Calculations" to fix them</li>
                <li>Run the test again to verify the fix worked</li>
            </ol>
            <p><strong>Note:</strong> The "Fix Net Amounts" button specifically fixes the issue where currency totals were calculated as credit+debit instead of credit-debit.</p>
            <p><strong>WU Issue:</strong> If WU transactions show 0 in the UI but exist in the data, use "Fix WU Transaction Processing" to recalculate WU metrics from raw data.</p>
        </div>

        <div id="results" class="results" style="display: none;"></div>
        <div id="status"></div>
    </div>

    <script>
        // Check if we're in the context of the main application
        function checkDataProcessor() {
            if (typeof window.parent !== 'undefined' && window.parent.dataProcessor) {
                return window.parent.dataProcessor;
            } else if (typeof window.dataProcessor !== 'undefined') {
                return window.dataProcessor;
            } else {
                showStatus('DataProcessor not found. Please ensure you have loaded CSV files in the main dashboard first.', 'error');
                return null;
            }
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function showResults(content) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = content;
        }

        function runTest() {
            const dataProcessor = checkDataProcessor();
            if (!dataProcessor) return;

            showStatus('Running currency calculation test...', 'info');
            
            try {
                const result = dataProcessor.testCurrencyCalculations();
                if (result) {
                    showStatus('✓ All currency calculations are correct!', 'success');
                } else {
                    showStatus('✗ Currency calculation issues found. Click "Fix Currency Calculations" to resolve.', 'error');
                }
            } catch (error) {
                showStatus(`Error running test: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }

        function fixCalculations() {
            const dataProcessor = checkDataProcessor();
            if (!dataProcessor) return;

            showStatus('Fixing currency calculations...', 'info');

            try {
                dataProcessor.fixCurrencyCalculations();
                showStatus('✓ Currency calculations have been fixed!', 'success');
            } catch (error) {
                showStatus(`Error fixing calculations: ${error.message}`, 'error');
                console.error('Fix error:', error);
            }
        }

        function fixNetAmounts() {
            const dataProcessor = checkDataProcessor();
            if (!dataProcessor) return;

            showStatus('Fixing currency net amounts (credit - debit)...', 'info');

            try {
                const fixedCount = dataProcessor.fixCurrencyNetAmounts();
                if (fixedCount > 0) {
                    showStatus(`✓ Fixed ${fixedCount} currency net amount calculations!`, 'success');
                } else {
                    showStatus('✓ All currency net amounts are already correct!', 'success');
                }
            } catch (error) {
                showStatus(`Error fixing net amounts: ${error.message}`, 'error');
                console.error('Net amount fix error:', error);
            }
        }

        function fixWUProcessing() {
            const dataProcessor = checkDataProcessor();
            if (!dataProcessor) return;

            showStatus('Fixing WU transaction processing...', 'info');

            try {
                const success = dataProcessor.fixWUTransactionProcessing();
                if (success) {
                    showStatus('✓ WU transaction processing has been fixed!', 'success');
                } else {
                    showStatus('⚠️ No WU transactions found to process.', 'info');
                }
            } catch (error) {
                showStatus(`Error fixing WU processing: ${error.message}`, 'error');
                console.error('WU processing fix error:', error);
            }
        }

        function validateCalculations(silent = false) {
            const dataProcessor = checkDataProcessor();
            if (!dataProcessor) return;

            showStatus(silent ? 'Running silent validation...' : 'Validating and auto-fixing calculations...', 'info');

            try {
                const issuesFixed = dataProcessor.validateAndFixCurrencyCalculations(silent);
                if (issuesFixed > 0) {
                    showStatus(`✓ Validation complete! Fixed ${issuesFixed} issues.`, 'success');
                } else {
                    showStatus('✓ Validation complete! No issues found.', 'success');
                }
            } catch (error) {
                showStatus(`Error validating calculations: ${error.message}`, 'error');
                console.error('Validation error:', error);
            }
        }

        function diagnoseWU() {
            const dataProcessor = checkDataProcessor();
            if (!dataProcessor) return;

            showStatus('Diagnosing WU transaction processing...', 'info');

            try {
                const diagnosis = dataProcessor.diagnoseWUTransactions();
                if (diagnosis) {
                    let output = 'WU Transaction Diagnosis Results:\n\n';
                    output += `Total WU transactions in raw data: ${diagnosis.totalWUInRawData}\n`;
                    output += `WU transactions in metrics: ${diagnosis.wuInMetrics}\n`;
                    output += `Discrepancy detected: ${diagnosis.discrepancy ? 'YES' : 'NO'}\n\n`;

                    output += 'WU transactions by currency:\n';
                    Object.keys(diagnosis.wuByCurrency).forEach(currency => {
                        const data = diagnosis.wuByCurrency[currency];
                        output += `  ${currency}: Count=${data.count}, Amount=${data.amount.toLocaleString()}\n`;
                        output += `    Credit=${data.credit.toLocaleString()}, Debit=${data.debit.toLocaleString()}\n`;
                    });

                    if (diagnosis.sampleTransactions.length > 0) {
                        output += '\nSample WU transactions:\n';
                        diagnosis.sampleTransactions.forEach((sample, i) => {
                            output += `  ${i + 1}. ${sample.amount.toLocaleString()} ${sample.currency} (${sample.type})\n`;
                        });
                    }

                    showResults(output);

                    if (diagnosis.discrepancy) {
                        showStatus('⚠️ WU transaction discrepancy found! Check results below and consider running fixes.', 'error');
                    } else {
                        showStatus('✓ WU transaction processing appears correct.', 'success');
                    }
                } else {
                    showStatus('No diagnosis data returned.', 'error');
                }
            } catch (error) {
                showStatus(`Error diagnosing WU transactions: ${error.message}`, 'error');
                console.error('WU diagnosis error:', error);
            }
        }

        function showCurrentMetrics() {
            const dataProcessor = checkDataProcessor();
            if (!dataProcessor) return;

            try {
                const metrics = dataProcessor.summaryMetrics;
                const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
                
                let output = 'Current Currency Metrics:\n\n';
                
                supportedCurrencies.forEach(currency => {
                    const hocCredit = metrics[`hocCreditAmount${currency}`] || 0;
                    const hocDebit = metrics[`hocDebitAmount${currency}`] || 0;
                    const ibdCredit = metrics[`ibdCreditAmount${currency}`] || 0;
                    const ibdDebit = metrics[`ibdDebitAmount${currency}`] || 0;
                    const wuCredit = metrics[`wuCreditAmount${currency}`] || 0;
                    const wuDebit = metrics[`wuDebitAmount${currency}`] || 0;
                    
                    if (hocCredit > 0 || hocDebit > 0 || ibdCredit > 0 || ibdDebit > 0 || wuCredit > 0 || wuDebit > 0) {
                        output += `${currency}:\n`;
                        output += `  HOC: Credit=${hocCredit.toLocaleString()}, Debit=${hocDebit.toLocaleString()}\n`;
                        output += `  IBD: Credit=${ibdCredit.toLocaleString()}, Debit=${ibdDebit.toLocaleString()}\n`;
                        output += `  WU:  Credit=${wuCredit.toLocaleString()}, Debit=${wuDebit.toLocaleString()}\n\n`;
                    }
                });
                
                if (output === 'Current Currency Metrics:\n\n') {
                    output += 'No currency data found. Please load CSV files first.';
                }
                
                showResults(output);
                showStatus('Current metrics displayed below', 'info');
            } catch (error) {
                showStatus(`Error showing metrics: ${error.message}`, 'error');
                console.error('Metrics error:', error);
            }
        }

        // Auto-check on page load
        window.addEventListener('load', () => {
            const dataProcessor = checkDataProcessor();
            if (dataProcessor) {
                showStatus('DataProcessor found! Ready to test currency calculations.', 'success');
            }
        });
    </script>
</body>
</html>
