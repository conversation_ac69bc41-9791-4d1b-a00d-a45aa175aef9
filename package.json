{"name": "financial-transaction-dashboard", "version": "2.5.7", "description": "Financial Transaction Dashboard V2.5.7 Production - Streamlined financial analytics with advanced TTR monitoring (visualization-free)", "main": "application.html", "scripts": {"start": "python -m http.server 8000", "serve": "npx http-server -p 8000 -c-1", "production": "npx http-server -p 80 -c 3600"}, "keywords": ["financial", "transaction", "dashboard", "analytics", "csv", "data-processing", "ttr-monitoring", "aml-compliance", "production-ready"], "author": "Financial Dashboard Team", "license": "MIT", "dependencies": {"xlsx": "^0.18.5"}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/your-org/financial-transaction-dashboard.git"}, "bugs": {"url": "https://github.com/your-org/financial-transaction-dashboard/issues"}, "homepage": "https://github.com/your-org/financial-transaction-dashboard#readme", "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "files": ["application.html", "index.html", "js/**/*.js", "css/**/*.css", "workers/**/*.js", "tests/**/*.html", "tests/**/*.js", "docs/**/*.md", ".htpasswd.example", "package.json"]}