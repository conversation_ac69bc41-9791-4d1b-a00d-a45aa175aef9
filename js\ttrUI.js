/**
 * TTR UI Component
 * Handles user interface interactions for TTR monitoring
 */

class TTRUI {
    constructor(ttrMonitoring) {
        this.ttrMonitoring = ttrMonitoring;
        this.currentPage = 1;
        this.rowsPerPage = 10;
        this.sortColumn = 'timestamp';
        this.sortDirection = 'desc';

        // Category state tracking for expandable/collapsible functionality
        this.categoryStates = {
            'threshold': false,    // Threshold Breach Detection
            'structuring': false,  // Structuring/Smurfing Detection
            'velocity': false,     // Velocity-Based Monitoring
            'pattern': false,      // Suspicious Pattern Analysis
            'behavioral': false    // Behavioral Anomaly Detection
        };

        this.init();
    }

    // Initialize UI components
    init() {
        this.setupTablePagination();
        this.setupSorting();
        this.setupAlertActions();
    }

    // Set up table pagination
    setupTablePagination() {
        // This would be implemented when we add pagination controls to the HTML
        // For now, we'll just display all alerts
    }

    // Set up table sorting
    setupSorting() {
        const table = document.getElementById('ttrAlertsTable');
        if (!table) return;

        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(index);
            });
        });
    }

    // Set up alert action buttons
    setupAlertActions() {
        // Remove any existing event listeners to prevent duplicates
        document.removeEventListener('click', this.alertActionHandler);

        // Create a bound handler function
        this.alertActionHandler = (event) => {
            const target = event.target;

            // Find the button that was clicked (could be the button or a child element)
            const button = target.closest('.view-alert-btn, .dismiss-alert-btn, .expand-alert-btn, .category-toggle-btn');

            if (!button) return; // Not a button we're interested in

            // Get the alert ID from the button's data attribute
            const alertId = button.dataset.alertId;
            const categoryType = button.dataset.categoryType;

            console.log(`TTR UI: Button clicked - Type: ${button.className}, Alert ID: ${alertId}, Category: ${categoryType}`);

            try {
                if (button.classList.contains('view-alert-btn')) {
                    console.log(`TTR UI: Viewing alert details for ${alertId}`);
                    this.viewAlertDetails(alertId);
                } else if (button.classList.contains('dismiss-alert-btn')) {
                    console.log(`TTR UI: Dismissing alert ${alertId}`);
                    this.dismissAlert(alertId);
                } else if (button.classList.contains('expand-alert-btn')) {
                    console.log(`TTR UI: Expanding consolidated alert ${alertId}`);
                    this.expandConsolidatedAlert(alertId);
                } else if (button.classList.contains('category-toggle-btn')) {
                    console.log(`TTR UI: Toggling category ${categoryType}`);
                    this.toggleCategory(categoryType);
                }
            } catch (error) {
                console.error('TTR UI: Error handling button click:', error);
                if (window.app && window.app.showNotification) {
                    window.app.showNotification('Error processing action: ' + error.message, 'error');
                }
            }
        };

        // Add the event listener
        document.addEventListener('click', this.alertActionHandler);

        console.log('TTR UI: Alert action handlers initialized');
    }

    // Get category mapping for alert types
    getCategoryMapping() {
        return {
            'threshold': {
                name: 'Threshold Breach Detection',
                icon: '⚠️'
            },
            'structuring': {
                name: 'Structuring/Smurfing Detection',
                icon: '🔍'
            },
            'velocity': {
                name: 'Velocity-Based Monitoring',
                icon: '⚡'
            },
            'pattern': {
                name: 'Suspicious Pattern Analysis',
                icon: '📊'
            },
            'behavioral': {
                name: 'Behavioral Anomaly Detection',
                icon: '🎯'
            }
        };
    }

    // Update the alerts table with category grouping
    updateAlertsTable(alerts) {
        const tableBody = document.getElementById('ttrAlertsTableBody');
        if (!tableBody) return;

        // Clear existing content
        tableBody.innerHTML = '';

        if (!alerts || alerts.length === 0) {
            // Determine column count based on table structure
            const includeFileId = document.querySelector('th:nth-child(8)')?.textContent === 'File ID';
            const colSpan = includeFileId ? 9 : 8;

            const emptyRow = document.createElement('tr');
            emptyRow.className = 'empty-table-message';
            emptyRow.innerHTML = `<td colspan="${colSpan}">No alerts match the current filters.</td>`;
            tableBody.appendChild(emptyRow);
            return;
        }

        // Group alerts by category
        const groupedAlerts = this.groupAlertsByCategory(alerts);
        const categoryMapping = this.getCategoryMapping();

        // Process each category
        Object.keys(categoryMapping).forEach(categoryType => {
            const categoryAlerts = groupedAlerts[categoryType] || [];

            // Skip empty categories
            if (categoryAlerts.length === 0) return;

            // Sort alerts within this category
            const sortedCategoryAlerts = this.sortAlerts(categoryAlerts);

            // Create category header
            const categoryHeader = this.createCategoryHeader(categoryType, categoryAlerts.length);
            tableBody.appendChild(categoryHeader);

            // Create alert rows for this category (if expanded)
            if (this.categoryStates[categoryType]) {
                sortedCategoryAlerts.forEach(alert => {
                    const row = this.createAlertRow(alert);
                    row.classList.add(`category-${categoryType}`);
                    tableBody.appendChild(row);
                });
            }
        });
    }

    // Group alerts by their category type
    groupAlertsByCategory(alerts) {
        const grouped = {};

        alerts.forEach(alert => {
            let categoryType;

            if (alert.isConsolidated) {
                // For consolidated alerts, use the primary type (first alert type)
                categoryType = alert.alertTypes[0]?.type || 'behavioral';
            } else {
                categoryType = alert.type || 'behavioral';
            }

            if (!grouped[categoryType]) {
                grouped[categoryType] = [];
            }
            grouped[categoryType].push(alert);
        });

        return grouped;
    }

    // Create a category header row
    createCategoryHeader(categoryType, alertCount) {
        const categoryMapping = this.getCategoryMapping();
        const category = categoryMapping[categoryType];
        const isExpanded = this.categoryStates[categoryType];

        // Determine column count based on table structure
        const includeFileId = document.querySelector('th:nth-child(8)')?.textContent === 'File ID';
        const colSpan = includeFileId ? 9 : 8;

        const headerRow = document.createElement('tr');
        headerRow.className = 'category-header-row';
        headerRow.dataset.categoryType = categoryType;

        headerRow.innerHTML = `
            <td colspan="${colSpan}" class="category-header">
                <div class="category-header-content">
                    <button class="category-toggle-btn" data-category-type="${categoryType}" aria-expanded="${isExpanded}">
                        <span class="category-icon">${category.icon}</span>
                        <span class="category-name">${category.name}</span>
                        <span class="category-count-badge">${alertCount}</span>
                        <span class="toggle-icon">${isExpanded ? '▼' : '▶'}</span>
                    </button>
                </div>
            </td>
        `;

        return headerRow;
    }

    // Toggle category expand/collapse state
    toggleCategory(categoryType) {
        // Toggle the state
        this.categoryStates[categoryType] = !this.categoryStates[categoryType];

        // Update the table to reflect the new state
        if (this.ttrMonitoring) {
            this.updateAlertsTable(this.ttrMonitoring.getFilteredAlerts());
        }
    }

    // Create a table row for an alert
    createAlertRow(alert) {
        const row = document.createElement('tr');
        row.className = `alert-row priority-${alert.priority}`;

        const priorityClass = `priority-${alert.priority}`;
        const priorityText = (alert.priority || 'low').toUpperCase();

        // Check if we need to include fileId column (for testing)
        const includeFileId = document.querySelector('th:nth-child(8)')?.textContent === 'File ID';

        if (includeFileId) {
            // Create cells safely using DOM methods
            const cells = [
                this.createTextCell('alert-id', alert.id || ''),
                this.createPriorityCell(priorityClass, alert.priority, priorityText),
                this.createTextCell('alert-type', this.formatAlertType(alert.type, alert.subtype, alert)),
                this.createTextCell('customer-name', alert.customerName || 'Unknown'),
                this.createTextCell('alert-amount', this.formatCurrency(alert.amount || 0)),
                this.createTextCell('alert-date', this.formatDate(alert.date)),
                this.createScoreCell(alert.riskScore),
                this.createTextCell('file-id', alert.fileId || 'Unknown'),
                this.createActionCellWithExpand(alert.id, alert.isConsolidated)
            ];

            cells.forEach(cell => row.appendChild(cell));
        } else {
            // Create cells safely using DOM methods (without file ID)
            const cells = [
                this.createTextCell('alert-id', alert.id || ''),
                this.createPriorityCell(priorityClass, alert.priority, priorityText),
                this.createTextCell('alert-type', this.formatAlertType(alert.type, alert.subtype, alert)),
                this.createTextCell('customer-name', alert.customerName || 'Unknown'),
                this.createTextCell('alert-amount', this.formatCurrency(alert.amount || 0)),
                this.createTextCell('alert-date', this.formatDate(alert.date)),
                this.createScoreCell(alert.riskScore),
                this.createActionCellWithExpand(alert.id, alert.isConsolidated)
            ];

            cells.forEach(cell => row.appendChild(cell));
        }

        return row;
    }

    // Sort alerts based on current sort settings
    sortAlerts(alerts) {
        return [...alerts].sort((a, b) => {
            let aValue, bValue;

            switch (this.sortColumn) {
                case 0: // Alert ID
                    aValue = a.id || '';
                    bValue = b.id || '';
                    break;
                case 1: // Priority
                    const priorityOrder = { high: 3, medium: 2, low: 1 };
                    aValue = priorityOrder[a.priority] || 0;
                    bValue = priorityOrder[b.priority] || 0;
                    break;
                case 2: // Type
                    aValue = a.type || '';
                    bValue = b.type || '';
                    break;
                case 3: // Customer
                    aValue = a.customerName || '';
                    bValue = b.customerName || '';
                    break;
                case 4: // Amount
                    aValue = a.amount || 0;
                    bValue = b.amount || 0;
                    break;
                case 5: // Date
                    aValue = new Date(a.date || 0);
                    bValue = new Date(b.date || 0);
                    break;
                case 6: // Score
                    aValue = a.riskScore || 0;
                    bValue = b.riskScore || 0;
                    break;
                default:
                    aValue = new Date(a.timestamp || 0);
                    bValue = new Date(b.timestamp || 0);
            }

            if (aValue < bValue) return this.sortDirection === 'asc' ? -1 : 1;
            if (aValue > bValue) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });
    }

    // Handle table sorting
    sortTable(columnIndex) {
        if (this.sortColumn === columnIndex) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = columnIndex;
            this.sortDirection = 'desc';
        }

        // Update table with current alerts
        if (this.ttrMonitoring) {
            this.updateAlertsTable(this.ttrMonitoring.getFilteredAlerts());
        }

        // Update sort indicators
        this.updateSortIndicators();
    }

    // Update sort indicators in table headers
    updateSortIndicators() {
        const table = document.getElementById('ttrAlertsTable');
        if (!table) return;

        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.classList.remove('sort-asc', 'sort-desc');
            if (index === this.sortColumn) {
                header.classList.add(`sort-${this.sortDirection}`);
            }
        });
    }

    // View alert details
    viewAlertDetails(alertId) {
        // Use the comprehensive getAllAlerts method to find the alert
        const alert = this.ttrMonitoring.getAllAlerts ?
                     this.ttrMonitoring.getAllAlerts().find(a => a.id === alertId) :
                     (this.ttrMonitoring.getAlerts().find(a => a.id === alertId) ||
                      this.ttrMonitoring.getFilteredAlerts().find(a => a.id === alertId));

        if (!alert) {
            console.error(`TTR UI: Alert ${alertId} not found for viewing details`);
            return;
        }

        const modalContent = this.createAlertDetailsModal(alert);
        this.showModal('Alert Details', modalContent);
    }

    // Expand consolidated alert to show individual alerts
    expandConsolidatedAlert(alertId) {
        // Use the comprehensive getAllAlerts method to find the consolidated alert
        const alert = this.ttrMonitoring.getAllAlerts ?
                     this.ttrMonitoring.getAllAlerts().find(a => a.id === alertId) :
                     this.ttrMonitoring.getFilteredAlerts().find(a => a.id === alertId);

        if (!alert || !alert.isConsolidated) {
            console.error(`TTR UI: Consolidated alert ${alertId} not found or not consolidated`);
            return;
        }

        const modalContent = this.createConsolidatedAlertModal(alert);
        this.showModal('Consolidated Alert Details', modalContent);
    }

    // Create alert details modal content
    createAlertDetailsModal(alert) {
        const isConsolidated = alert.isConsolidated;

        return `
            <div class="alert-details">
                <div class="alert-header">
                    <h3>Alert ${alert.id}</h3>
                    <span class="priority-badge priority-${alert.priority}">${(alert.priority || 'low').toUpperCase()}</span>
                    ${isConsolidated ? '<span class="consolidated-badge">CONSOLIDATED</span>' : ''}
                </div>

                <div class="alert-info-grid">
                    <div class="info-item">
                        <label>Type:</label>
                        <span>${this.formatAlertType(alert.type, alert.subtype, alert)}</span>
                    </div>
                    <div class="info-item">
                        <label>Risk Score:</label>
                        <span class="score-badge score-${this.getScoreClass(alert.riskScore)}">${alert.riskScore || 0}</span>
                    </div>
                    <div class="info-item">
                        <label>Customer:</label>
                        <span>${alert.customerName || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>Amount:</label>
                        <span>${this.formatCurrency(alert.amount || 0)}</span>
                    </div>
                    <div class="info-item">
                        <label>Date:</label>
                        <span>${this.formatDate(alert.date)}</span>
                    </div>
                    <div class="info-item">
                        <label>Status:</label>
                        <span>${alert.status || 'Active'}</span>
                    </div>
                    ${isConsolidated ? `
                    <div class="info-item">
                        <label>Individual Alerts:</label>
                        <span>${alert.individualAlerts.length} alerts</span>
                    </div>
                    ` : ''}
                </div>

                <div class="alert-description">
                    <label>Description:</label>
                    <p>${alert.description || 'No description available'}</p>
                </div>

                ${isConsolidated ? this.createConsolidatedRulesSection(alert.alertTypes) : ''}
                ${alert.metadata ? this.createMetadataSection(alert.metadata) : ''}

                <div class="alert-actions-modal">
                    <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">Close</button>
                    ${isConsolidated ?
                        `<button class="btn btn-info" onclick="this.closest('.modal').remove(); window.ttrUI.expandConsolidatedAlert('${alert.id}')">View Individual Alerts</button>` : ''
                    }
                    <button class="btn dismiss-alert-btn" data-alert-id="${alert.id}">Dismiss Alert</button>
                </div>
            </div>
        `;
    }

    // Create consolidated alert modal showing individual alerts
    createConsolidatedAlertModal(alert) {
        const individualAlertsHtml = alert.individualAlerts.map((individualAlert, index) => `
            <div class="individual-alert-card">
                <div class="individual-alert-header">
                    <h4>Alert ${index + 1}: ${this.formatAlertType(individualAlert.type, individualAlert.subtype)}</h4>
                    <span class="priority-badge priority-${individualAlert.priority}">${(individualAlert.priority || 'low').toUpperCase()}</span>
                </div>
                <div class="individual-alert-details">
                    <div class="detail-row">
                        <label>Alert ID:</label>
                        <span>${individualAlert.id}</span>
                    </div>
                    <div class="detail-row">
                        <label>Amount:</label>
                        <span>${this.formatCurrency(individualAlert.amount || 0)}</span>
                    </div>
                    <div class="detail-row">
                        <label>Risk Score:</label>
                        <span class="score-badge score-${this.getScoreClass(individualAlert.riskScore)}">${individualAlert.riskScore || 0}</span>
                    </div>
                    <div class="detail-row">
                        <label>Description:</label>
                        <span>${individualAlert.description}</span>
                    </div>
                </div>
            </div>
        `).join('');

        return `
            <div class="consolidated-alert-details">
                <div class="consolidated-header">
                    <h3>Consolidated Alert: ${alert.customerName}</h3>
                    <div class="consolidated-summary">
                        <span class="priority-badge priority-${alert.priority}">${(alert.priority || 'low').toUpperCase()}</span>
                        <span class="consolidated-count">${alert.individualAlerts.length} Individual Alerts</span>
                        <span class="consolidated-total">Total: ${this.formatCurrency(alert.totalAmount || 0)}</span>
                    </div>
                </div>

                <div class="individual-alerts-container">
                    ${individualAlertsHtml}
                </div>

                <div class="alert-actions-modal">
                    <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">Close</button>
                    <button class="btn dismiss-alert-btn" data-alert-id="${alert.id}">Dismiss All Alerts</button>
                </div>
            </div>
        `;
    }

    // Create consolidated rules section
    createConsolidatedRulesSection(alertTypes) {
        const rulesHtml = alertTypes.map(alertType => `
            <div class="rule-item">
                <span class="rule-type">${this.formatAlertType(alertType.type, alertType.subtype)}</span>
                <span class="rule-amount">${this.formatCurrency(alertType.amount || 0)}</span>
                <span class="rule-score score-badge score-${this.getScoreClass(alertType.riskScore)}">${alertType.riskScore || 0}</span>
            </div>
        `).join('');

        return `
            <div class="consolidated-rules">
                <label>Triggered Rules:</label>
                <div class="rules-list">
                    ${rulesHtml}
                </div>
            </div>
        `;
    }

    // Create metadata section for alert details
    createMetadataSection(metadata) {
        const metadataItems = Object.entries(metadata)
            .filter(([key]) => key !== 'transactions') // Exclude transaction arrays for display
            .map(([key, value]) => `
                <div class="metadata-item">
                    <label>${this.formatMetadataKey(key)}:</label>
                    <span>${value}</span>
                </div>
            `).join('');

        return `
            <div class="alert-metadata">
                <label>Additional Information:</label>
                <div class="metadata-grid">
                    ${metadataItems}
                </div>
            </div>
        `;
    }

    // Dismiss an alert
    dismissAlert(alertId) {
        if (!alertId) {
            console.error('TTR UI: No alert ID provided for dismissal');
            return;
        }

        console.log(`TTR UI: Attempting to dismiss alert ${alertId}`);

        if (!confirm('Are you sure you want to dismiss this alert?')) return;

        try {
            // Check if alert exists using the comprehensive getAllAlerts method
            const alert = this.ttrMonitoring.getAllAlerts ?
                         this.ttrMonitoring.getAllAlerts().find(a => a.id === alertId) :
                         (this.ttrMonitoring.getAlerts().find(a => a.id === alertId) ||
                          this.ttrMonitoring.getFilteredAlerts().find(a => a.id === alertId));

            if (!alert) {
                console.error(`TTR UI: Alert ${alertId} not found`);
                if (window.app && window.app.showNotification) {
                    window.app.showNotification('Alert not found', 'error');
                }
                return;
            }

            // Use the enhanced TTRMonitoring dismissAlert method which handles both individual and consolidated alerts
            const success = this.ttrMonitoring.dismissAlert(alertId);

            if (success) {
                // Close modal if open
                const modal = document.querySelector('.modal');
                if (modal) modal.remove();

                // Force UI refresh to ensure the alert is removed from display
                this.updateAlertsTable(this.ttrMonitoring.getFilteredAlerts());

                // Update summary metrics
                this.ttrMonitoring.updateUI();

                const message = alert.isConsolidated ?
                    `Consolidated alert and all individual alerts dismissed successfully` :
                    'Alert dismissed successfully';

                if (window.app && window.app.showNotification) {
                    window.app.showNotification(message, 'success');
                }

                console.log(`TTR UI: Alert ${alertId} dismissed successfully`);
            } else {
                console.error(`TTR UI: Failed to dismiss alert ${alertId}`);
                if (window.app && window.app.showNotification) {
                    window.app.showNotification('Failed to dismiss alert', 'error');
                }
            }
        } catch (error) {
            console.error('TTR UI: Error dismissing alert:', error);
            if (window.app && window.app.showNotification) {
                window.app.showNotification('Error dismissing alert: ' + error.message, 'error');
            }
        }
    }

    // Show modal dialog - XSS-safe implementation
    showModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal';

        // Create modal structure safely using DOM methods
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        const modalHeader = document.createElement('div');
        modalHeader.className = 'modal-header';

        const titleElement = document.createElement('h2');
        titleElement.textContent = this.sanitizeText(title); // Safe text assignment

        const closeButton = document.createElement('button');
        closeButton.className = 'modal-close';
        closeButton.textContent = '×';
        closeButton.setAttribute('aria-label', 'Close modal');
        closeButton.addEventListener('click', () => modal.remove());

        const modalBody = document.createElement('div');
        modalBody.className = 'modal-body';

        // Handle content safely - check if it's HTML or text
        if (typeof content === 'string') {
            // If content contains HTML tags, sanitize it
            if (this.containsHTML(content)) {
                modalBody.innerHTML = this.sanitizeHTML(content);
            } else {
                modalBody.textContent = content;
            }
        } else if (content instanceof HTMLElement) {
            modalBody.appendChild(content);
        } else {
            modalBody.textContent = String(content);
        }

        // Assemble modal structure
        modalHeader.appendChild(titleElement);
        modalHeader.appendChild(closeButton);
        modalContent.appendChild(modalHeader);
        modalContent.appendChild(modalBody);
        modal.appendChild(modalContent);

        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.remove();
            }
        });

        // Add keyboard support for accessibility
        modal.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                modal.remove();
            }
        });

        // Focus management for accessibility
        closeButton.focus();
    }

    // Sanitize text content to prevent XSS
    sanitizeText(text) {
        if (!text) return '';
        return String(text)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    // Check if string contains HTML tags
    containsHTML(str) {
        return /<[^>]*>/g.test(str);
    }

    // Sanitize HTML content (basic implementation)
    sanitizeHTML(html) {
        // Create a temporary div to parse HTML
        const temp = document.createElement('div');
        temp.innerHTML = html;

        // Remove script tags and event handlers
        const scripts = temp.querySelectorAll('script');
        scripts.forEach(script => script.remove());

        // Remove dangerous attributes
        const allElements = temp.querySelectorAll('*');
        allElements.forEach(element => {
            // Remove event handler attributes
            const attributes = Array.from(element.attributes);
            attributes.forEach(attr => {
                if (attr.name.startsWith('on') || attr.name === 'javascript:') {
                    element.removeAttribute(attr.name);
                }
            });

            // Remove dangerous elements
            if (['script', 'iframe', 'object', 'embed', 'form'].includes(element.tagName.toLowerCase())) {
                element.remove();
            }
        });

        return temp.innerHTML;
    }

    // Utility methods
    formatAlertType(type, subtype, alert = null) {
        // Handle consolidated alerts
        if (type === 'consolidated' && alert && alert.isConsolidated) {
            const count = alert.alertTypes.length;
            return `Consolidated (${count} rule${count > 1 ? 's' : ''})`;
        }

        const typeMap = {
            threshold: 'Threshold Breach',
            structuring: 'Structuring',
            velocity: 'Velocity Anomaly',
            pattern: 'Suspicious Pattern',
            behavioral: 'Behavioral Anomaly',
            consolidated: 'Consolidated Alert'
        };

        const subtypeMap = {
            single_transaction: 'Single Transaction',
            daily_cumulative: 'Daily Cumulative',
            threshold_avoidance: 'Threshold Avoidance',
            high_frequency: 'High Frequency',
            burst_activity: 'Burst Activity',
            round_amounts: 'Round Amounts',
            volume_anomaly: 'Volume Anomaly',
            multiple_rules: 'Multiple Rules'
        };

        const typeText = typeMap[type] || type || 'Unknown';
        const subtypeText = subtypeMap[subtype] || subtype;

        return subtypeText ? `${typeText} (${subtypeText})` : typeText;
    }

    formatCurrency(amount, currency = 'MMK') {
        const supportedCurrencies = ['USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

        if (currency === 'MMK') {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount) + ' MMK';
        } else if (supportedCurrencies.includes(currency)) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency,
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        } else {
            // Fallback for unsupported currencies
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount) + ` ${currency}`;
        }
    }

    formatDate(dateString) {
        if (!dateString || dateString === 'Unknown') return 'Unknown';

        try {
            // Handle YYYY-MM-DD format (from TTR rules)
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                const date = new Date(dateString);
                if (!isNaN(date.getTime())) {
                    return date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });
                }
            }

            // Handle DD-MMM-YY format (legacy)
            if (/^\d{2}-[A-Z]{3}-\d{2}$/.test(dateString)) {
                const [day, month, year] = dateString.split('-');
                const monthNames = {
                    'JAN': 'Jan', 'FEB': 'Feb', 'MAR': 'Mar', 'APR': 'Apr',
                    'MAY': 'May', 'JUN': 'Jun', 'JUL': 'Jul', 'AUG': 'Aug',
                    'SEP': 'Sep', 'OCT': 'Oct', 'NOV': 'Nov', 'DEC': 'Dec'
                };
                const fullYear = parseInt(year) < 50 ? `20${year}` : `19${year}`;
                return `${monthNames[month] || month} ${parseInt(day)}, ${fullYear}`;
            }

            // Try to parse as a standard date
            const date = new Date(dateString);
            if (!isNaN(date.getTime())) {
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
            }

            // Return original string if all else fails
            return dateString;
        } catch (error) {
            console.warn('Error formatting date:', dateString, error);
            return dateString;
        }
    }

    formatMetadataKey(key) {
        return key.replace(/([A-Z])/g, ' $1')
                  .replace(/^./, str => str.toUpperCase())
                  .replace(/_/g, ' ');
    }

    getScoreClass(score) {
        if (score >= 80) return 'high';
        if (score >= 50) return 'medium';
        return 'low';
    }

    // Safe DOM creation methods to prevent XSS
    createTextCell(className, text) {
        const cell = document.createElement('td');
        cell.className = className;
        cell.textContent = text || ''; // Use textContent instead of innerHTML
        return cell;
    }

    createPriorityCell(priorityClass, priority, priorityText) {
        const cell = document.createElement('td');
        cell.className = priorityClass;

        const span = document.createElement('span');
        span.className = `priority-badge priority-${this.sanitizeText(priority)}`;
        span.textContent = this.sanitizeText(priorityText);

        cell.appendChild(span);
        return cell;
    }

    createScoreCell(riskScore) {
        const cell = document.createElement('td');
        cell.className = 'risk-score';

        const span = document.createElement('span');
        const score = riskScore || 0;
        span.className = `score-badge score-${this.getScoreClass(score)}`;
        span.textContent = this.sanitizeText(score.toString());

        cell.appendChild(span);
        return cell;
    }

    createActionCell(alertId) {
        const cell = document.createElement('td');
        cell.className = 'alert-actions';

        // View button
        const viewBtn = document.createElement('button');
        viewBtn.className = 'btn btn-sm view-alert-btn';
        viewBtn.setAttribute('data-alert-id', this.sanitizeText(alertId));
        viewBtn.setAttribute('title', 'View Details');
        viewBtn.textContent = '👁️';

        // Dismiss button
        const dismissBtn = document.createElement('button');
        dismissBtn.className = 'btn btn-sm dismiss-alert-btn';
        dismissBtn.setAttribute('data-alert-id', this.sanitizeText(alertId));
        dismissBtn.setAttribute('title', 'Dismiss Alert');
        dismissBtn.textContent = '✖️';

        cell.appendChild(viewBtn);
        cell.appendChild(dismissBtn);

        return cell;
    }

    createActionCellWithExpand(alertId, isConsolidated) {
        const cell = document.createElement('td');
        cell.className = 'alert-actions';

        if (!alertId) {
            console.error('TTR UI: No alert ID provided for action cell');
            return cell;
        }

        // View button
        const viewBtn = document.createElement('button');
        viewBtn.className = 'btn btn-sm view-alert-btn';
        viewBtn.setAttribute('data-alert-id', alertId);
        viewBtn.setAttribute('title', 'View Details');
        viewBtn.textContent = '👁️';

        // Dismiss button
        const dismissBtn = document.createElement('button');
        dismissBtn.className = 'btn btn-sm dismiss-alert-btn';
        dismissBtn.setAttribute('data-alert-id', alertId);
        dismissBtn.setAttribute('title', 'Dismiss Alert');
        dismissBtn.textContent = '✖️';

        // Add expand button for consolidated alerts
        if (isConsolidated) {
            const expandBtn = document.createElement('button');
            expandBtn.className = 'btn btn-sm expand-alert-btn';
            expandBtn.setAttribute('data-alert-id', alertId);
            expandBtn.setAttribute('title', 'Expand Consolidated Alert');
            expandBtn.textContent = '🔍';
            cell.appendChild(expandBtn);
        }

        cell.appendChild(viewBtn);
        cell.appendChild(dismissBtn);

        return cell;
    }

    // Enhanced text sanitization
    sanitizeText(text) {
        if (text === null || text === undefined) {
            return '';
        }

        const str = String(text);

        // Remove HTML tags and encode special characters
        return str
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;')
            .replace(/\\/g, '&#x5C;')
            .trim();
    }
}

// Export for use in other modules
window.TTRUI = TTRUI;







