/**
 * High-Value Transaction Export Module
 * Handles export functionality for high-value transaction analytics
 */

class HighValueExport {
    constructor() {
        this.exportFormats = ['csv', 'excel'];
        this.HIGH_VALUE_THRESHOLD = 1000000000; // 1B MMK
        this.HIGH_VALUE_THRESHOLD_USD = 10000; // 10K USD
    }

    // Export high-value transactions to Excel format with streaming support
    async exportToExcel(filename = null) {
        try {
            // Check if XLSX library is available
            if (typeof XLSX === 'undefined') {
                throw new Error('XLSX library not available. Cannot export to Excel format.');
            }

            // Get high-value transaction data from DataProcessor
            const highValueData = this.getHighValueTransactionData();

            if (!highValueData || highValueData.transactions.length === 0) {
                throw new Error('No high-value transactions to export');
            }

            // Use streaming approach for large datasets
            if (highValueData.transactions.length > 10000) {
                return await this.exportToExcelStreaming(highValueData, filename);
            }

            // Create Excel workbook
            const workbook = this.createExcelWorkbook(highValueData);
            const finalFilename = filename || `High_Value_Transactions_${this.getTimestamp()}.xlsx`;

            // Write and download the file
            XLSX.writeFile(workbook, finalFilename);

            return {
                success: true,
                filename: finalFilename,
                recordCount: highValueData.transactions.length,
                format: 'Excel',
                summary: highValueData.summary
            };

        } catch (error) {
            console.error('Error exporting high-value transactions to Excel:', error);
            throw error;
        }
    }

    // Export large datasets to Excel using streaming approach
    async exportToExcelStreaming(highValueData, filename = null) {
        try {
            console.log(`Using streaming Excel export for ${highValueData.transactions.length} records`);

            const finalFilename = filename || `High_Value_Transactions_${this.getTimestamp()}.xlsx`;
            const workbook = XLSX.utils.book_new();

            // Process transactions in chunks to avoid memory issues
            const chunkSize = 5000;
            const totalTransactions = highValueData.transactions.length;
            let processedCount = 0;

            // Create main worksheet with headers
            const headers = [
                'Transaction Date', 'Amount', 'Currency', 'Report Type', 'Account Role',
                'Customer Name', 'Conductor Name', 'Counterparty Name',
                'Serial Number', 'File Name'
            ];

            // Initialize worksheet data with headers
            const worksheetData = [headers];

            // Process transactions in chunks
            for (let i = 0; i < totalTransactions; i += chunkSize) {
                const chunk = highValueData.transactions.slice(i, i + chunkSize);

                // Convert chunk to worksheet rows
                const chunkRows = chunk.map(transaction => [
                    transaction.TRANSACTION_DATE || '',
                    parseFloat(transaction.TRANSACTION_AMOUNT) || 0,
                    transaction.TRANSACTION_CURRENCY || '',
                    transaction.REPORTTYPE || '',
                    transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || '',
                    transaction.CUSTOMER_NAME || '',
                    transaction.PARTICIPANT_NAME_CONDUCTOR || '',
                    transaction.PARTICIPANT_NAME_COUNTERPARTY || '',
                    transaction.SERIAL_NO || '',
                    transaction.fileName || ''
                ]);

                // Add chunk rows to worksheet data
                worksheetData.push(...chunkRows);
                processedCount += chunk.length;

                // Log progress
                console.log(`Processed ${processedCount}/${totalTransactions} transactions for Excel export`);

                // Yield control to prevent blocking
                if (i + chunkSize < totalTransactions) {
                    await new Promise(resolve => setTimeout(resolve, 0));
                }

                // Check memory pressure and trigger GC if needed
                if (this.checkMemoryPressure()) {
                    this.triggerGarbageCollection();
                }
            }

            // Create worksheet from data
            const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

            // Set column widths for better readability
            worksheet['!cols'] = [
                { width: 15 }, // Transaction Date
                { width: 15 }, // Amount
                { width: 8 },  // Currency
                { width: 10 }, // Report Type
                { width: 12 }, // Account Role
                { width: 25 }, // Customer Name
                { width: 25 }, // Conductor Name
                { width: 25 }, // Counterparty Name
                { width: 15 }, // Serial Number
                { width: 20 }  // File Name
            ];

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, 'High Value Transactions');

            // Create summary worksheet
            this.addSummaryWorksheet(workbook, highValueData.summary);

            // Write file
            XLSX.writeFile(workbook, finalFilename);

            // Clear large data structures
            worksheetData.length = 0;

            console.log(`Streaming Excel export completed: ${finalFilename}`);

            return {
                success: true,
                filename: finalFilename,
                recordCount: totalTransactions,
                format: 'Excel (Streaming)',
                summary: highValueData.summary
            };

        } catch (error) {
            console.error('Error in streaming Excel export:', error);
            throw error;
        }
    }

    // Export high-value transactions to CSV format with streaming support
    async exportToCSV(filename = null) {
        try {
            // Get high-value transaction data
            const highValueData = this.getHighValueTransactionData();

            if (!highValueData || highValueData.transactions.length === 0) {
                throw new Error('No high-value transactions to export');
            }

            // Use streaming approach for large datasets
            if (highValueData.transactions.length > 20000) {
                return await this.exportToCSVStreaming(highValueData, filename);
            }

            // Convert to CSV
            const csvData = this.convertToCSV(highValueData.transactions);
            const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });

            const finalFilename = filename || `High_Value_Transactions_${this.getTimestamp()}.csv`;
            this.downloadBlob(blob, finalFilename);

            return {
                success: true,
                filename: finalFilename,
                recordCount: highValueData.transactions.length,
                format: 'CSV',
                summary: highValueData.summary
            };

        } catch (error) {
            console.error('Error exporting high-value transactions to CSV:', error);
            throw error;
        }
    }

    // Export large datasets to CSV using streaming approach
    async exportToCSVStreaming(highValueData, filename = null) {
        return new Promise((resolve, reject) => {
            try {
                console.log(`Using streaming CSV export for ${highValueData.transactions.length} records`);

                const finalFilename = filename || `High_Value_Transactions_${this.getTimestamp()}.csv`;

                // Check if Web Workers are supported
                if (!window.Worker) {
                    // Fallback to main thread processing
                    return this.exportToCSVMainThread(highValueData, finalFilename)
                        .then(resolve)
                        .catch(reject);
                }

                // Create worker for background processing
                const worker = new Worker('streamingExportWorker.js');

                worker.onmessage = (e) => {
                    const { action, result, progress, error } = e.data;

                    if (action === 'exportComplete') {
                        // Create and download the file
                        const blob = new Blob([result.csvContent], { type: 'text/csv;charset=utf-8;' });
                        this.downloadBlob(blob, finalFilename);

                        worker.terminate();

                        resolve({
                            success: true,
                            filename: finalFilename,
                            recordCount: result.recordCount,
                            format: 'CSV (Streaming)',
                            summary: highValueData.summary
                        });
                    } else if (action === 'progress') {
                        console.log(`CSV Export Progress: ${progress.percentage}% (${progress.processed}/${progress.total})`);
                    } else if (action === 'error') {
                        worker.terminate();
                        reject(new Error(`Worker error: ${error}`));
                    }
                };

                worker.onerror = (error) => {
                    worker.terminate();
                    reject(new Error(`Worker failed: ${error.message}`));
                };

                // Start the export
                worker.postMessage({
                    action: 'exportCSVStreaming',
                    data: {
                        transactions: highValueData.transactions,
                        options: {
                            chunkSize: 5000
                        }
                    }
                });

            } catch (error) {
                console.error('Error in streaming CSV export:', error);
                reject(error);
            }
        });
    }

    // Fallback CSV export for main thread
    async exportToCSVMainThread(highValueData, filename) {
        const chunkSize = 5000;
        const totalTransactions = highValueData.transactions.length;
        let csvContent = '';
        let processedCount = 0;

        // Add headers
        const headers = [
            'Transaction Date', 'Transaction Amount', 'Transaction Currency',
            'Report Type', 'Customer Name', 'Counterparty', 'File Name', 'File ID'
        ];
        csvContent += headers.map(header => this.escapeCSVField(header)).join(',') + '\n';

        // Process in chunks
        for (let i = 0; i < totalTransactions; i += chunkSize) {
            const chunk = highValueData.transactions.slice(i, i + chunkSize);

            const chunkCSV = chunk.map(transaction => [
                transaction.TRANSACTION_DATE || '',
                parseFloat(transaction.TRANSACTION_AMOUNT) || 0,
                transaction.TRANSACTION_CURRENCY || '',
                transaction.REPORTTYPE || transaction.REPORT_TYPE || '',
                transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || '',
                transaction.PARTICIPANT_NAME_COUNTERPARTY || transaction.COUNTERPARTY_NAME || '',
                transaction.fileName || '',
                transaction.fileId || ''
            ].map(field => this.escapeCSVField(field)).join(',')).join('\n');

            csvContent += chunkCSV + '\n';
            processedCount += chunk.length;

            console.log(`Main thread CSV export progress: ${Math.round((processedCount / totalTransactions) * 100)}%`);

            // Yield control
            if (i + chunkSize < totalTransactions) {
                await new Promise(resolve => setTimeout(resolve, 0));
            }
        }

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        this.downloadBlob(blob, filename);

        return {
            success: true,
            filename: filename,
            recordCount: processedCount,
            format: 'CSV (Main Thread)',
            summary: highValueData.summary
        };
    }

    // Get high-value transaction data from the system
    getHighValueTransactionData() {
        if (!window.dataProcessor) {
            throw new Error('DataProcessor not available');
        }

        const dataProcessor = window.dataProcessor;
        const transactions = [];
        const summary = {
            totalCount: 0,
            totalAmount: 0,
            filesProcessed: 0,
            dateRange: { start: null, end: null }
        };

        // Debug logging
        console.log('Debug: Checking high-value transaction data sources...');
        console.log('fileHighValueCounts size:', dataProcessor.fileHighValueCounts ? dataProcessor.fileHighValueCounts.size : 'undefined');
        console.log('fileHighValueTransactions size:', dataProcessor.fileHighValueTransactions ? dataProcessor.fileHighValueTransactions.size : 'undefined');
        console.log('summaryMetrics.highValueTransactionCount:', dataProcessor.summaryMetrics ? dataProcessor.summaryMetrics.highValueTransactionCount : 'undefined');

        // Log detailed contents for debugging
        if (dataProcessor.fileHighValueCounts && dataProcessor.fileHighValueCounts.size > 0) {
            console.log('Debug: fileHighValueCounts contents:');
            for (const [fileId, fileData] of dataProcessor.fileHighValueCounts) {
                console.log(`  File ${fileId}:`, fileData);
            }
        }

        if (dataProcessor.fileHighValueTransactions && dataProcessor.fileHighValueTransactions.size > 0) {
            console.log('Debug: fileHighValueTransactions contents:');
            for (const [fileId, transactions] of dataProcessor.fileHighValueTransactions) {
                console.log(`  File ${fileId}: ${transactions.length} transactions`);
                if (transactions.length > 0) {
                    console.log(`    Sample transaction:`, transactions[0]);
                }
            }
        }

        // Get high-value MMK transactions from the correct data structure
        // The actual transaction data is stored in fileHighValueTransactions, not fileHighValueCounts
        if (dataProcessor.fileHighValueTransactions && dataProcessor.fileHighValueTransactions.size > 0) {
            console.log('Debug: Found fileHighValueTransactions data (MMK)');

            for (const [fileId, fileTransactions] of dataProcessor.fileHighValueTransactions) {
                // Get file metadata from fileHighValueCounts
                const fileMetadata = dataProcessor.fileHighValueCounts.get(fileId);
                const fileName = fileMetadata ? fileMetadata.fileName : 'Unknown';

                console.log(`Debug: Processing MMK file ${fileName} (${fileId}) with ${fileTransactions.length} transactions`);

                if (fileTransactions && fileTransactions.length > 0) {
                    fileTransactions.forEach(transaction => {
                        // Ensure the transaction meets high-value criteria
                        const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                        if (transaction.TRANSACTION_CURRENCY === 'MMK' && amount >= this.HIGH_VALUE_THRESHOLD) {
                            transactions.push({
                                ...transaction,
                                fileName: fileName,
                                fileId: fileId
                            });

                            summary.totalAmount += amount;

                            // Update date range
                            const transactionDate = transaction.TRANSACTION_DATE;
                            if (transactionDate) {
                                if (!summary.dateRange.start || transactionDate < summary.dateRange.start) {
                                    summary.dateRange.start = transactionDate;
                                }
                                if (!summary.dateRange.end || transactionDate > summary.dateRange.end) {
                                    summary.dateRange.end = transactionDate;
                                }
                            }
                        }
                    });
                }
            }
        }

        // Get high-value USD transactions from the USD data structure
        if (dataProcessor.fileHighValueTransactionsUSD && dataProcessor.fileHighValueTransactionsUSD.size > 0) {
            console.log('Debug: Found fileHighValueTransactionsUSD data');

            for (const [fileId, fileTransactions] of dataProcessor.fileHighValueTransactionsUSD) {
                // Get file metadata from fileHighValueCountsUSD
                const fileMetadata = dataProcessor.fileHighValueCountsUSD.get(fileId);
                const fileName = fileMetadata ? fileMetadata.fileName : 'Unknown';

                console.log(`Debug: Processing USD file ${fileName} (${fileId}) with ${fileTransactions.length} transactions`);

                if (fileTransactions && fileTransactions.length > 0) {
                    fileTransactions.forEach(transaction => {
                        // Ensure the transaction meets high-value criteria
                        const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                        if (transaction.TRANSACTION_CURRENCY === 'USD' && amount >= this.HIGH_VALUE_THRESHOLD_USD) {
                            transactions.push({
                                ...transaction,
                                fileName: fileName,
                                fileId: fileId
                            });

                            summary.totalAmount += amount;

                            // Update date range
                            const transactionDate = transaction.TRANSACTION_DATE;
                            if (transactionDate) {
                                if (!summary.dateRange.start || transactionDate < summary.dateRange.start) {
                                    summary.dateRange.start = transactionDate;
                                }
                                if (!summary.dateRange.end || transactionDate > summary.dateRange.end) {
                                    summary.dateRange.end = transactionDate;
                                }
                            }
                        }
                    });
                }
            }
        }

        // Set files processed count
        const mmkFiles = dataProcessor.fileHighValueTransactions ? dataProcessor.fileHighValueTransactions.size : 0;
        const usdFiles = dataProcessor.fileHighValueTransactionsUSD ? dataProcessor.fileHighValueTransactionsUSD.size : 0;
        summary.filesProcessed = Math.max(mmkFiles, usdFiles); // Use the higher count to avoid double counting

        // Fallback check if no transaction data found
        if (transactions.length === 0) {
            console.log('Debug: No fileHighValueTransactions data found');

            // Fallback: Check if there are high-value transactions in the summary metrics
            if (dataProcessor.summaryMetrics &&
                (dataProcessor.summaryMetrics.highValueTransactionCount > 0 ||
                 dataProcessor.summaryMetrics.highValueTransactionCountUSD > 0)) {
                console.log('Debug: Found high-value transactions in summary metrics but no detailed transaction data');
                console.log('This suggests the transactions were counted but not stored for export');
            }
        }

        summary.totalCount = transactions.length;

        console.log(`Debug: Final summary - ${summary.totalCount} transactions, ${summary.filesProcessed} files processed`);

        // Sort transactions by amount (descending) and then by date
        transactions.sort((a, b) => {
            const amountA = parseFloat(a.TRANSACTION_AMOUNT) || 0;
            const amountB = parseFloat(b.TRANSACTION_AMOUNT) || 0;
            if (amountB !== amountA) {
                return amountB - amountA; // Descending by amount
            }
            return (a.TRANSACTION_DATE || '').localeCompare(b.TRANSACTION_DATE || ''); // Ascending by date
        });

        return {
            transactions,
            summary
        };
    }

    // Create Excel workbook with multiple sheets
    createExcelWorkbook(highValueData) {
        const workbook = XLSX.utils.book_new();

        // Sheet 1: Summary
        const summaryData = this.createSummaryData(highValueData.summary);
        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

        // Sheet 2: High-Value Transactions
        const transactionData = this.createTransactionData(highValueData.transactions);
        const transactionSheet = XLSX.utils.aoa_to_sheet(transactionData);
        XLSX.utils.book_append_sheet(workbook, transactionSheet, 'High-Value Transactions');

        // Sheet 3: File Breakdown
        const fileBreakdownData = this.createFileBreakdownData();
        const fileBreakdownSheet = XLSX.utils.aoa_to_sheet(fileBreakdownData);
        XLSX.utils.book_append_sheet(workbook, fileBreakdownSheet, 'File Breakdown');

        return workbook;
    }

    // Create summary data for Excel export
    createSummaryData(summary) {
        const timestamp = new Date().toLocaleString();

        return [
            ['High-Value Transaction Analytics Report'],
            ['Generated on:', timestamp],
            [''],
            ['Summary Statistics'],
            ['Total High-Value Transactions:', summary.totalCount],
            ['Total Amount (MMK):', this.formatCurrency(summary.totalAmount, 'MMK')],
            ['Files Processed:', summary.filesProcessed],
            ['Date Range:', summary.dateRange.start && summary.dateRange.end ?
                `${summary.dateRange.start} to ${summary.dateRange.end}` : 'N/A'],
            ['High-Value Threshold:', this.formatCurrency(this.HIGH_VALUE_THRESHOLD, 'MMK')],
            [''],
            ['Report Criteria'],
            ['Currency:', 'MMK only'],
            ['Minimum Amount:', this.formatCurrency(this.HIGH_VALUE_THRESHOLD, 'MMK')],
            ['Transaction Types:', 'All (HOC and IBD)'],
            [''],
            ['Note: This report includes only transactions in MMK currency that meet or exceed the high-value threshold.']
        ];
    }

    // Create transaction data for Excel export
    createTransactionData(transactions) {
        const headers = [
            'Transaction Date',
            'CONDUCTOR',
            'CONDUCTORID',
            'Counterparty',
            'COUNTERPARTYID',
            'Transaction Amount',
            'ROLE',
            'Transaction Currency',
            'Report Type',
            'File Name',
            'File ID'
        ];

        const rows = transactions.map(transaction => [
            transaction.TRANSACTION_DATE || '',
            transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || '',
            transaction.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A',
            transaction.PARTICIPANT_NAME_COUNTERPARTY || transaction.COUNTERPARTY_NAME || '',
            transaction.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A',
            parseFloat(transaction.TRANSACTION_AMOUNT) || 0,
            transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || 'N/A',
            transaction.TRANSACTION_CURRENCY || '',
            transaction.REPORTTYPE || transaction.REPORT_TYPE || '',
            transaction.fileName || '',
            transaction.fileId || ''
        ]);

        return [headers, ...rows];
    }

    // Create file breakdown data for Excel export
    createFileBreakdownData() {
        const headers = [
            'File Name',
            'File ID',
            'High-Value Transaction Count (MMK)',
            'High-Value Transaction Count (USD)',
            'Total Amount (MMK)',
            'Total Amount (USD)',
            'Date Range'
        ];

        const rows = [];
        const processedFiles = new Set();

        // Process MMK files
        if (window.dataProcessor && window.dataProcessor.fileHighValueCounts) {
            for (const [fileId, fileData] of window.dataProcessor.fileHighValueCounts) {
                let totalAmountMMK = 0;
                let dateRange = { start: null, end: null };

                // Get actual transaction data from fileHighValueTransactions
                const fileTransactions = window.dataProcessor.fileHighValueTransactions.get(fileId);

                if (fileTransactions && fileTransactions.length > 0) {
                    fileTransactions.forEach(transaction => {
                        const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                        if (transaction.TRANSACTION_CURRENCY === 'MMK' && amount >= this.HIGH_VALUE_THRESHOLD) {
                            totalAmountMMK += amount;

                            const transactionDate = transaction.TRANSACTION_DATE;
                            if (transactionDate) {
                                if (!dateRange.start || transactionDate < dateRange.start) {
                                    dateRange.start = transactionDate;
                                }
                                if (!dateRange.end || transactionDate > dateRange.end) {
                                    dateRange.end = transactionDate;
                                }
                            }
                        }
                    });
                }

                // Check for USD data for the same file
                let totalAmountUSD = 0;
                let usdCount = 0;
                const fileDataUSD = window.dataProcessor.fileHighValueCountsUSD ?
                    window.dataProcessor.fileHighValueCountsUSD.get(fileId) : null;

                if (fileDataUSD) {
                    usdCount = fileDataUSD.totalCount || 0;
                    const fileTransactionsUSD = window.dataProcessor.fileHighValueTransactionsUSD.get(fileId);

                    if (fileTransactionsUSD && fileTransactionsUSD.length > 0) {
                        fileTransactionsUSD.forEach(transaction => {
                            const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                            if (transaction.TRANSACTION_CURRENCY === 'USD' && amount >= this.HIGH_VALUE_THRESHOLD_USD) {
                                totalAmountUSD += amount;

                                const transactionDate = transaction.TRANSACTION_DATE;
                                if (transactionDate) {
                                    if (!dateRange.start || transactionDate < dateRange.start) {
                                        dateRange.start = transactionDate;
                                    }
                                    if (!dateRange.end || transactionDate > dateRange.end) {
                                        dateRange.end = transactionDate;
                                    }
                                }
                            }
                        });
                    }
                }

                const dateRangeStr = dateRange.start && dateRange.end ?
                    `${dateRange.start} to ${dateRange.end}` : 'N/A';

                rows.push([
                    fileData.fileName || 'Unknown',
                    fileId,
                    fileData.totalCount || 0,
                    usdCount,
                    totalAmountMMK,
                    totalAmountUSD,
                    dateRangeStr
                ]);

                processedFiles.add(fileId);
            }
        }

        // Process USD-only files (files that have USD transactions but no MMK high-value transactions)
        if (window.dataProcessor && window.dataProcessor.fileHighValueCountsUSD) {
            for (const [fileId, fileData] of window.dataProcessor.fileHighValueCountsUSD) {
                if (!processedFiles.has(fileId)) {
                    let totalAmountUSD = 0;
                    let dateRange = { start: null, end: null };

                    // Get actual transaction data from fileHighValueTransactionsUSD
                    const fileTransactions = window.dataProcessor.fileHighValueTransactionsUSD.get(fileId);

                    if (fileTransactions && fileTransactions.length > 0) {
                        fileTransactions.forEach(transaction => {
                            const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
                            if (transaction.TRANSACTION_CURRENCY === 'USD' && amount >= this.HIGH_VALUE_THRESHOLD_USD) {
                                totalAmountUSD += amount;

                                const transactionDate = transaction.TRANSACTION_DATE;
                                if (transactionDate) {
                                    if (!dateRange.start || transactionDate < dateRange.start) {
                                        dateRange.start = transactionDate;
                                    }
                                    if (!dateRange.end || transactionDate > dateRange.end) {
                                        dateRange.end = transactionDate;
                                    }
                                }
                            }
                        });
                    }

                    const dateRangeStr = dateRange.start && dateRange.end ?
                        `${dateRange.start} to ${dateRange.end}` : 'N/A';

                    rows.push([
                        fileData.fileName || 'Unknown',
                        fileId,
                        0, // No MMK high-value transactions
                        fileData.totalCount || 0,
                        0, // No MMK amount
                        totalAmountUSD,
                        dateRangeStr
                    ]);
                }
            }
        }

        return [headers, ...rows];
    }

    // Convert transactions to CSV format
    convertToCSV(transactions) {
        const headers = [
            'Transaction Date',
            'CONDUCTOR',
            'CONDUCTORID',
            'Counterparty',
            'COUNTERPARTYID',
            'Transaction Amount',
            'ROLE',
            'Transaction Currency',
            'Report Type',
            'File Name',
            'File ID'
        ];

        const rows = transactions.map(transaction => [
            transaction.TRANSACTION_DATE || '',
            transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || '',
            transaction.PARTICIPANT_ID_NUMBER_CONDUCTOR || 'N/A',
            transaction.PARTICIPANT_NAME_COUNTERPARTY || transaction.COUNTERPARTY_NAME || '',
            transaction.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A',
            parseFloat(transaction.TRANSACTION_AMOUNT) || 0,
            transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || 'N/A',
            transaction.TRANSACTION_CURRENCY || '',
            transaction.REPORTTYPE || transaction.REPORT_TYPE || '',
            transaction.fileName || '',
            transaction.fileId || ''
        ]);

        // Combine headers and rows
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => this.escapeCSVField(field)).join(','))
            .join('\n');

        return csvContent;
    }

    // Escape CSV field
    escapeCSVField(field) {
        if (field === null || field === undefined) {
            return '';
        }

        const stringField = String(field);
        if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
            return `"${stringField.replace(/"/g, '""')}"`;
        }
        return stringField;
    }

    // Format currency
    formatCurrency(amount, currency = 'MMK') {
        const numAmount = parseFloat(amount) || 0;
        const supportedCurrencies = ['USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

        if (currency === 'MMK') {
            return `${numAmount.toLocaleString()} MMK`;
        } else if (supportedCurrencies.includes(currency)) {
            try {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: currency,
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(numAmount);
            } catch (e) {
                // Fallback if currency formatting fails
                return `${numAmount.toLocaleString()} ${currency}`;
            }
        } else {
            // Fallback for unsupported currencies
            return `${numAmount.toLocaleString()} ${currency}`;
        }
    }

    // Get timestamp for filename
    getTimestamp() {
        return new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    }

    // Add summary worksheet for streaming export
    addSummaryWorksheet(workbook, summary) {
        const summaryData = [
            ['High-Value Transaction Analytics Report'],
            ['Generated on:', new Date().toLocaleString()],
            [''],
            ['Summary Statistics'],
            ['Total High-Value Transactions:', summary.totalCount],
            ['Total Amount (MMK):', this.formatCurrency(summary.totalAmount, 'MMK')],
            ['Files Processed:', summary.filesProcessed],
            ['Date Range:', summary.dateRange.start && summary.dateRange.end ?
                `${summary.dateRange.start} to ${summary.dateRange.end}` : 'N/A'],
            ['High-Value Threshold:', this.formatCurrency(this.HIGH_VALUE_THRESHOLD, 'MMK')],
            [''],
            ['Export Method:', 'Streaming (Memory Optimized)'],
            [''],
            ['Report Criteria'],
            ['Currency:', 'MMK only'],
            ['Minimum Amount:', this.formatCurrency(this.HIGH_VALUE_THRESHOLD, 'MMK')],
            ['Transaction Types:', 'All (HOC and IBD)'],
            [''],
            ['Note: This report includes only transactions in MMK currency that meet or exceed the high-value threshold.'],
            ['Note: Streaming export was used to optimize memory usage for large datasets.']
        ];

        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    }

    // Check memory pressure
    checkMemoryPressure() {
        if (window.performance && window.performance.memory) {
            const memInfo = window.performance.memory;
            const usageRatio = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;
            return usageRatio > 0.8; // 80% threshold
        }
        return false;
    }

    // Trigger garbage collection
    triggerGarbageCollection() {
        // Create and destroy temporary objects to encourage GC
        const temp = new Array(1000).fill(null).map(() => ({
            data: new Array(1000).join('x')
        }));
        temp.length = 0;

        console.log('Triggered garbage collection due to memory pressure during export');
    }

    // Download blob as file
    downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }
}

// Export for use in other modules
window.HighValueExport = HighValueExport;
