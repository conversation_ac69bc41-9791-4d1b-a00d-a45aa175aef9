/**
 * TTR Summary Export Module
 * Handles Excel export functionality for TTR Summary Report data
 */

class TTRSummaryExport {
    constructor() {
        this.exportFormats = ['excel'];
        this.isExporting = false;
    }

    // Main export function for TTR Summary Report data
    async exportTTRSummaryToExcel(filename = null) {
        if (this.isExporting) {
            console.warn('TTR Summary export already in progress');
            return;
        }

        try {
            this.isExporting = true;
            this.showLoadingIndicator();

            // Check if XLSX library is available
            if (typeof XLSX === 'undefined') {
                throw new Error('XLSX library not available. Cannot export to Excel format.');
            }

            // Get TTR summary data
            const ttrData = this.getTTRSummaryData();

            if (!ttrData || ttrData.length === 0) {
                throw new Error('No TTR summary data available to export');
            }

            // Create Excel workbook
            const workbook = this.createTTRSummaryWorkbook(ttrData);
            
            // Generate filename with timestamp
            const finalFilename = filename || this.generateFilename();

            // Write and download the Excel file
            XLSX.writeFile(workbook, finalFilename);

            // Show success notification
            this.showSuccessNotification(finalFilename, ttrData.length);

            return {
                success: true,
                filename: finalFilename,
                recordCount: ttrData.length,
                format: 'Excel'
            };

        } catch (error) {
            console.error('TTR Summary export error:', error);
            this.showErrorNotification(error.message);
            throw error;
        } finally {
            this.isExporting = false;
            this.hideLoadingIndicator();
        }
    }

    // Get TTR summary data from DataProcessor
    getTTRSummaryData() {
        if (!window.dataProcessor) {
            console.warn('TTR Summary Export: DataProcessor not available');
            return [];
        }

        // Check if basic data requirements are met before calling calculateTTRSummary
        if (!window.fileHandler || !window.fileHandler.processedData || window.fileHandler.processedData.length === 0) {
            console.log('TTR Summary Export: No processed files available yet');
            return [];
        }

        console.log('TTR Summary Export: Getting TTR data...');

        try {
            // Get TTR data using the same method as the UI table
            const ttrData = window.dataProcessor.calculateTTRSummary();

            console.log('TTR Summary Export: Retrieved data:', {
                dataLength: ttrData ? ttrData.length : 0,
                sampleData: ttrData && ttrData.length > 0 ? ttrData.slice(0, 2) : []
            });

            // Validate the data structure
            if (Array.isArray(ttrData) && ttrData.length > 0) {
                // Check if the first item has the expected structure
                const firstItem = ttrData[0];
                const requiredFields = ['reportDate', 'hocSerial', 'hocCount', 'ibdSerial', 'ibdCount', 'wuSerial', 'wuCount', 'oneBillionPlusCount', 'grandTotal'];
                const hasRequiredFields = requiredFields.every(field => firstItem.hasOwnProperty(field));

                if (!hasRequiredFields) {
                    console.warn('TTR data structure validation failed, missing required fields');
                }
            }

            return ttrData || [];
        } catch (error) {
            console.error('Error getting TTR summary data:', error);
            return [];
        }
    }

    // Create Excel workbook with TTR Summary Report
    createTTRSummaryWorkbook(ttrData) {
        const workbook = XLSX.utils.book_new();

        // Add TTR Summary Report worksheet
        const summarySheet = this.createTTRSummaryWorksheet(ttrData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'TTR Summary Report');

        return workbook;
    }

    // Create TTR Summary Report worksheet
    createTTRSummaryWorksheet(ttrData) {
        console.log('Creating TTR Summary worksheet with data:', ttrData.length, 'records');

        // Create headers matching the UI table
        const headers = [
            'Report Date',
            'HOC Serial',
            'HOC Count',
            'IBD Serial', 
            'IBD Count',
            'WU Serial',
            'WU Count',
            '1B+ Count',
            'Grand Total'
        ];

        // Convert TTR data to rows
        const rows = ttrData.map(item => [
            item.reportDate || '',
            item.hocSerial || '-',
            item.hocCount || 0,
            item.ibdSerial || '-',
            item.ibdCount || 0,
            item.wuSerial || '-',
            item.wuCount || 0,
            item.oneBillionPlusCount || 0,
            item.grandTotal || 0
        ]);

        // Add summary information at the top
        const summaryData = [
            ['TTR SUMMARY REPORT'],
            ['Generated on:', new Date().toLocaleString()],
            ['Total Files Processed:', ttrData.length],
            [''],
            ['This report contains the same data displayed in the TTR Summary Report table.'],
            [''],
            ...([headers, ...rows])
        ];

        return XLSX.utils.aoa_to_sheet(summaryData);
    }

    // Generate filename with timestamp
    generateFilename() {
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0];
        const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
        return `TTR_Summary_Report_${dateStr}_${timeStr}.xlsx`;
    }

    // Check if TTR data is available
    hasTTRData() {
        try {
            // First check if the basic requirements are met
            if (!window.dataProcessor || !window.fileHandler) {
                return false;
            }

            // Check if there are any processed files
            const hasProcessedFiles = window.fileHandler.processedData &&
                                    window.fileHandler.processedData.length > 0;

            const hasFileMetrics = window.fileHandler.fileMetrics &&
                                 window.fileHandler.fileMetrics.size > 0;

            // Only try to get TTR data if basic requirements are met
            if (!hasProcessedFiles || !hasFileMetrics) {
                console.log('TTR Summary Export: No processed files or metrics available yet');
                return false;
            }

            // Now safely try to get TTR data
            const ttrData = this.getTTRSummaryData();
            return ttrData && ttrData.length > 0;
        } catch (error) {
            console.error('Error checking TTR data availability:', error);
            return false;
        }
    }

    // Show loading indicator
    showLoadingIndicator() {
        const exportBtn = document.getElementById('exportTTRSummaryBtn');
        if (exportBtn) {
            exportBtn.disabled = true;
            exportBtn.innerHTML = '<span class="loading-spinner"></span>Exporting...';
        }
    }

    // Hide loading indicator
    hideLoadingIndicator() {
        const exportBtn = document.getElementById('exportTTRSummaryBtn');
        if (exportBtn) {
            exportBtn.disabled = false;
            exportBtn.innerHTML = '<span class="export-icon"></span>Export to Excel';
        }
    }

    // Show success notification
    showSuccessNotification(filename, recordCount) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(
                `Successfully exported ${recordCount} TTR summary records to ${filename}`,
                'success',
                5000
            );
        }
    }

    // Show error notification
    showErrorNotification(message) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(
                `TTR Summary export failed: ${message}`,
                'error',
                5000
            );
        }
    }
}

// Create global instance
if (!window.ttrSummaryExport) {
    window.ttrSummaryExport = new TTRSummaryExport();
    console.log('TTRSummaryExport module initialized');
}
