<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- Deny access to all files by default -->
        <security>
            <authorization>
                <remove users="*" roles="" verbs="" />
                <add accessType="Deny" users="*" />
            </authorization>
            
            <!-- HTTP Basic Authentication for Worker files -->
            <authentication>
                <basicAuthentication enabled="true" realm="Restricted Worker Files" />
            </authentication>
        </security>
        
        <!-- Security headers -->
        <httpProtocol>
            <customHeaders>
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-Frame-Options" value="DENY" />
                <add name="X-XSS-Protection" value="1; mode=block" />
                <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
                <add name="Cache-Control" value="no-cache, no-store, must-revalidate" />
                <add name="Pragma" value="no-cache" />
                <add name="Expires" value="0" />
            </customHeaders>
        </httpProtocol>
        
        <!-- URL Rewrite rules -->
        <rewrite>
            <rules>
                <!-- Block direct access attempts -->
                <rule name="Block Direct Worker Access" stopProcessing="true">
                    <match url="\.js$" />
                    <conditions>
                        <add input="{HTTP_REFERER}" pattern="^https?://[^/]+/(application|index)\.html" negate="true" ignoreCase="true" />
                    </conditions>
                    <action type="CustomResponse" statusCode="403" statusReason="Forbidden" statusDescription="Direct access not allowed" />
                </rule>
                
                <!-- Block suspicious user agents -->
                <rule name="Block Suspicious Agents" stopProcessing="true">
                    <match url=".*" />
                    <conditions>
                        <add input="{HTTP_USER_AGENT}" pattern="(wget|curl|libwww)" ignoreCase="true" />
                    </conditions>
                    <action type="CustomResponse" statusCode="403" statusReason="Forbidden" statusDescription="Access denied" />
                </rule>
            </rules>
        </rewrite>
        
        <!-- Disable directory browsing -->
        <directoryBrowse enabled="false" />
        
        <!-- Remove server header -->
        <security>
            <requestFiltering removeServerHeader="true" />
        </security>
    </system.webServer>
</configuration>
