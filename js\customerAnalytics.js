/**
 * Customer Analytics Module
 * Handles high-value customer analytics and alerting system
 */

class CustomerAnalytics {
    constructor() {
        this.customerData = new Map(); // Map of customer -> date -> {mmk: amount, usd: amount, count: number}
        this.alertCustomers = new Set(); // Set of customers with alerts
        this.MMK_THRESHOLD = 1000000000; // 1B MMK
        this.USD_THRESHOLD = 10000; // 10K USD
        this.isInitialized = false;
        
        // UI elements
        this.elements = {};

        // Store all transaction data for detailed views
        this.allTransactions = [];
        this.currentModalTransactions = [];

        // Source file tracking
        this.sourceFiles = new Set(); // Track available source files
        this.transactionSourceMap = new Map(); // Map transaction IDs to source files
        
        // Bind methods
        this.processTransactionData = this.processTransactionData.bind(this);
        this.updateUI = this.updateUI.bind(this);
        this.generateAlerts = this.generateAlerts.bind(this);
    }

    // Initialize the customer analytics module
    initialize() {
        if (this.isInitialized) {
            console.log('Customer Analytics already initialized, skipping...');
            return;
        }

        console.log('Initializing Customer Analytics module...');

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.performInitialization();
            });
        } else {
            this.performInitialization();
        }
    }

    // Perform the actual initialization
    performInitialization() {
        this.initializeElements();
        this.attachEventListeners();
        this.isInitialized = true;

        console.log('Customer Analytics module initialized successfully');

        // Log element availability for debugging
        this.logElementAvailability();
    }

    // Initialize DOM elements
    initializeElements() {
        console.log('Initializing Customer Analytics DOM elements...');

        this.elements = {
            // Metrics
            activeCustomerAlerts: document.getElementById('activeCustomerAlerts'),
            mmkAlertCustomers: document.getElementById('mmkAlertCustomers'),
            usdAlertCustomers: document.getElementById('usdAlertCustomers'),
            totalMonitoredCustomers: document.getElementById('totalMonitoredCustomers'),

            // Alert banner
            customerAlertBanner: document.getElementById('customerAlertBanner'),
            customerAlertMessage: document.getElementById('customerAlertMessage'),
            closeCustomerAlert: document.getElementById('closeCustomerAlert'),

            // Table and controls
            customerAnalyticsTable: document.getElementById('customerAnalyticsTable'),
            customerAnalyticsTableBody: document.getElementById('customerAnalyticsTableBody'),
            customerAlertFilter: document.getElementById('customerAlertFilter'),
            customerSortBy: document.getElementById('customerSortBy'),
            customerDateFilter: document.getElementById('customerDateFilter'),
            customerSourceFileFilter: document.getElementById('customerSourceFileFilter'),
            refreshCustomerAnalytics: document.getElementById('refreshCustomerAnalytics'),

            // Summary
            customerAnalyticsSummary: document.getElementById('customerAnalyticsSummary'),
            summaryTotalCustomers: document.getElementById('summaryTotalCustomers'),
            summaryAlertCustomers: document.getElementById('summaryAlertCustomers'),
            summaryTotalMMK: document.getElementById('summaryTotalMMK'),
            summaryTotalUSD: document.getElementById('summaryTotalUSD'),

            // Export buttons
            exportCustomerAnalyticsBtn: document.getElementById('exportCustomerAnalyticsBtn'),
            testCustomerAlertsBtn: document.getElementById('testCustomerAlertsBtn'),

            // Customer transaction details modal
            customerTransactionModal: document.getElementById('customerTransactionDetailsModal'),
            modalBackdrop: document.getElementById('modalBackdrop'),
            modalCloseBtn: document.getElementById('modalCloseBtn'),
            modalCancelBtn: document.getElementById('modalCancelBtn'),
            modalCustomerName: document.getElementById('modalCustomerName'),
            modalCustomerDate: document.getElementById('modalCustomerDate'),
            modalTotalCount: document.getElementById('modalTotalCount'),
            modalTotalMMK: document.getElementById('modalTotalMMK'),
            modalTotalUSD: document.getElementById('modalTotalUSD'),
            modalAlertStatus: document.getElementById('modalAlertStatus'),
            modalTransactionTable: document.getElementById('modalTransactionTable'),
            modalTransactionTableBody: document.getElementById('modalTransactionTableBody'),
            modalExportBtn: document.getElementById('modalExportBtn')
        };

        console.log('Customer Analytics DOM elements initialized');
    }

    // Log element availability for debugging
    logElementAvailability() {
        const criticalElements = [
            'customerAlertFilter', 'customerSortBy', 'customerDateFilter', 'customerSourceFileFilter',
            'customerAnalyticsTableBody', 'customerTransactionModal'
        ];

        console.log('=== Customer Analytics Element Availability ===');
        criticalElements.forEach(elementKey => {
            const element = this.elements[elementKey];
            const status = element ? '✓ Found' : '✗ Missing';
            console.log(`${elementKey}: ${status}`);

            if (!element) {
                console.warn(`Critical element missing: ${elementKey}`);
            }
        });
        console.log('===============================================');
    }

    // Attach event listeners
    attachEventListeners() {
        console.log('Attaching Customer Analytics event listeners...');

        // Listen for file processing events
        document.addEventListener('fileProcessed', (event) => {
            console.log('🔔 Customer Analytics: Received fileProcessed event', event.detail);
            this.handleFileProcessed(event.detail);
        });
        console.log('✓ File processed event listener attached');

        // Close alert banner
        if (this.elements.closeCustomerAlert) {
            this.elements.closeCustomerAlert.addEventListener('click', () => {
                this.hideAlertBanner();
            });
            console.log('✓ Close alert banner event listener attached');
        } else {
            console.warn('✗ Close alert banner element not found');
        }

        // Filter and sort controls with enhanced debugging
        if (this.elements.customerAlertFilter) {
            this.elements.customerAlertFilter.addEventListener('change', (e) => {
                console.log('Customer alert filter changed:', e.target.value);
                this.updateTable();
            });
            console.log('✓ Customer alert filter event listener attached');
        } else {
            console.warn('✗ Customer alert filter element not found');
        }

        if (this.elements.customerSortBy) {
            this.elements.customerSortBy.addEventListener('change', (e) => {
                console.log('Customer sort by changed:', e.target.value);
                this.updateTable();
            });
            console.log('✓ Customer sort by event listener attached');
        } else {
            console.warn('✗ Customer sort by element not found');
        }

        if (this.elements.customerDateFilter) {
            this.elements.customerDateFilter.addEventListener('change', (e) => {
                console.log('Customer date filter changed:', e.target.value);
                this.updateTable();
            });
            console.log('✓ Customer date filter event listener attached');
        } else {
            console.warn('✗ Customer date filter element not found');
        }

        if (this.elements.customerSourceFileFilter) {
            this.elements.customerSourceFileFilter.addEventListener('change', (e) => {
                const selectedFiles = Array.from(e.target.selectedOptions).map(option => option.value);
                console.log('Customer source file filter changed:', selectedFiles);
                this.updateTable();
            });
            console.log('✓ Customer source file filter event listener attached');
        } else {
            console.warn('✗ Customer source file filter element not found');
        }

        // Refresh button
        if (this.elements.refreshCustomerAnalytics) {
            this.elements.refreshCustomerAnalytics.addEventListener('click', () => {
                console.log('Refresh customer analytics clicked');
                this.refreshAnalytics();
            });
            console.log('✓ Refresh customer analytics event listener attached');
        } else {
            console.warn('✗ Refresh customer analytics element not found');
        }

        // Export button
        if (this.elements.exportCustomerAnalyticsBtn) {
            this.elements.exportCustomerAnalyticsBtn.addEventListener('click', () => {
                console.log('Export customer analytics clicked');
                this.exportCustomerAnalytics();
            });
            console.log('✓ Export customer analytics event listener attached');
        } else {
            console.warn('✗ Export customer analytics element not found');
        }

        // Test alerts button
        if (this.elements.testCustomerAlertsBtn) {
            this.elements.testCustomerAlertsBtn.addEventListener('click', () => {
                console.log('Test customer alerts clicked');
                this.testCustomerAlerts();
            });
            console.log('✓ Test customer alerts event listener attached');
        } else {
            console.warn('✗ Test customer alerts element not found');
        }

        // Modal event listeners
        if (this.elements.modalCloseBtn) {
            this.elements.modalCloseBtn.addEventListener('click', () => {
                console.log('Modal close button clicked');
                this.hideCustomerTransactionModal();
            });
            console.log('✓ Modal close button event listener attached');
        } else {
            console.warn('✗ Modal close button element not found');
        }

        if (this.elements.modalCancelBtn) {
            this.elements.modalCancelBtn.addEventListener('click', () => {
                console.log('Modal cancel button clicked');
                this.hideCustomerTransactionModal();
            });
            console.log('✓ Modal cancel button event listener attached');
        } else {
            console.warn('✗ Modal cancel button element not found');
        }

        if (this.elements.modalBackdrop) {
            this.elements.modalBackdrop.addEventListener('click', () => {
                console.log('Modal backdrop clicked');
                this.hideCustomerTransactionModal();
            });
            console.log('✓ Modal backdrop event listener attached');
        } else {
            console.warn('✗ Modal backdrop element not found');
        }

        // Modal export button
        if (this.elements.modalExportBtn) {
            this.elements.modalExportBtn.addEventListener('click', () => {
                console.log('Modal export button clicked');
                this.exportModalTransactions();
            });
            console.log('✓ Modal export button event listener attached');
        } else {
            console.warn('✗ Modal export button element not found');
        }

        // Keyboard event listeners for modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isModalOpen()) {
                console.log('Escape key pressed, closing modal');
                this.hideCustomerTransactionModal();
            }
        });
        console.log('✓ Keyboard event listeners attached');

        console.log('All Customer Analytics event listeners attached successfully');
    }

    // Handle file processed events
    handleFileProcessed(eventDetail) {
        if (!eventDetail || !eventDetail.transactions || !eventDetail.fileName) {
            console.warn('Invalid fileProcessed event detail:', eventDetail);
            return;
        }

        const { transactions, fileName, fileId } = eventDetail;
        console.log(`Processing file: ${fileName} with ${transactions.length} transactions`);

        // Add source file to tracking
        this.sourceFiles.add(fileName);

        // Add transactions to all transactions array with source file information
        transactions.forEach(transaction => {
            transaction.SOURCE_FILE = fileName;
            transaction.FILE_ID = fileId;
        });

        // Process transactions from this specific source
        this.processTransactionsFromSource(transactions, fileName);

        // Update the source file filter dropdown
        this.updateSourceFileFilter();

        console.log(`File ${fileName} processed successfully for customer analytics`);
    }

    // Process transaction data and group by customer and date
    processTransactionData(transactions, sourceFileName = null) {
        console.log('=== Processing Customer Analytics Transaction Data ===');

        if (!transactions || !Array.isArray(transactions)) {
            console.warn('Invalid transaction data provided to customer analytics:', transactions);
            return;
        }

        console.log(`Processing ${transactions.length} transactions for customer analytics`);
        if (sourceFileName) {
            console.log(`Source file: ${sourceFileName}`);
        }

        // Clear previous data
        this.customerData.clear();
        this.alertCustomers.clear();
        this.sourceFiles.clear();
        this.transactionSourceMap.clear();

        // Store all transactions for detailed views
        this.allTransactions = transactions || [];

        let processedCount = 0;
        let skippedCount = 0;
        let mmkTransactions = 0;
        let usdTransactions = 0;

        // Group transactions by customer and date
        transactions.forEach((transaction, index) => {
            const customerName = this.getCustomerName(transaction);
            const customerId = this.getCustomerId(transaction);
            const transactionDate = this.extractDate(transaction.TRANSACTION_DATE);
            const amount = this.parseAmount(transaction.TRANSACTION_AMOUNT);
            const currency = (transaction.TRANSACTION_CURRENCY || 'MMK').toUpperCase();

            // Track source file information
            const transactionSourceFile = sourceFileName || transaction.SOURCE_FILE || 'Unknown Source';
            this.sourceFiles.add(transactionSourceFile);

            // Create unique transaction ID for source mapping
            const transactionId = `${customerName}_${customerId}_${transactionDate}_${index}`;
            this.transactionSourceMap.set(transactionId, transactionSourceFile);

            // Add source file to transaction object for later use
            transaction.SOURCE_FILE = transactionSourceFile;

            // Debug first few transactions
            if (index < 3) {
                console.log(`Transaction ${index + 1} debug:`, {
                    customerName,
                    customerId,
                    transactionDate,
                    amount,
                    currency,
                    sourceFile: transactionSourceFile,
                    rawDate: transaction.TRANSACTION_DATE,
                    rawAmount: transaction.TRANSACTION_AMOUNT
                });
            }

            if (!customerName || !transactionDate || amount <= 0) {
                skippedCount++;
                if (index < 5) {
                    console.log(`Skipping transaction ${index + 1}:`, {
                        customerName: !!customerName,
                        transactionDate: !!transactionDate,
                        amount: amount
                    });
                }
                return; // Skip invalid transactions
            }

            processedCount++;

            // Create customer key
            const customerKey = `${customerName}|${customerId}`;

            // Initialize customer data if not exists
            if (!this.customerData.has(customerKey)) {
                this.customerData.set(customerKey, new Map());
            }

            const customerDates = this.customerData.get(customerKey);

            // Initialize date data if not exists
            if (!customerDates.has(transactionDate)) {
                customerDates.set(transactionDate, {
                    mmk: 0,
                    usd: 0,
                    count: 0,
                    customerName: customerName,
                    customerId: customerId,
                    sourceFiles: new Set() // Track source files for this customer/date
                });
            }

            const dateData = customerDates.get(transactionDate);
            dateData.count++;
            dateData.sourceFiles.add(transactionSourceFile);

            // Add amount to appropriate currency
            if (currency === 'MMK') {
                dateData.mmk += amount;
                mmkTransactions++;
            } else if (currency === 'USD') {
                dateData.usd += amount;
                usdTransactions++;
            }
        });

        console.log('Transaction processing summary:', {
            totalTransactions: transactions.length,
            processedCount,
            skippedCount,
            mmkTransactions,
            usdTransactions,
            uniqueCustomers: this.customerData.size
        });

        // Generate alerts and update UI
        this.generateAlerts();
        this.updateSourceFileFilter();
        this.updateUI();

        console.log('=== Customer Analytics Processing Complete ===');
    }

    // Process multiple data sources (for handling multiple CSV files)
    processMultipleDataSources(dataSources) {
        console.log('=== Processing Multiple Data Sources ===');
        console.log(`Processing ${dataSources.length} data sources`);

        // Clear previous data
        this.customerData.clear();
        this.alertCustomers.clear();
        this.sourceFiles.clear();
        this.transactionSourceMap.clear();
        this.allTransactions = [];

        // Process each data source
        dataSources.forEach((dataSource, index) => {
            const { transactions, fileName } = dataSource;
            console.log(`Processing source ${index + 1}: ${fileName} with ${transactions.length} transactions`);

            // Process transactions from this source
            this.processTransactionsFromSource(transactions, fileName);
        });

        // Generate alerts and update UI
        this.generateAlerts();
        this.updateSourceFileFilter();
        this.updateUI();

        console.log('=== Multiple Data Sources Processing Complete ===');
    }

    // Process transactions from a specific source file
    processTransactionsFromSource(transactions, sourceFileName) {
        if (!transactions || !Array.isArray(transactions)) {
            console.warn('Invalid transaction data for source:', sourceFileName);
            return;
        }

        console.log(`Processing transactions from source: ${sourceFileName}`);

        // Add source file to tracking
        this.sourceFiles.add(sourceFileName);

        // Add transactions to all transactions array (avoid duplicates)
        const existingTransactionIds = new Set(this.allTransactions.map(t =>
            `${t.CUSTOMER_NAME || t.PARTICIPANT_NAME_CONDUCTOR || t.PARTICIPANT_NAME}_${t.TRANSACTION_DATE}_${t.TRANSACTION_AMOUNT}_${t.SOURCE_FILE}`
        ));

        transactions.forEach(transaction => {
            transaction.SOURCE_FILE = sourceFileName;
            const transactionId = `${transaction.CUSTOMER_NAME || transaction.PARTICIPANT_NAME_CONDUCTOR || transaction.PARTICIPANT_NAME}_${transaction.TRANSACTION_DATE}_${transaction.TRANSACTION_AMOUNT}_${sourceFileName}`;

            if (!existingTransactionIds.has(transactionId)) {
                this.allTransactions.push(transaction);
                existingTransactionIds.add(transactionId);
            }
        });

        // Process transactions similar to processTransactionData but without clearing data
        let processedCount = 0;
        let skippedCount = 0;

        transactions.forEach((transaction, index) => {
            const customerName = this.getCustomerName(transaction);
            const customerId = this.getCustomerId(transaction);
            const transactionDate = this.extractDate(transaction.TRANSACTION_DATE);
            const amount = this.parseAmount(transaction.TRANSACTION_AMOUNT);
            const currency = (transaction.TRANSACTION_CURRENCY || 'MMK').toUpperCase();

            // Track source file information
            const transactionId = `${customerName}_${customerId}_${transactionDate}_${sourceFileName}_${index}`;
            this.transactionSourceMap.set(transactionId, sourceFileName);

            if (!customerName || !transactionDate || amount <= 0) {
                skippedCount++;
                return;
            }

            processedCount++;

            // Create customer key
            const customerKey = `${customerName}|${customerId}`;

            // Initialize customer data if not exists
            if (!this.customerData.has(customerKey)) {
                this.customerData.set(customerKey, new Map());
            }

            const customerDates = this.customerData.get(customerKey);

            // Initialize date data if not exists
            if (!customerDates.has(transactionDate)) {
                customerDates.set(transactionDate, {
                    mmk: 0,
                    usd: 0,
                    count: 0,
                    customerName: customerName,
                    customerId: customerId,
                    sourceFiles: new Set()
                });
            }

            const dateData = customerDates.get(transactionDate);
            dateData.count++;
            dateData.sourceFiles.add(sourceFileName);

            // Add amount to appropriate currency
            if (currency === 'MMK') {
                dateData.mmk += amount;
            } else if (currency === 'USD') {
                dateData.usd += amount;
            }
        });

        console.log(`Source ${sourceFileName}: processed ${processedCount}, skipped ${skippedCount}`);

        // Generate alerts and update UI after processing this source
        this.generateAlerts();
        this.updateUI();
    }

    // Update the source file filter dropdown with available files
    updateSourceFileFilter() {
        console.log('Updating source file filter...');

        if (!this.elements.customerSourceFileFilter) {
            console.warn('Source file filter element not found');
            return;
        }

        // Clear existing options except "All Source Files"
        const filterElement = this.elements.customerSourceFileFilter;
        filterElement.innerHTML = '<option value="all">All Source Files</option>';

        // Add options for each source file
        const sortedFiles = Array.from(this.sourceFiles).sort();
        sortedFiles.forEach(fileName => {
            const option = document.createElement('option');
            option.value = fileName;
            option.textContent = this.formatSourceFileName(fileName);
            filterElement.appendChild(option);
        });

        console.log(`Source file filter updated with ${sortedFiles.length} files:`, sortedFiles);
    }

    // Format source file name for display
    formatSourceFileName(fileName) {
        if (!fileName || fileName === 'Unknown Source') {
            return 'Unknown Source';
        }

        // Extract just the filename from full path if needed
        const justFileName = fileName.split(/[/\\]/).pop();

        // If it's a CSV file, show it nicely
        if (justFileName.toLowerCase().endsWith('.csv')) {
            return justFileName;
        }

        // For other files or if no extension, show as is
        return justFileName || fileName;
    }

    // Extract customer name from transaction
    getCustomerName(transaction) {
        return transaction.CUSTOMER_NAME || 
               transaction.PARTICIPANT_NAME_CONDUCTOR || 
               transaction.PARTICIPANT_NAME || 
               'Unknown Customer';
    }

    // Extract customer ID from transaction
    getCustomerId(transaction) {
        const participantId = transaction.PARTICIPANT_ID_NUMBER_CONDUCTOR;
        const customerId = transaction.CUSTOMER_ID;
        const participantIdAlt = transaction.PARTICIPANT_ID;
        const accountNumber = transaction.ACCOUNT_NUMBER;

        // Debug logging for first few transactions
        if (Math.random() < 0.01) { // Log 1% of transactions for debugging
            console.log('Customer ID extraction debug:', {
                PARTICIPANT_ID_NUMBER_CONDUCTOR: participantId,
                CUSTOMER_ID: customerId,
                PARTICIPANT_ID: participantIdAlt,
                ACCOUNT_NUMBER: accountNumber
            });
        }

        return participantId ||
               customerId ||
               participantIdAlt ||
               accountNumber ||
               'Unknown ID';
    }

    // Extract date from transaction date string
    extractDate(dateString) {
        if (!dateString) return null;
        
        try {
            // Handle format: "11-JUN-25 01.22.40.321000 PM"
            const datePart = dateString.split(' ')[0];
            return datePart;
        } catch (error) {
            console.warn('Error parsing date:', dateString, error);
            return null;
        }
    }

    // Parse transaction amount
    parseAmount(amount) {
        if (typeof amount === 'number') return amount;
        if (typeof amount === 'string') {
            const parsed = parseFloat(amount.replace(/[^\d.-]/g, ''));
            return isNaN(parsed) ? 0 : parsed;
        }
        return 0;
    }

    // Generate alerts for customers exceeding thresholds
    generateAlerts() {
        this.alertCustomers.clear();
        let mmkAlertCount = 0;
        let usdAlertCount = 0;

        this.customerData.forEach((customerDates, customerKey) => {
            customerDates.forEach((dateData, date) => {
                let hasAlert = false;

                // Check MMK threshold
                if (dateData.mmk >= this.MMK_THRESHOLD) {
                    hasAlert = true;
                    mmkAlertCount++;
                }

                // Check USD threshold
                if (dateData.usd >= this.USD_THRESHOLD) {
                    hasAlert = true;
                    usdAlertCount++;
                }

                if (hasAlert) {
                    this.alertCustomers.add(`${customerKey}|${date}`);
                }
            });
        });

        // Show alert banner if there are alerts
        if (this.alertCustomers.size > 0) {
            this.showAlertBanner();
        }
    }

    // Show alert banner
    showAlertBanner() {
        if (this.elements.customerAlertBanner) {
            const alertCount = this.alertCustomers.size;
            const message = `${alertCount} customer${alertCount > 1 ? 's' : ''} with daily transactions exceeding thresholds detected.`;
            
            if (this.elements.customerAlertMessage) {
                this.elements.customerAlertMessage.textContent = message;
            }
            
            this.elements.customerAlertBanner.style.display = 'block';
        }
    }

    // Hide alert banner
    hideAlertBanner() {
        if (this.elements.customerAlertBanner) {
            this.elements.customerAlertBanner.style.display = 'none';
        }
    }

    // Update UI with current data
    updateUI() {
        this.updateMetrics();
        this.updateTable();
        this.updateSummary();
    }

    // Update metrics cards
    updateMetrics() {
        const totalCustomers = this.customerData.size;
        const alertCustomers = new Set();
        let mmkAlertCustomers = 0;
        let usdAlertCustomers = 0;

        this.customerData.forEach((customerDates, customerKey) => {
            let customerHasMMKAlert = false;
            let customerHasUSDAlert = false;

            customerDates.forEach((dateData) => {
                if (dateData.mmk >= this.MMK_THRESHOLD) {
                    customerHasMMKAlert = true;
                }
                if (dateData.usd >= this.USD_THRESHOLD) {
                    customerHasUSDAlert = true;
                }
            });

            if (customerHasMMKAlert) {
                mmkAlertCustomers++;
                alertCustomers.add(customerKey);
            }
            if (customerHasUSDAlert) {
                usdAlertCustomers++;
                alertCustomers.add(customerKey);
            }
        });

        // Update metric elements
        this.updateElement('activeCustomerAlerts', alertCustomers.size);
        this.updateElement('mmkAlertCustomers', mmkAlertCustomers);
        this.updateElement('usdAlertCustomers', usdAlertCustomers);
        this.updateElement('totalMonitoredCustomers', totalCustomers);
    }

    // Helper method to update element text content
    updateElement(elementKey, value) {
        if (this.elements[elementKey]) {
            this.elements[elementKey].textContent = value.toLocaleString();
        }
    }

    // Update the customer analytics table
    updateTable() {
        console.log('=== Updating Customer Analytics Table ===');

        if (!this.elements.customerAnalyticsTableBody) {
            console.error('Customer analytics table body element not found');
            return;
        }

        const tableData = this.getFilteredAndSortedData();
        console.log(`Table update: ${tableData.length} rows to display`);

        if (tableData.length === 0) {
            console.log('No data to display, showing empty message');
            this.elements.customerAnalyticsTableBody.innerHTML = `
                <tr class="empty-table-message">
                    <td colspan="8">No high-value customer alerts detected. Upload CSV files with customers exceeding daily thresholds (≥1B MMK or ≥10K USD).</td>
                </tr>
            `;

            // Hide summary when no data
            if (this.elements.customerAnalyticsSummary) {
                this.elements.customerAnalyticsSummary.style.display = 'none';
            }
            return;
        }

        // Generate table rows
        console.log('Generating table rows...');
        const rows = tableData.map(data => this.createTableRow(data)).join('');
        this.elements.customerAnalyticsTableBody.innerHTML = rows;

        // Attach event listeners to action buttons
        console.log('Attaching table event listeners...');
        this.attachTableEventListeners();

        // Show the summary
        if (this.elements.customerAnalyticsSummary) {
            this.elements.customerAnalyticsSummary.style.display = 'block';
            console.log('Summary section displayed');
        }

        console.log('=== Table Update Complete ===');
    }

    // Get filtered and sorted customer data (ONLY ALERT CUSTOMERS) - ENHANCED WITH DEBUGGING
    getFilteredAndSortedData() {
        const alertFilter = this.elements.customerAlertFilter?.value || 'alert';
        const sortBy = this.elements.customerSortBy?.value || 'alert-priority';
        const dateFilter = this.elements.customerDateFilter?.value || 'all';
        const sourceFileFilter = this.getSelectedSourceFiles();

        // Debug current filter values
        console.log('Current filter values:', {
            alertFilter,
            sortBy,
            dateFilter,
            sourceFileFilter,
            availableSourceFiles: Array.from(this.sourceFiles)
        });

        let data = [];
        let totalProcessed = 0;
        let alertCustomers = 0;
        let filteredOut = 0;

        console.log('Filtering customer data:', { alertFilter, sortBy, dateFilter, sourceFileFilter, totalCustomerKeys: this.customerData.size });

        // Convert customer data to array format - ONLY INCLUDE ALERT CUSTOMERS
        this.customerData.forEach((customerDates, customerKey) => {
            const [customerName, customerId] = customerKey.split('|');

            customerDates.forEach((dateData, date) => {
                totalProcessed++;

                // Apply date filter
                if (dateFilter !== 'all' && !this.matchesDateFilter(date, dateFilter)) {
                    return;
                }

                const alertStatus = this.getAlertStatus(dateData);

                // ONLY INCLUDE CUSTOMERS WITH ALERTS - Skip customers with no alerts
                if (alertStatus === 'no-alert') {
                    return;
                }

                alertCustomers++;

                // Apply additional alert filter for specific alert types
                if (alertFilter !== 'alert' && !this.matchesAlertFilter(alertStatus, alertFilter)) {
                    filteredOut++;
                    console.log('Filtered out customer:', { customerName, alertStatus, alertFilter, dateData });
                    return;
                }

                // Apply source file filter
                if (!this.matchesSourceFileFilter(dateData.sourceFiles, sourceFileFilter)) {
                    filteredOut++;
                    return;
                }

                data.push({
                    customerName,
                    customerId,
                    date,
                    mmkAmount: dateData.mmk,
                    usdAmount: dateData.usd,
                    transactionCount: dateData.count,
                    alertStatus,
                    sourceFiles: Array.from(dateData.sourceFiles || [])
                });
            });
        });

        console.log('Filter results:', {
            totalProcessed,
            alertCustomers,
            filteredOut,
            finalDataCount: data.length,
            alertFilter
        });

        // Sort data
        data.sort((a, b) => this.sortData(a, b, sortBy));

        return data;
    }

    // Get selected source files from the filter
    getSelectedSourceFiles() {
        if (!this.elements.customerSourceFileFilter) {
            return ['all'];
        }

        const selectedOptions = Array.from(this.elements.customerSourceFileFilter.selectedOptions);
        const selectedValues = selectedOptions.map(option => option.value);

        // If "all" is selected or nothing is selected, return all
        if (selectedValues.length === 0 || selectedValues.includes('all')) {
            return ['all'];
        }

        return selectedValues;
    }

    // Check if source files match the filter
    matchesSourceFileFilter(customerSourceFiles, selectedSourceFiles) {
        // If "all" is selected, show everything
        if (selectedSourceFiles.includes('all')) {
            return true;
        }

        // Check if any of the customer's source files match the selected filters
        if (!customerSourceFiles || customerSourceFiles.size === 0) {
            return selectedSourceFiles.includes('Unknown Source');
        }

        // Check for intersection between customer source files and selected files
        for (const sourceFile of customerSourceFiles) {
            if (selectedSourceFiles.includes(sourceFile)) {
                return true;
            }
        }

        return false;
    }

    // Check if date matches filter
    matchesDateFilter(date, filter) {
        const today = new Date();
        const transactionDate = this.parseTransactionDate(date);

        if (!transactionDate) return true;

        switch (filter) {
            case 'today':
                return this.isSameDay(transactionDate, today);
            case 'week':
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                return transactionDate >= weekAgo;
            case 'month':
                const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                return transactionDate >= monthAgo;
            default:
                return true;
        }
    }

    // Parse transaction date string to Date object
    parseTransactionDate(dateString) {
        try {
            // Handle format: "11-JUN-25"
            const parts = dateString.split('-');
            if (parts.length === 3) {
                const day = parseInt(parts[0]);
                const monthMap = {
                    'JAN': 0, 'FEB': 1, 'MAR': 2, 'APR': 3, 'MAY': 4, 'JUN': 5,
                    'JUL': 6, 'AUG': 7, 'SEP': 8, 'OCT': 9, 'NOV': 10, 'DEC': 11
                };
                const month = monthMap[parts[1]];
                const year = 2000 + parseInt(parts[2]); // Assuming 2-digit year

                if (month !== undefined) {
                    return new Date(year, month, day);
                }
            }
        } catch (error) {
            console.warn('Error parsing transaction date:', dateString, error);
        }
        return null;
    }

    // Check if two dates are the same day
    isSameDay(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }

    // Check if alert status matches filter (ONLY FOR ALERT CUSTOMERS) - UPDATED FILTERING
    matchesAlertFilter(alertStatus, filter) {
        // Debug logging for filter issues
        if (Math.random() < 0.1) { // Log 10% of filter checks for debugging
            console.log('Filter check:', { alertStatus, filter, match: this.getFilterMatch(alertStatus, filter) });
        }

        switch (filter) {
            case 'alert':
                // Show all alert customers (excluding no-alert which is already filtered out)
                return alertStatus !== 'no-alert';
            case 'mmk-alert':
                // Show customers with MMK alerts (including high alerts which have MMK component)
                return alertStatus === 'mmk-alert' || alertStatus === 'high-alert';
            case 'usd-alert':
                // Show customers with USD alerts (including high alerts which have USD component)
                return alertStatus === 'usd-alert' || alertStatus === 'high-alert';
            default:
                // Default to showing all alert customers
                return alertStatus !== 'no-alert';
        }
    }

    // Helper method for debugging filter matches
    getFilterMatch(alertStatus, filter) {
        switch (filter) {
            case 'alert': return alertStatus !== 'no-alert';
            case 'mmk-alert': return alertStatus === 'mmk-alert' || alertStatus === 'high-alert';
            case 'usd-alert': return alertStatus === 'usd-alert' || alertStatus === 'high-alert';
            case 'high-alert': return alertStatus === 'high-alert';
            default: return alertStatus !== 'no-alert';
        }
    }

    // Get alert status for customer data
    getAlertStatus(dateData) {
        const hasMMKAlert = dateData.mmk >= this.MMK_THRESHOLD;
        const hasUSDAlert = dateData.usd >= this.USD_THRESHOLD;

        if (hasMMKAlert && hasUSDAlert) {
            return 'high-alert';
        } else if (hasMMKAlert) {
            return 'mmk-alert';
        } else if (hasUSDAlert) {
            return 'usd-alert';
        } else {
            return 'no-alert';
        }
    }

    // Sort data based on sort criteria (optimized for alert customers) - ENHANCED WITH USD SORTING FIX
    sortData(a, b, sortBy) {
        // Debug logging for USD sorting
        if (sortBy === 'usd-desc' && Math.random() < 0.2) { // Log 20% of USD sorts for debugging
            console.log('USD Sort Debug:', {
                customerA: a.customerName,
                customerB: b.customerName,
                usdA: a.usdAmount,
                usdB: b.usdAmount,
                sortResult: b.usdAmount - a.usdAmount
            });
        }

        switch (sortBy) {
            case 'alert-priority':
                // Prioritize high alerts first, then by amount
                const alertPriority = {
                    'high-alert': 4,
                    'mmk-alert': 3,
                    'usd-alert': 2,
                    'no-alert': 1
                };
                const priorityDiff = (alertPriority[b.alertStatus] || 0) - (alertPriority[a.alertStatus] || 0);
                if (priorityDiff !== 0) return priorityDiff;
                // If same alert priority, sort by total value (MMK + USD converted)
                const totalA = a.mmkAmount + (a.usdAmount * 1600); // Rough MMK conversion
                const totalB = b.mmkAmount + (b.usdAmount * 1600);
                return totalB - totalA;
            case 'mmk-desc':
                // Sort by MMK amount descending
                const mmkDiff = b.mmkAmount - a.mmkAmount;
                if (Math.abs(mmkDiff) < 0.01) { // If MMK amounts are equal, sort by customer name
                    return a.customerName.localeCompare(b.customerName);
                }
                return mmkDiff;
            case 'usd-desc':
                // Sort by USD amount descending - FIXED
                const usdDiff = b.usdAmount - a.usdAmount;
                console.log(`USD Sort: ${a.customerName}(${a.usdAmount}) vs ${b.customerName}(${b.usdAmount}) = ${usdDiff}`);
                if (Math.abs(usdDiff) < 0.01) { // If USD amounts are equal, sort by customer name
                    return a.customerName.localeCompare(b.customerName);
                }
                return usdDiff;
            case 'total-desc':
                // Sort by transaction count descending
                const countDiff = b.transactionCount - a.transactionCount;
                if (countDiff === 0) { // If counts are equal, sort by customer name
                    return a.customerName.localeCompare(b.customerName);
                }
                return countDiff;
            case 'date-desc':
                // Sort by date descending (newest first)
                const dateDiff = b.date.localeCompare(a.date);
                if (dateDiff === 0) { // If dates are equal, sort by customer name
                    return a.customerName.localeCompare(b.customerName);
                }
                return dateDiff;
            case 'customer-name':
                // Sort by customer name ascending
                return a.customerName.localeCompare(b.customerName);
            default:
                // Default to alert priority sorting
                return this.sortData(a, b, 'alert-priority');
        }
    }

    // Create table row HTML
    createTableRow(data) {
        const alertStatusClass = data.alertStatus;
        const alertStatusText = this.getAlertStatusText(data.alertStatus);

        // Escape values for safe HTML attributes and JavaScript
        const escapedCustomerName = this.escapeHtml(data.customerName);
        const escapedCustomerId = this.escapeHtml(data.customerId);
        const escapedDate = this.escapeHtml(data.date);

        // Create unique identifiers for the buttons to avoid onclick issues
        const rowId = `customer-row-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        return `
            <tr data-customer="${escapedCustomerName}" data-customer-id="${escapedCustomerId}" data-date="${escapedDate}" id="${rowId}">
                <td class="customer-name-cell" title="${escapedCustomerName}">${this.truncateText(data.customerName, 30)}</td>
                <td class="customer-id-cell" title="${escapedCustomerId}">${this.truncateText(data.customerId, 20)}</td>
                <td class="date-cell">${data.date}</td>
                <td class="amount-cell mmk-amount">${this.formatAmount(data.mmkAmount, 'MMK')}</td>
                <td class="amount-cell usd-amount">${this.formatAmount(data.usdAmount, 'USD')}</td>
                <td class="count-cell">${data.transactionCount.toLocaleString()}</td>
                <td class="alert-status-cell"><span class="alert-status ${alertStatusClass}">${alertStatusText}</span></td>
                <td class="actions-cell">
                    <button class="customer-action-btn view-details" data-customer-name="${escapedCustomerName}" data-customer-id="${escapedCustomerId}" data-date="${escapedDate}" title="View detailed transactions">
                        <span class="btn-icon">👁️</span>
                        <span class="btn-text">View Details</span>
                    </button>
                </td>
            </tr>
        `;
    }

    // Escape HTML to prevent XSS and syntax errors
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Attach event listeners to table action buttons (VIEW ONLY)
    attachTableEventListeners() {
        if (!this.elements.customerAnalyticsTableBody) {
            return;
        }

        // Remove existing event listeners to prevent duplicates
        const existingButtons = this.elements.customerAnalyticsTableBody.querySelectorAll('.customer-action-btn');
        existingButtons.forEach(button => {
            button.replaceWith(button.cloneNode(true));
        });

        // Add event listeners for view details buttons only
        const viewButtons = this.elements.customerAnalyticsTableBody.querySelectorAll('.view-details');
        viewButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const customerName = button.getAttribute('data-customer-name');
                const customerId = button.getAttribute('data-customer-id');
                const date = button.getAttribute('data-date');

                // Add loading state
                button.disabled = true;
                button.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Loading...</span>';

                // View customer details
                setTimeout(() => {
                    this.viewCustomerDetails(customerName, customerId, date);
                    // Reset button state
                    button.disabled = false;
                    button.innerHTML = '<span class="btn-icon">👁️</span><span class="btn-text">View Details</span>';
                }, 100);
            });
        });
    }

    // Get alert status text for display
    getAlertStatusText(status) {
        switch (status) {
            case 'high-alert':
                return 'HIGH ALERT';
            case 'mmk-alert':
                return 'MMK ALERT';
            case 'usd-alert':
                return 'USD ALERT';
            case 'no-alert':
                return 'NO ALERT';
            default:
                return 'UNKNOWN';
        }
    }

    // Truncate text to specified length
    truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    // Format amount with currency
    formatAmount(amount, currency) {
        if (amount === 0) {
            return `0.00 ${currency}`;
        }

        const formatted = amount.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        return `${formatted} ${currency}`;
    }

    // Update summary statistics
    updateSummary() {
        let totalCustomers = 0;
        let alertCustomers = 0;
        let totalMMK = 0;
        let totalUSD = 0;

        const uniqueCustomers = new Set();
        const uniqueAlertCustomers = new Set();

        this.customerData.forEach((customerDates, customerKey) => {
            const [customerName] = customerKey.split('|');
            uniqueCustomers.add(customerName);

            let customerHasAlert = false;

            customerDates.forEach((dateData) => {
                totalMMK += dateData.mmk;
                totalUSD += dateData.usd;

                if (dateData.mmk >= this.MMK_THRESHOLD || dateData.usd >= this.USD_THRESHOLD) {
                    customerHasAlert = true;
                }
            });

            if (customerHasAlert) {
                uniqueAlertCustomers.add(customerName);
            }
        });

        totalCustomers = uniqueCustomers.size;
        alertCustomers = uniqueAlertCustomers.size;

        // Update summary elements
        this.updateElement('summaryTotalCustomers', totalCustomers);
        this.updateElement('summaryAlertCustomers', alertCustomers);

        if (this.elements.summaryTotalMMK) {
            this.elements.summaryTotalMMK.textContent = this.formatAmount(totalMMK, 'MMK');
        }

        if (this.elements.summaryTotalUSD) {
            this.elements.summaryTotalUSD.textContent = this.formatAmount(totalUSD, 'USD');
        }
    }

    // Refresh analytics data
    refreshAnalytics() {
        if (window.dataProcessor && window.dataProcessor.getAllTransactions) {
            const allTransactions = window.dataProcessor.getAllTransactions();
            console.log('Refreshing customer analytics with', allTransactions.length, 'transactions');

            // Clear existing data first
            this.customerData.clear();
            this.alertCustomers.clear();
            this.sourceFiles.clear();
            this.transactionSourceMap.clear();
            this.allTransactions = [];
            this.hideAlertBanner();

            // Group transactions by source file for proper processing
            const transactionsByFile = new Map();

            allTransactions.forEach(transaction => {
                const sourceFile = transaction.SOURCE_FILE || transaction.fileName || 'Unknown Source';
                if (!transactionsByFile.has(sourceFile)) {
                    transactionsByFile.set(sourceFile, []);
                }
                transactionsByFile.get(sourceFile).push(transaction);
            });

            // Process each file's transactions separately to maintain source file tracking
            transactionsByFile.forEach((transactions, sourceFile) => {
                console.log(`Processing ${transactions.length} transactions from ${sourceFile}`);
                this.processTransactionsFromSource(transactions, sourceFile);
            });

            // Update source file filter after processing all files
            this.updateSourceFileFilter();

            if (window.app) {
                window.app.showNotification(`Customer analytics refreshed with ${allTransactions.length} transactions from ${transactionsByFile.size} files`, 'success', 3000);
            }
        } else {
            console.warn('DataProcessor not available for refresh');
            if (window.app) {
                window.app.showNotification('Unable to refresh customer analytics - DataProcessor not available', 'error', 3000);
            }
        }
    }

    // Export customer analytics data
    exportCustomerAnalytics() {
        try {
            const data = this.getFilteredAndSortedData();

            if (data.length === 0) {
                if (window.app) {
                    window.app.showNotification('No customer data to export', 'warning', 3000);
                }
                return;
            }

            // Create CSV content
            const headers = [
                'Customer Name',
                'Customer ID',
                'Transaction Date',
                'Total MMK Amount',
                'Total USD Amount',
                'Transaction Count',
                'Alert Status'
            ];

            const csvContent = [
                headers.join(','),
                ...data.map(row => [
                    `"${row.customerName}"`,
                    `"${row.customerId}"`,
                    row.date,
                    row.mmkAmount,
                    row.usdAmount,
                    row.transactionCount,
                    `"${this.getAlertStatusText(row.alertStatus)}"`
                ].join(','))
            ].join('\n');

            // Download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `Customer_Analytics_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            if (window.app) {
                window.app.showNotification('Customer analytics exported successfully', 'success', 3000);
            }
        } catch (error) {
            console.error('Error exporting customer analytics:', error);
            if (window.app) {
                window.app.showNotification('Error exporting customer analytics', 'error', 3000);
            }
        }
    }

    // Test customer alerts (for demonstration) - ONLY ALERT CUSTOMERS
    testCustomerAlerts() {
        // Create sample alert data - ALL CUSTOMERS WILL TRIGGER ALERTS
        const sampleData = [
            {
                customerName: 'High Alert Customer A',
                customerId: 'ALERT001',
                date: '15-JUN-25',
                mmk: 2500000000, // 2.5B MMK - triggers MMK alert
                usd: 15000, // 15K USD - triggers USD alert (HIGH ALERT)
                count: 5
            },
            {
                customerName: 'MMK Alert Customer B',
                customerId: 'ALERT002',
                date: '15-JUN-25',
                mmk: 1800000000, // 1.8B MMK - triggers MMK alert
                usd: 5000, // Below USD threshold
                count: 3
            },
            {
                customerName: 'USD Alert Customer C',
                customerId: 'ALERT003',
                date: '16-JUN-25',
                mmk: 500000000, // Below MMK threshold
                usd: 25000, // 25K USD - triggers USD alert
                count: 4
            },
            {
                customerName: 'High Alert Customer D',
                customerId: 'ALERT004',
                date: '16-JUN-25',
                mmk: 3000000000, // 3B MMK - triggers MMK alert
                usd: 50000, // 50K USD - triggers USD alert (HIGH ALERT)
                count: 8
            }
        ];

        // Temporarily add sample data
        sampleData.forEach(data => {
            const customerKey = `${data.customerName}|${data.customerId}`;
            if (!this.customerData.has(customerKey)) {
                this.customerData.set(customerKey, new Map());
            }
            this.customerData.get(customerKey).set(data.date, data);
        });

        // Generate alerts and update UI
        this.generateAlerts();
        this.updateUI();

        if (window.app) {
            window.app.showNotification(`Generated ${sampleData.length} test customer alerts - only alert customers shown`, 'info', 4000);
        }
    }

    // View customer details - show detailed transaction view
    viewCustomerDetails(customerName, customerId, date) {
        console.log('=== Viewing Customer Details ===');
        console.log('Customer details request:', { customerName, customerId, date });
        console.log('Total transactions available:', this.allTransactions.length);

        // Filter transactions for this specific customer and date
        const customerTransactions = this.getCustomerTransactionsForDate(customerName, customerId, date);
        console.log(`Found ${customerTransactions.length} transactions for customer`);

        if (customerTransactions.length === 0) {
            console.warn('No transactions found for customer details');
            console.log('Debug: Checking first few transactions for matching patterns...');

            // Debug: Show first few transactions to help identify matching issues
            this.allTransactions.slice(0, 5).forEach((tx, index) => {
                console.log(`Transaction ${index + 1}:`, {
                    customerName: this.getCustomerName(tx),
                    customerId: this.getCustomerId(tx),
                    date: this.extractDate(tx.TRANSACTION_DATE),
                    rawDate: tx.TRANSACTION_DATE
                });
            });

            if (window.app) {
                window.app.showNotification(`No transactions found for ${customerName} on ${date}`, 'warning', 3000);
            }
            return;
        }

        console.log('Showing customer transaction details modal...');

        // Show the customer transaction details view
        this.showCustomerTransactionDetails(customerName, customerId, date, customerTransactions);

        if (window.app) {
            window.app.showNotification(`Showing ${customerTransactions.length} transactions for ${customerName}`, 'info', 3000);
        }

        console.log('=== Customer Details View Complete ===');
    }

    // Get all transactions for a specific customer and date
    getCustomerTransactionsForDate(customerName, customerId, date) {
        if (!this.allTransactions || this.allTransactions.length === 0) {
            console.warn('No transaction data available for customer details');
            return [];
        }

        return this.allTransactions.filter(transaction => {
            const txCustomerName = this.getCustomerName(transaction);
            const txCustomerId = this.getCustomerId(transaction);
            const txDate = this.extractDate(transaction.TRANSACTION_DATE);

            // Match customer name/ID and date
            const customerMatch = (txCustomerName === customerName) || (txCustomerId === customerId);
            const dateMatch = txDate === date;

            return customerMatch && dateMatch;
        });
    }

    // Show customer transaction details modal
    showCustomerTransactionDetails(customerName, customerId, date, transactions) {
        console.log('=== Showing Customer Transaction Details Modal ===');
        console.log('Modal data:', { customerName, customerId, date, transactionCount: transactions.length });

        if (!this.elements.customerTransactionModal) {
            console.error('Customer transaction modal element not found');
            console.error('Available modal elements:', Object.keys(this.elements).filter(key => key.includes('modal')));
            return;
        }

        // Store current transaction data for modal
        this.currentModalTransactions = transactions;
        console.log('Stored modal transactions:', this.currentModalTransactions.length);

        // Update modal header information
        if (this.elements.modalCustomerName) {
            this.elements.modalCustomerName.textContent = customerName;
            console.log('✓ Modal customer name updated');
        } else {
            console.warn('✗ Modal customer name element not found');
        }

        if (this.elements.modalCustomerDate) {
            this.elements.modalCustomerDate.textContent = date;
            console.log('✓ Modal customer date updated');
        } else {
            console.warn('✗ Modal customer date element not found');
        }

        // Calculate summary statistics
        console.log('Calculating transaction summary...');
        const summary = this.calculateTransactionSummary(transactions);
        console.log('Transaction summary:', summary);
        this.updateModalSummary(summary);

        // Populate the transaction details table
        console.log('Populating modal transaction table...');
        this.populateModalTransactionTable(transactions);

        // Show the modal with animation
        console.log('Displaying modal...');
        this.showCustomerTransactionModal();

        console.log('=== Modal Display Complete ===');
    }

    // Calculate summary statistics for transactions
    calculateTransactionSummary(transactions) {
        let totalMMK = 0;
        let totalUSD = 0;
        let totalCount = transactions.length;

        transactions.forEach(transaction => {
            const amount = this.parseAmount(transaction.TRANSACTION_AMOUNT);
            const currency = (transaction.TRANSACTION_CURRENCY || 'MMK').toUpperCase();

            if (currency === 'MMK') {
                totalMMK += amount;
            } else if (currency === 'USD') {
                totalUSD += amount;
            }
        });

        // Determine alert status
        let alertStatus = 'No Alert';
        if (totalMMK >= this.MMK_THRESHOLD && totalUSD >= this.USD_THRESHOLD) {
            alertStatus = 'HIGH ALERT';
        } else if (totalMMK >= this.MMK_THRESHOLD) {
            alertStatus = 'MMK ALERT';
        } else if (totalUSD >= this.USD_THRESHOLD) {
            alertStatus = 'USD ALERT';
        }

        return {
            totalCount,
            totalMMK,
            totalUSD,
            alertStatus
        };
    }

    // Update modal summary statistics
    updateModalSummary(summary) {
        if (this.elements.modalTotalCount) {
            this.elements.modalTotalCount.textContent = summary.totalCount.toLocaleString();
        }
        if (this.elements.modalTotalMMK) {
            this.elements.modalTotalMMK.textContent = this.formatAmount(summary.totalMMK, 'MMK');
        }
        if (this.elements.modalTotalUSD) {
            this.elements.modalTotalUSD.textContent = this.formatAmount(summary.totalUSD, 'USD');
        }
        if (this.elements.modalAlertStatus) {
            this.elements.modalAlertStatus.textContent = summary.alertStatus;

            // Apply appropriate styling based on alert status
            this.elements.modalAlertStatus.className = 'stat-value';
            if (summary.alertStatus.includes('HIGH')) {
                this.elements.modalAlertStatus.style.color = '#e74c3c';
            } else if (summary.alertStatus.includes('ALERT')) {
                this.elements.modalAlertStatus.style.color = '#f39c12';
            } else {
                this.elements.modalAlertStatus.style.color = '#2ecc71';
            }
        }
    }

    // Populate modal transaction details table
    populateModalTransactionTable(transactions) {
        if (!this.elements.modalTransactionTableBody) {
            console.error('Modal transaction table body not found');
            return;
        }

        if (transactions.length === 0) {
            this.elements.modalTransactionTableBody.innerHTML = `
                <tr class="empty-message-row">
                    <td colspan="6">No transactions found for this customer and date.</td>
                </tr>
            `;
            return;
        }

        // Sort transactions by time (most recent first)
        const sortedTransactions = transactions.sort((a, b) => {
            const timeA = this.extractTime(a.TRANSACTION_DATE);
            const timeB = this.extractTime(b.TRANSACTION_DATE);
            return timeB.localeCompare(timeA);
        });

        // Generate table rows
        const rows = sortedTransactions.map(transaction => this.createModalTransactionRow(transaction)).join('');
        this.elements.modalTransactionTableBody.innerHTML = rows;
    }

    // Extract time from transaction date string
    extractTime(dateString) {
        if (!dateString) return '';

        try {
            // Handle format: "11-JUN-25 01.22.40.321000 PM"
            const parts = dateString.split(' ');
            if (parts.length >= 3) {
                return `${parts[1]} ${parts[2]}`;
            }
            return parts[1] || '';
        } catch (error) {
            console.warn('Error parsing time from date:', dateString, error);
            return '';
        }
    }

    // Create transaction detail row HTML
    createTransactionDetailRow(transaction) {
        const amount = this.parseAmount(transaction.TRANSACTION_AMOUNT);
        const currency = (transaction.TRANSACTION_CURRENCY || 'MMK').toUpperCase();
        const time = this.extractTime(transaction.TRANSACTION_DATE);
        const reportType = (transaction.REPORTTYPE || '').toUpperCase();
        const role = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || '';
        const counterparty = transaction.PARTICIPANT_NAME_COUNTERPARTY || 'N/A';
        const counterpartyId = transaction.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A';
        const accountNumber = transaction.ACCOUNT_NUMBER || 'N/A';
        const transactionType = transaction.TRANSACTION_TYPE || 'N/A';
        const serialNumber = transaction.SERIAL_NO || transaction.SERIAL_NUMBER || 'N/A';
        const channel = transaction.CHANNEL || 'N/A';
        const location = transaction.LOCATION || 'N/A';

        // Determine if this is a high-value transaction
        const isHighValue = (currency === 'MMK' && amount >= this.MMK_THRESHOLD) ||
                           (currency === 'USD' && amount >= this.USD_THRESHOLD);

        return `
            <tr>
                <td class="time-cell">${time}</td>
                <td class="amount-cell-details ${isHighValue ? 'high-value' : ''}">${this.formatAmount(amount, currency)}</td>
                <td>${currency}</td>
                <td><span class="transaction-type ${reportType.toLowerCase()}">${reportType}</span></td>
                <td class="role-cell">${role}</td>
                <td title="${counterparty}">${this.truncateText(counterparty, 20)}</td>
                <td title="${counterpartyId}">${this.truncateText(counterpartyId, 15)}</td>
                <td title="${accountNumber}">${this.truncateText(accountNumber, 15)}</td>
                <td title="${transactionType}">${this.truncateText(transactionType, 15)}</td>
                <td title="${serialNumber}">${this.truncateText(serialNumber, 12)}</td>
                <td>${channel}</td>
                <td>${location}</td>
            </tr>
        `;
    }

    // Create modal transaction row HTML
    createModalTransactionRow(transaction) {
        const amount = this.parseAmount(transaction.TRANSACTION_AMOUNT);
        const currency = (transaction.TRANSACTION_CURRENCY || 'MMK').toUpperCase();
        const time = this.extractTime(transaction.TRANSACTION_DATE);
        const reportType = (transaction.REPORTTYPE || '').toUpperCase();
        const role = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || '';
        const counterparty = transaction.PARTICIPANT_NAME_COUNTERPARTY || 'N/A';
        const counterpartyId = transaction.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A';
        const accountNumber = transaction.ACCOUNT_NUMBER || 'N/A';
        const transactionType = transaction.TRANSACTION_TYPE || 'N/A';
        const serialNumber = transaction.SERIAL_NO || transaction.SERIAL_NUMBER || 'N/A';
        const channel = transaction.CHANNEL || 'N/A';
        const location = transaction.LOCATION || 'N/A';

        // Determine if this is a high-value transaction
        const isHighValue = (currency === 'MMK' && amount >= this.MMK_THRESHOLD) ||
                           (currency === 'USD' && amount >= this.USD_THRESHOLD);

        return `
            <tr>
                <td class="time-cell" title="${time}">${time}</td>
                <td class="amount-cell-details ${isHighValue ? 'high-value' : ''}" title="${this.formatAmount(amount, currency)}">${this.formatAmount(amount, currency)}</td>
                <td class="currency-cell">
                    <span class="currency-badge ${currency.toLowerCase()}">${currency}</span>
                </td>
                <td class="type-cell">
                    <span class="transaction-type ${reportType.toLowerCase()}">${reportType}</span>
                </td>
                <td class="counterparty-cell" title="${counterparty}">${this.truncateText(counterparty, 20)}</td>
                <td class="serial-cell" title="${serialNumber}">${this.truncateText(serialNumber, 12)}</td>
            </tr>
        `;
    }

    // Show customer transaction modal
    showCustomerTransactionModal() {
        console.log('Attempting to show customer transaction modal...');

        if (!this.elements.customerTransactionModal) {
            console.error('Cannot show modal: customerTransactionModal element not found');
            console.error('Available elements:', Object.keys(this.elements));
            return;
        }

        console.log('Modal element found, displaying...');

        // Prevent body scrolling
        document.body.style.overflow = 'hidden';

        // Show modal
        this.elements.customerTransactionModal.style.display = 'flex';
        this.elements.customerTransactionModal.classList.add('show');

        console.log('Modal display style set to flex and show class added');

        // Focus management
        setTimeout(() => {
            if (this.elements.modalCloseBtn) {
                this.elements.modalCloseBtn.focus();
                console.log('Focus set to modal close button');
            } else {
                console.warn('Modal close button not found for focus');
            }
        }, 100);

        console.log('Customer transaction modal displayed successfully');
    }

    // Hide customer transaction modal
    hideCustomerTransactionModal() {
        console.log('Hiding customer transaction modal...');

        if (!this.elements.customerTransactionModal) {
            console.warn('Cannot hide modal: customerTransactionModal element not found');
            return;
        }

        // Add closing animation
        this.elements.customerTransactionModal.classList.add('closing');
        console.log('Added closing animation class');

        setTimeout(() => {
            // Hide modal
            this.elements.customerTransactionModal.style.display = 'none';
            this.elements.customerTransactionModal.classList.remove('show', 'closing');

            // Restore body scrolling
            document.body.style.overflow = '';

            // Clear modal content
            this.clearModalContent();

            console.log('Modal hidden and content cleared');
        }, 300);
    }

    // Check if modal is open
    isModalOpen() {
        return this.elements.customerTransactionModal &&
               this.elements.customerTransactionModal.classList.contains('show');
    }

    // Clear modal content
    clearModalContent() {
        if (this.elements.modalTransactionTableBody) {
            this.elements.modalTransactionTableBody.innerHTML = `
                <tr class="empty-message-row">
                    <td colspan="6">No transaction details available.</td>
                </tr>
            `;
        }

        // Reset summary values
        if (this.elements.modalCustomerName) {
            this.elements.modalCustomerName.textContent = '-';
        }
        if (this.elements.modalCustomerDate) {
            this.elements.modalCustomerDate.textContent = '-';
        }

        // Clear stored transaction data
        this.currentModalTransactions = [];
    }

    // Export modal transactions
    exportModalTransactions() {
        if (!this.currentModalTransactions || this.currentModalTransactions.length === 0) {
            if (window.app) {
                window.app.showNotification('No transaction data to export', 'warning', 3000);
            }
            return;
        }

        try {
            // Create CSV content
            const headers = [
                'Time',
                'Amount',
                'Currency',
                'Type',
                'Role',
                'Counterparty',
                'Counterparty ID',
                'Transaction Type',
                'Serial Number',
                'Channel',
                'Location'
            ];

            const csvContent = [
                headers.join(','),
                ...this.currentModalTransactions.map(transaction => [
                    `"${this.extractTime(transaction.TRANSACTION_DATE)}"`,
                    this.parseAmount(transaction.TRANSACTION_AMOUNT),
                    `"${transaction.TRANSACTION_CURRENCY || 'MMK'}"`,
                    `"${transaction.REPORTTYPE || ''}"`,
                    `"${transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE || ''}"`,
                    `"${transaction.PARTICIPANT_NAME_COUNTERPARTY || 'N/A'}"`,
                    `"${transaction.PARTICIPANT_ID_NUMBER_COUNTERPARTY || 'N/A'}"`,
                    `"${transaction.TRANSACTION_TYPE || 'N/A'}"`,
                    `"${transaction.SERIAL_NO || transaction.SERIAL_NUMBER || 'N/A'}"`,
                    `"${transaction.CHANNEL || 'N/A'}"`,
                    `"${transaction.LOCATION || 'N/A'}"`
                ].join(','))
            ].join('\n');

            // Download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);

            const customerName = this.elements.modalCustomerName?.textContent || 'Customer';
            const date = this.elements.modalCustomerDate?.textContent || 'Date';
            link.setAttribute('download', `${customerName}_${date}_Transactions.csv`);

            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            if (window.app) {
                window.app.showNotification('Transaction details exported successfully', 'success', 3000);
            }
        } catch (error) {
            console.error('Error exporting modal transactions:', error);
            if (window.app) {
                window.app.showNotification('Error exporting transaction details', 'error', 3000);
            }
        }
    }

    // Hide customer transaction details (legacy method - now redirects to modal)
    hideCustomerTransactionDetails() {
        this.hideCustomerTransactionModal();
    }

    // Export individual customer data (placeholder for future implementation)
    exportCustomerData(customerName, customerId) {
        if (window.app) {
            window.app.showNotification(`Exporting data for ${customerName}`, 'info', 3000);
        }
        console.log('Export customer data:', { customerName, customerId });
    }

    // Get all customer data (for external access)
    getCustomerData() {
        return this.customerData;
    }

    // Get alert customers (for external access)
    getAlertCustomers() {
        return this.alertCustomers;
    }

    // Clear all data
    clearData() {
        this.customerData.clear();
        this.alertCustomers.clear();
        this.allTransactions = [];
        this.currentModalTransactions = [];
        this.sourceFiles.clear();
        this.transactionSourceMap.clear();
        this.hideAlertBanner();
        this.hideCustomerTransactionModal();
        this.updateSourceFileFilter();
        this.updateUI();
    }

    // Debug function to test customer analytics functionality
    debugCustomerAnalytics() {
        console.log('=== Customer Analytics Debug Information ===');
        console.log('Initialization status:', this.isInitialized);
        console.log('Customer data size:', this.customerData.size);
        console.log('Alert customers size:', this.alertCustomers.size);
        console.log('All transactions count:', this.allTransactions.length);

        // Check critical elements
        const criticalElements = [
            'customerAlertFilter', 'customerSortBy', 'customerDateFilter', 'customerSourceFileFilter',
            'customerAnalyticsTableBody', 'customerTransactionModal'
        ];

        console.log('Element availability:');
        criticalElements.forEach(elementKey => {
            const element = this.elements[elementKey];
            console.log(`  ${elementKey}: ${element ? '✓' : '✗'}`);
        });

        // Show current filter values
        if (this.elements.customerAlertFilter) {
            console.log('Current alert filter:', this.elements.customerAlertFilter.value);
        }
        if (this.elements.customerSortBy) {
            console.log('Current sort by:', this.elements.customerSortBy.value);
        }
        if (this.elements.customerDateFilter) {
            console.log('Current date filter:', this.elements.customerDateFilter.value);
        }
        if (this.elements.customerSourceFileFilter) {
            console.log('Current source file filter:', this.getSelectedSourceFiles());
        }

        // Show source file information
        console.log('Available source files:', Array.from(this.sourceFiles));
        console.log('Transaction source map size:', this.transactionSourceMap.size);

        // Show sample customer data
        if (this.customerData.size > 0) {
            console.log('Sample customer data:');
            let count = 0;
            for (const [customerKey, customerDates] of this.customerData) {
                if (count >= 3) break;
                console.log(`  Customer: ${customerKey}`);
                for (const [date, dateData] of customerDates) {
                    console.log(`    ${date}: MMK=${dateData.mmk}, USD=${dateData.usd}, Count=${dateData.count}`);
                }
                count++;
            }
        }

        console.log('=== End Debug Information ===');
    }

    // Test USD sorting functionality
    testUSDSorting() {
        console.log('=== Testing USD Sorting Functionality ===');

        if (!this.isInitialized) {
            console.error('Customer Analytics not initialized');
            return;
        }

        // Get current data
        const currentData = this.getFilteredAndSortedData();
        console.log(`Current data count: ${currentData.length}`);

        if (currentData.length === 0) {
            console.warn('No data available for USD sorting test');
            return;
        }

        // Test USD sorting
        console.log('Setting sort to USD descending...');
        if (this.elements.customerSortBy) {
            this.elements.customerSortBy.value = 'usd-desc';
            this.updateTable();

            // Get sorted data
            const sortedData = this.getFilteredAndSortedData();
            console.log('USD Sorted data (top 5):');
            sortedData.slice(0, 5).forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.customerName}: USD ${item.usdAmount.toLocaleString()}`);
            });

            // Verify sorting is correct
            let isSortedCorrectly = true;
            for (let i = 1; i < sortedData.length; i++) {
                if (sortedData[i-1].usdAmount < sortedData[i].usdAmount) {
                    isSortedCorrectly = false;
                    console.error(`Sorting error at position ${i}: ${sortedData[i-1].usdAmount} < ${sortedData[i].usdAmount}`);
                    break;
                }
            }

            console.log(`USD Sorting is ${isSortedCorrectly ? 'CORRECT' : 'INCORRECT'}`);
        } else {
            console.error('Sort by element not found');
        }

        console.log('=== USD Sorting Test Complete ===');
    }
}

// Create global instance
window.customerAnalytics = new CustomerAnalytics();

// Global debug functions for testing
window.debugCustomerAnalytics = () => {
    if (window.customerAnalytics) {
        window.customerAnalytics.debugCustomerAnalytics();
    } else {
        console.error('Customer Analytics not available');
    }
};

window.testCustomerAnalyticsFilters = () => {
    console.log('=== Testing Customer Analytics Filters ===');

    if (!window.customerAnalytics || !window.customerAnalytics.isInitialized) {
        console.error('Customer Analytics not initialized');
        return;
    }

    // Test filter changes
    const filters = ['alert', 'mmk-alert', 'usd-alert'];
    const sorts = ['alert-priority', 'mmk-desc', 'usd-desc', 'total-desc'];

    filters.forEach(filter => {
        console.log(`Testing filter: ${filter}`);
        if (window.customerAnalytics.elements.customerAlertFilter) {
            window.customerAnalytics.elements.customerAlertFilter.value = filter;
            window.customerAnalytics.updateTable();
        }
    });

    console.log('=== Filter Test Complete ===');
};

window.forceCustomerAnalyticsRefresh = () => {
    console.log('=== Forcing Customer Analytics Refresh ===');

    if (window.customerAnalytics) {
        window.customerAnalytics.refreshAnalytics();
    } else {
        console.error('Customer Analytics not available');
    }
};

window.testUSDSorting = () => {
    console.log('=== Testing USD Sorting from Global Function ===');

    if (window.customerAnalytics) {
        window.customerAnalytics.testUSDSorting();
    } else {
        console.error('Customer Analytics not available');
    }
};
