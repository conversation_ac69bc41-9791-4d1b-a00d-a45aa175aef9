/**
 * TTR Export Module
 * Handles export functionality for regulatory reporting
 */

class TTRExport {
    constructor() {
        this.exportFormats = ['csv', 'excel', 'pdf'];
    }

    // Export alerts to CSV format
    exportAlertsToCSV(alerts, filename = null) {
        if (!alerts || alerts.length === 0) {
            throw new Error('No alerts to export');
        }

        const csvData = this.convertAlertsToCSV(alerts);
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });

        const finalFilename = filename || `TTR_Alerts_${this.getTimestamp()}.csv`;
        this.downloadBlob(blob, finalFilename);

        return {
            success: true,
            filename: finalFilename,
            recordCount: alerts.length,
            format: 'CSV'
        };
    }

    // Export alerts to Excel format
    exportAlertsToExcel(alerts, filename = null) {
        if (!alerts || alerts.length === 0) {
            throw new Error('No alerts to export');
        }

        // Check if XLSX library is available
        if (typeof XLSX === 'undefined') {
            throw new Error('XLSX library not available. Cannot export to Excel format.');
        }

        const workbook = this.createExcelWorkbook(alerts);
        const finalFilename = filename || `TTR_Alerts_${this.getTimestamp()}.xlsx`;

        XLSX.writeFile(workbook, finalFilename);

        return {
            success: true,
            filename: finalFilename,
            recordCount: alerts.length,
            format: 'Excel'
        };
    }

    // Export alerts to PDF format (basic implementation)
    exportAlertsToPDF(alerts, filename = null) {
        if (!alerts || alerts.length === 0) {
            throw new Error('No alerts to export');
        }

        // For now, we'll create a simple HTML report and let the browser handle PDF conversion
        const htmlContent = this.createHTMLReport(alerts);
        const finalFilename = filename || `TTR_Alerts_${this.getTimestamp()}.html`;

        const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8;' });
        this.downloadBlob(blob, finalFilename);

        // Also open in new window for PDF printing
        const newWindow = window.open();
        newWindow.document.write(htmlContent);
        newWindow.document.close();

        return {
            success: true,
            filename: finalFilename,
            recordCount: alerts.length,
            format: 'HTML/PDF'
        };
    }

    // Convert alerts to CSV format
    convertAlertsToCSV(alerts) {
        const headers = [
            'Alert ID',
            'Timestamp',
            'Priority',
            'Risk Score',
            'Type',
            'Subtype',
            'Customer ID',
            'Customer Name',
            'Amount',
            'Date',
            'Description',
            'Status',
            'File ID',
            'Transaction Count',
            'Additional Details'
        ];

        const rows = alerts.map(alert => [
            alert.id || '',
            alert.timestamp || '',
            alert.priority || '',
            alert.riskScore || 0,
            alert.type || '',
            alert.subtype || '',
            alert.customerId || '',
            alert.customerName || '',
            alert.amount || 0,
            alert.date || '',
            alert.description || '',
            alert.status || '',
            alert.fileId || '',
            (alert.metadata && alert.metadata.transactionCount) || '',
            this.formatMetadataForCSV(alert.metadata)
        ]);

        // Combine headers and rows
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => this.escapeCSVField(field)).join(','))
            .join('\n');

        return csvContent;
    }

    // Create Excel workbook
    createExcelWorkbook(alerts) {
        const workbook = XLSX.utils.book_new();

        // Main alerts sheet
        const alertsData = alerts.map(alert => ({
            'Alert ID': alert.id || '',
            'Timestamp': alert.timestamp || '',
            'Priority': alert.priority || '',
            'Risk Score': alert.riskScore || 0,
            'Type': alert.type || '',
            'Subtype': alert.subtype || '',
            'Customer ID': alert.customerId || '',
            'Customer Name': alert.customerName || '',
            'Amount': alert.amount || 0,
            'Date': alert.date || '',
            'Description': alert.description || '',
            'Status': alert.status || '',
            'File ID': alert.fileId || ''
        }));

        const alertsSheet = XLSX.utils.json_to_sheet(alertsData);
        XLSX.utils.book_append_sheet(workbook, alertsSheet, 'TTR Alerts');

        // Summary sheet
        const summary = this.generateAlertsSummary(alerts);
        const summarySheet = XLSX.utils.json_to_sheet(summary);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

        return workbook;
    }

    // Create HTML report for PDF export
    createHTMLReport(alerts) {
        const summary = this.generateAlertsSummary(alerts);
        const timestamp = new Date().toLocaleString();

        return `
<!DOCTYPE html>
<html>
<head>
    <title>TTR Monitoring Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { background-color: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .summary-item { text-align: center; }
        .summary-item h3 { margin: 0; color: #333; }
        .summary-item p { margin: 5px 0; font-size: 1.2em; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .priority-high { color: #dc3545; font-weight: bold; }
        .priority-medium { color: #ffc107; font-weight: bold; }
        .priority-low { color: #17a2b8; font-weight: bold; }
        .footer { margin-top: 30px; text-align: center; font-size: 0.9em; color: #666; }
        @media print {
            body { margin: 0; }
            .header { page-break-after: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Threshold Transaction Reporting (TTR) Monitoring Report</h1>
        <p>Generated on: ${timestamp}</p>
        <p>Total Alerts: ${alerts.length}</p>
    </div>

    <div class="summary">
        <h2>Summary Statistics</h2>
        <div class="summary-grid">
            ${summary.map(item => `
                <div class="summary-item">
                    <h3>${item.Metric}</h3>
                    <p>${item.Value}</p>
                </div>
            `).join('')}
        </div>
    </div>

    <h2>Alert Details</h2>
    <table>
        <thead>
            <tr>
                <th>Alert ID</th>
                <th>Priority</th>
                <th>Type</th>
                <th>Customer</th>
                <th>Amount</th>
                <th>Date</th>
                <th>Score</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            ${alerts.map(alert => `
                <tr>
                    <td>${alert.id || ''}</td>
                    <td class="priority-${alert.priority || 'low'}">${(alert.priority || '').toUpperCase()}</td>
                    <td>${alert.type || ''}</td>
                    <td>${alert.customerName || ''}</td>
                    <td>${this.formatCurrency(alert.amount || 0)}</td>
                    <td>${alert.date || ''}</td>
                    <td>${alert.riskScore || 0}</td>
                    <td>${alert.description || ''}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="footer">
        <p>This report was generated by the Financial Transaction Dashboard TTR Monitoring System</p>
        <p>Report contains ${alerts.length} alert(s) for regulatory compliance review</p>
    </div>
</body>
</html>`;
    }

    // Generate summary statistics for alerts
    generateAlertsSummary(alerts) {
        const priorityCounts = {
            high: alerts.filter(a => a.priority === 'high').length,
            medium: alerts.filter(a => a.priority === 'medium').length,
            low: alerts.filter(a => a.priority === 'low').length
        };

        const typeCounts = {};
        alerts.forEach(alert => {
            const type = alert.type || 'unknown';
            typeCounts[type] = (typeCounts[type] || 0) + 1;
        });

        const totalAmount = alerts.reduce((sum, alert) => sum + (alert.amount || 0), 0);
        const averageRiskScore = alerts.length > 0 ?
            Math.round(alerts.reduce((sum, alert) => sum + (alert.riskScore || 0), 0) / alerts.length) : 0;

        return [
            { Metric: 'Total Alerts', Value: alerts.length },
            { Metric: 'High Priority', Value: priorityCounts.high },
            { Metric: 'Medium Priority', Value: priorityCounts.medium },
            { Metric: 'Low Priority', Value: priorityCounts.low },
            { Metric: 'Total Amount Flagged', Value: this.formatCurrency(totalAmount) },
            { Metric: 'Average Risk Score', Value: averageRiskScore },
            { Metric: 'Threshold Alerts', Value: typeCounts.threshold || 0 },
            { Metric: 'Structuring Alerts', Value: typeCounts.structuring || 0 },
            { Metric: 'Velocity Alerts', Value: typeCounts.velocity || 0 },
            { Metric: 'Pattern Alerts', Value: typeCounts.pattern || 0 },
            { Metric: 'Behavioral Alerts', Value: typeCounts.behavioral || 0 }
        ];
    }

    // Helper methods
    escapeCSVField(field) {
        if (field === null || field === undefined) return '';
        const stringField = String(field);
        if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
            return `"${stringField.replace(/"/g, '""')}"`;
        }
        return stringField;
    }

    formatMetadataForCSV(metadata) {
        if (!metadata) return '';
        return Object.entries(metadata)
            .map(([key, value]) => `${key}: ${value}`)
            .join('; ');
    }

    formatCurrency(amount, currency = 'MMK') {
        const supportedCurrencies = ['USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

        if (currency === 'MMK') {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount) + ' MMK';
        } else if (supportedCurrencies.includes(currency)) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency,
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        } else {
            // Fallback for unsupported currencies
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount) + ` ${currency}`;
        }
    }

    getTimestamp() {
        return new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    }

    downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }
}

// Export for use in other modules
window.TTRExport = TTRExport;
