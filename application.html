<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Transaction Dashboard V2.5.7</title>
    <link rel="stylesheet" href="css/styles.css">

    <!-- External libraries -->
    <script src="libs/xlsx.full.min.js" onerror="console.warn('XLSX library failed to load - Excel export will be disabled')"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Financial Transaction Dashboard V2.5.7</h1>
            <nav class="main-navigation">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#dashboard" class="nav-link active" data-page="dashboard" role="tab" aria-selected="true" aria-controls="dashboard-page">
                            <span class="nav-icon" aria-hidden="true">📊</span>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#ttr-monitoring" class="nav-link" data-page="ttr-monitoring" role="tab" aria-selected="false" aria-controls="ttr-monitoring-page">
                            <span class="nav-icon" aria-hidden="true">🚨</span>
                            <span class="nav-text">Advanced TTR Monitoring</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </header>

        <!-- Confirmation Dialog -->
        <div id="confirmationDialog" class="confirmation-dialog">
            <div class="confirmation-content">
                <h3 class="confirmation-title">Confirm Action</h3>
                <p id="confirmationMessage" class="confirmation-message">Are you sure you want to perform this action?</p>
                <div class="confirmation-buttons">
                    <button id="cancelBtn" class="btn cancel-btn">Cancel</button>
                    <button id="confirmBtn" class="btn confirm-btn">Confirm</button>
                </div>
            </div>
        </div>

        <main>
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page-content active">
                <!-- File Upload Section -->
                <section class="upload-section">
                    <div class="upload-main-container">
                        <div class="upload-container" id="dropArea" role="button" tabindex="0" aria-label="Upload CSV files by dragging and dropping or clicking to browse">
                            <div class="upload-icon" aria-hidden="true">
                                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17 8 12 3 7 8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                </svg>
                            </div>
                            <h3>Upload CSV Files</h3>
                            <p>Drag & drop files here or click to browse</p>
                            <input type="file" id="fileInput" multiple accept=".csv" style="display: none;" aria-label="Select CSV files to upload">
                            <p class="file-info">Multiple files supported (up to 3GB total)</p>
                        </div>
                        <div class="file-list" id="fileList">
                            <h3>Selected Files</h3>
                            <ul id="uploadedFilesList"></ul>
                            <div class="submit-container" id="submitContainer">
                                <button id="submitFiles" class="btn submit-btn">Process Files</button>
                            </div>
                        </div>
                    </div>
                    <div class="upload-progress" id="uploadProgress">
                        <div class="progress-container">
                            <div class="progress-bar" id="progressBar"></div>
                        </div>
                        <p id="progressText">0% - Processing files...</p>
                    </div>
                </section>



            <!-- Summary Metrics Section -->
            <section class="metrics-section">
                <div class="metrics-container">
                    <h3>Summary Metrics</h3>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <h4>Total Transactions</h4>
                            <p id="totalTransactions">0</p>
                        </div>
                        <div class="metric-card">
                            <h4>Total Amount MMK</h4>
                            <p id="totalAmountMMK">0.00 MMK</p>
                        </div>
                        <div class="metric-card">
                            <h4>Total Amount USD</h4>
                            <p id="totalAmountUSD">0.00 USD</p>
                        </div>
                        <div class="metric-card">
                            <h4>Files Processed</h4>
                            <p id="filesProcessed">0</p>
                        </div>
                        <div class="metric-card performance-card">
                            <h4>Memory Usage</h4>
                            <p id="memoryUsage" class="memory-usage normal">Loading...</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Transaction Breakdown Section -->
            <section class="breakdown-section">
                <div class="breakdown-container">
                    <div class="breakdown-header-container">
                        <h3>Transaction Breakdown</h3>
                        <button id="exportTransactionBreakdownBtn" class="btn export-btn transaction-breakdown-export-btn" style="display: none;">
                            <span class="export-icon"></span>
                            Export to Excel
                        </button>
                    </div>
                    <div class="breakdown-grid">
                        <!-- HOC Transactions -->
                        <div class="breakdown-card">
                            <h4>HOC Transactions</h4>
                            <div class="breakdown-details">
                                <p>Total Count: <span id="hocCount">0</span></p>
                                <p>Unique Serial Count: <span id="hocUniqueSerialCount">0</span></p>
                                <p>Total Amount MMK: <span id="hocAmountMMK">0.00 MMK</span></p>
                                <p>Total Amount USD: <span id="hocAmountUSD">0.00 USD</span></p>

                                <div class="credit-debit-breakdown">
                                    <div class="credit-breakdown">
                                        <h5>Credit</h5>
                                        <p>Count: <span id="hocCreditCount">0</span></p>
                                        <p>Amount MMK: <span id="hocCreditAmountMMK">0.00 MMK</span></p>
                                        <p>Amount USD: <span id="hocCreditAmountUSD">0.00 USD</span></p>
                                        <p>Amount CNY: <span id="hocCreditAmountCNY">0.00 CNY</span></p>
                                        <p>Amount EUR: <span id="hocCreditAmountEUR">0.00 EUR</span></p>
                                        <p>Amount INR: <span id="hocCreditAmountINR">0.00 INR</span></p>
                                        <p>Amount JPY: <span id="hocCreditAmountJPY">0.00 JPY</span></p>
                                        <p>Amount SGD: <span id="hocCreditAmountSGD">0.00 SGD</span></p>
                                        <p>Amount THB: <span id="hocCreditAmountTHB">0.00 THB</span></p>
                                    </div>
                                    <div class="debit-breakdown">
                                        <h5>Debit</h5>
                                        <p>Count: <span id="hocDebitCount">0</span></p>
                                        <p>Amount MMK: <span id="hocDebitAmountMMK">0.00 MMK</span></p>
                                        <p>Amount USD: <span id="hocDebitAmountUSD">0.00 USD</span></p>
                                        <p>Amount CNY: <span id="hocDebitAmountCNY">0.00 CNY</span></p>
                                        <p>Amount EUR: <span id="hocDebitAmountEUR">0.00 EUR</span></p>
                                        <p>Amount INR: <span id="hocDebitAmountINR">0.00 INR</span></p>
                                        <p>Amount JPY: <span id="hocDebitAmountJPY">0.00 JPY</span></p>
                                        <p>Amount SGD: <span id="hocDebitAmountSGD">0.00 SGD</span></p>
                                        <p>Amount THB: <span id="hocDebitAmountTHB">0.00 THB</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- IBD Transactions -->
                        <div class="breakdown-card">
                            <h4>IBD Transactions</h4>
                            <div class="breakdown-details">
                                <p>Total Count: <span id="ibdCount">0</span></p>
                                <p>Unique Serial Count: <span id="ibdUniqueSerialCount">0</span></p>
                                <p>Total Amount MMK: <span id="ibdAmountMMK">0.00 MMK</span></p>
                                <p>Total Amount USD: <span id="ibdAmountUSD">0.00 USD</span></p>

                                <div class="credit-debit-breakdown">
                                    <div class="credit-breakdown">
                                        <h5>Credit</h5>
                                        <p>Count: <span id="ibdCreditCount">0</span></p>
                                        <p>Amount MMK: <span id="ibdCreditAmountMMK">0.00 MMK</span></p>
                                        <p>Amount USD: <span id="ibdCreditAmountUSD">0.00 USD</span></p>
                                        <p>Amount CNY: <span id="ibdCreditAmountCNY">0.00 CNY</span></p>
                                        <p>Amount EUR: <span id="ibdCreditAmountEUR">0.00 EUR</span></p>
                                        <p>Amount INR: <span id="ibdCreditAmountINR">0.00 INR</span></p>
                                        <p>Amount JPY: <span id="ibdCreditAmountJPY">0.00 JPY</span></p>
                                        <p>Amount SGD: <span id="ibdCreditAmountSGD">0.00 SGD</span></p>
                                        <p>Amount THB: <span id="ibdCreditAmountTHB">0.00 THB</span></p>
                                    </div>
                                    <div class="debit-breakdown">
                                        <h5>Debit</h5>
                                        <p>Count: <span id="ibdDebitCount">0</span></p>
                                        <p>Amount MMK: <span id="ibdDebitAmountMMK">0.00 MMK</span></p>
                                        <p>Amount USD: <span id="ibdDebitAmountUSD">0.00 USD</span></p>
                                        <p>Amount CNY: <span id="ibdDebitAmountCNY">0.00 CNY</span></p>
                                        <p>Amount EUR: <span id="ibdDebitAmountEUR">0.00 EUR</span></p>
                                        <p>Amount INR: <span id="ibdDebitAmountINR">0.00 INR</span></p>
                                        <p>Amount JPY: <span id="ibdDebitAmountJPY">0.00 JPY</span></p>
                                        <p>Amount SGD: <span id="ibdDebitAmountSGD">0.00 SGD</span></p>
                                        <p>Amount THB: <span id="ibdDebitAmountTHB">0.00 THB</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- WU Transactions -->
                        <div class="breakdown-card">
                            <h4>WU Transactions</h4>
                            <div class="breakdown-details">
                                <p>Total Count: <span id="wuCount">0</span></p>
                                <p>Unique Serial Count: <span id="wuUniqueSerialCount">0</span></p>
                                <p>Total Amount MMK: <span id="wuAmountMMK">0.00 MMK</span></p>
                                <p>Total Amount USD: <span id="wuAmountUSD">0.00 USD</span></p>

                                <div class="credit-debit-breakdown">
                                    <div class="credit-breakdown">
                                        <h5>Credit</h5>
                                        <p>Count: <span id="wuCreditCount">0</span></p>
                                        <p>Amount MMK: <span id="wuCreditAmountMMK">0.00 MMK</span></p>
                                        <p>Amount USD: <span id="wuCreditAmountUSD">0.00 USD</span></p>
                                        <p>Amount CNY: <span id="wuCreditAmountCNY">0.00 CNY</span></p>
                                        <p>Amount EUR: <span id="wuCreditAmountEUR">0.00 EUR</span></p>
                                        <p>Amount INR: <span id="wuCreditAmountINR">0.00 INR</span></p>
                                        <p>Amount JPY: <span id="wuCreditAmountJPY">0.00 JPY</span></p>
                                        <p>Amount SGD: <span id="wuCreditAmountSGD">0.00 SGD</span></p>
                                        <p>Amount THB: <span id="wuCreditAmountTHB">0.00 THB</span></p>
                                    </div>
                                    <div class="debit-breakdown">
                                        <h5>Debit</h5>
                                        <p>Count: <span id="wuDebitCount">0</span></p>
                                        <p>Amount MMK: <span id="wuDebitAmountMMK">0.00 MMK</span></p>
                                        <p>Amount USD: <span id="wuDebitAmountUSD">0.00 USD</span></p>
                                        <p>Amount CNY: <span id="wuDebitAmountCNY">0.00 CNY</span></p>
                                        <p>Amount EUR: <span id="wuDebitAmountEUR">0.00 EUR</span></p>
                                        <p>Amount INR: <span id="wuDebitAmountINR">0.00 INR</span></p>
                                        <p>Amount JPY: <span id="wuDebitAmountJPY">0.00 JPY</span></p>
                                        <p>Amount SGD: <span id="wuDebitAmountSGD">0.00 SGD</span></p>
                                        <p>Amount THB: <span id="wuDebitAmountTHB">0.00 THB</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Currency Breakdown Section -->
            <section class="currency-breakdown-section">
                <div class="currency-breakdown-container">
                    <h3>Currency Breakdown</h3>
                    <div class="currency-breakdown-table-container">
                        <div class="table-responsive">
                            <table id="currencyBreakdownTable" class="currency-breakdown-table">
                                <thead>
                                    <tr>
                                        <th>Currency Type</th>
                                        <th>Credit</th>
                                        <th>Debit</th>
                                        <th>Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody id="currencyBreakdownTableBody">
                                    <!-- Currency breakdown data will be populated dynamically -->
                                    <tr class="empty-table-message">
                                        <td colspan="4">No data available. Please upload CSV files to view currency breakdown.</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- TTR Summary Report Section -->
            <section class="ttr-summary-section">
                <div class="ttr-summary-container">
                    <div class="ttr-summary-header">
                        <h3>TTR Summary Report</h3>
                        <div class="ttr-file-management">
                            <button id="exportTTRSummaryBtn" class="btn export-btn ttr-summary-export-btn" style="display: none;">
                                <span class="export-icon"></span>
                                Export to Excel
                            </button>
                            <button id="ttrManageFilesBtn" class="btn btn-secondary" style="display: none;">
                                📁 Manage Files
                            </button>
                        </div>
                    </div>

                    <!-- TTR File Management Panel -->
                    <div class="ttr-file-panel" id="ttrFilePanel" style="display: none;">
                        <div class="ttr-file-panel-header">
                            <h4>TTR Report File Management</h4>
                            <button id="ttrFilePanelClose" class="btn btn-secondary">✕ Close</button>
                        </div>
                        <div class="ttr-file-list">
                            <div class="ttr-file-list-header">
                                <span>Available Files</span>
                                <span>Status</span>
                                <span>Actions</span>
                            </div>
                            <div id="ttrFileListContainer" class="ttr-file-list-container">
                                <!-- TTR file list will be populated dynamically -->
                                <div class="ttr-empty-file-message">
                                    No files available. Please upload CSV files first.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ttr-summary-table-container">
                        <div class="table-responsive">
                            <table id="ttrSummaryTable" class="ttr-summary-table">
                                <thead>
                                    <tr>
                                        <th>Report Date</th>
                                        <th>HOC Serial</th>
                                        <th>HOC Count</th>
                                        <th>IBD Serial</th>
                                        <th>IBD Count</th>
                                        <th>WU Serial</th>
                                        <th>WU Count</th>
                                        <th>1B+ Count</th>
                                        <th>Grand Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="ttrSummaryTableBody">
                                    <!-- TTR summary data will be populated dynamically -->
                                    <tr class="empty-table-message">
                                        <td colspan="10">No data available. Please upload CSV files to view TTR summary report.</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>





            <!-- High-Value Transaction Analytics Section -->
            <section class="high-value-analytics-section">
                <div class="high-value-analytics-container">
                    <div class="high-value-header">
                        <h3>High-Value Transaction Analytics</h3>
                        <div class="high-value-header-buttons">
                            <button id="exportHighValueBtn" class="btn export-btn high-value-export-btn">
                                <span class="export-icon"></span>
                                Export to Excel
                            </button>
                            <button id="testHighValueAlertsBtn" class="btn btn-secondary" style="margin-right: 10px;" title="Test high-value transaction alerts">
                                🔔 Test Alerts
                            </button>
                        </div>
                    </div>
                    <div class="high-value-metrics-grid">
                        <div class="metric-card high-value-card">
                            <h4>High-Value Transactions (≥1B MMK)</h4>
                            <p id="highValueTransactionCount">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Total transactions exceeding or equal to 1,000,000,000 MMK</span>
                            </div>
                        </div>
                        <div class="metric-card high-value-card-usd">
                            <h4>High-Value Transactions (≥10,000 USD)</h4>
                            <p id="highValueTransactionCountUSD">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Total transactions exceeding or equal to 10,000 USD</span>
                            </div>
                        </div>
                        <div class="metric-card high-value-files-processed">
                            <h4>Files Processed</h4>
                            <p id="highValueFilesProcessed">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Files containing high-value transactions processed</span>
                            </div>
                        </div>
                        <div class="metric-card high-value-files-removed">
                            <h4>Files Removed</h4>
                            <p id="highValueFilesRemoved">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Files with high-value transactions removed from system</span>
                            </div>
                        </div>
                        <div class="metric-card high-value-files-active">
                            <h4>Active Files</h4>
                            <p id="highValueFilesActive">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Currently active files with high-value transactions</span>
                            </div>
                        </div>
                    </div>

                    <!-- Combined Date-Level Breakdown Section for MMK and USD -->
                    <div class="high-value-breakdown-combined" id="highValueBreakdownCombined" style="display: none;">
                        <div class="breakdown-header">
                            <h4>Date Breakdown (≥1B MMK and ≥10K USD)</h4>
                            <button class="breakdown-toggle" id="breakdownToggleCombined" aria-expanded="false">
                                <span class="toggle-icon">▼</span>
                                <span class="toggle-text">Show Details</span>
                            </button>
                        </div>
                        <div class="breakdown-content" id="breakdownContentCombined" style="display: none;">
                            <div class="breakdown-list" id="highValueFileListCombined">
                                <!-- Combined date breakdown items will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- High-Value Transaction Details Table -->
                    <div class="high-value-transaction-details" id="highValueTransactionDetails" style="display: none;">
                        <div class="transaction-details-header">
                            <h4 id="transactionDetailsTitle">Transaction Details</h4>
                            <div class="transaction-details-controls">
                                <div class="filter-group">
                                    <label for="currencyFilter">Currency</label>
                                    <select id="currencyFilter">
                                        <option value="all">All Currencies</option>
                                        <option value="MMK">MMK</option>
                                        <option value="USD">USD</option>
                                        <option value="SGD">SGD</option>
                                        <option value="EUR">EUR</option>
                                        <option value="JPY">JPY</option>
                                        <option value="CNY">CNY</option>
                                        <option value="THB">THB</option>
                                        <option value="INR">INR</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="sortBy">Sort By</label>
                                    <select id="sortBy">
                                        <option value="amount-desc">Amount (High to Low)</option>
                                        <option value="amount-asc">Amount (Low to High)</option>
                                        <option value="date-desc">Date (Newest First)</option>
                                        <option value="date-asc">Date (Oldest First)</option>
                                        <option value="customer">CONDUCTOR</option>
                                    </select>
                                </div>
                                <button id="clearTransactionDetails" class="btn btn-secondary">
                                    Clear Table
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="highValueTransactionTable" class="transaction-details-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>CONDUCTOR</th>
                                        <th>CONDUCTORID</th>
                                        <th>Counterparty</th>
                                        <th>COUNTERPARTYID</th>
                                        <th>Amount</th>
                                        <th>ROLE</th>
                                        <th>Currency</th>
                                        <th>Report Type</th>
                                        <th>File Source</th>
                                    </tr>
                                </thead>
                                <tbody id="highValueTransactionTableBody">
                                    <tr class="empty-table-message">
                                        <td colspan="10">Click on a high-value transaction card above to view detailed transaction information.</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="transaction-summary" id="transactionSummary" style="display: none;">
                            <div class="summary-stats">
                                <div class="summary-stat">
                                    <span class="stat-label">Total Transactions:</span>
                                    <span class="stat-value" id="summaryTotalCount">0</span>
                                </div>
                                <div class="summary-stat">
                                    <span class="stat-label">Total Amount:</span>
                                    <span class="stat-value" id="summaryTotalAmount">0</span>
                                </div>
                                <div class="summary-stat">
                                    <span class="stat-label">Average Amount:</span>
                                    <span class="stat-value" id="summaryAvgAmount">0</span>
                                </div>
                                <div class="summary-stat">
                                    <span class="stat-label">Unique Customers:</span>
                                    <span class="stat-value" id="summaryUniqueCustomers">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- High-Value Customer Analytics Section -->
            <section class="customer-analytics-section">
                <div class="customer-analytics-container">
                    <div class="customer-analytics-header">
                        <h3>High-Value Customer Analytics</h3>
                        <div class="customer-analytics-header-buttons">
                            <button id="exportCustomerAnalyticsBtn" class="btn export-btn customer-analytics-export-btn">
                                <span class="export-icon"></span>
                                Export to Excel
                            </button>
                            <button id="testCustomerAlertsBtn" class="btn btn-secondary" style="margin-right: 10px;" title="Test customer alert system">
                                🔔 Test Customer Alerts
                            </button>
                        </div>
                    </div>

                    <!-- Alert Banner -->
                    <div class="customer-alert-banner" id="customerAlertBanner" style="display: none;">
                        <div class="alert-content">
                            <div class="alert-icon">⚠️</div>
                            <div class="alert-text">
                                <strong>High-Value Customer Alert!</strong>
                                <span id="customerAlertMessage">Customers with daily transactions exceeding thresholds detected.</span>
                            </div>
                            <button class="alert-close" id="closeCustomerAlert">&times;</button>
                        </div>
                    </div>

                    <div class="customer-analytics-metrics-grid">
                        <div class="metric-card customer-alert-card">
                            <h4>Active Customer Alerts</h4>
                            <p id="activeCustomerAlerts">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Customers exceeding daily thresholds</span>
                            </div>
                        </div>
                        <div class="metric-card customer-mmk-card">
                            <h4>MMK Alert Customers</h4>
                            <p id="mmkAlertCustomers">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Daily total ≥1B MMK</span>
                            </div>
                        </div>
                        <div class="metric-card customer-usd-card">
                            <h4>USD Alert Customers</h4>
                            <p id="usdAlertCustomers">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Daily total ≥10K USD</span>
                            </div>
                        </div>
                        <div class="metric-card customer-total-card">
                            <h4>Total Monitored Customers</h4>
                            <p id="totalMonitoredCustomers">0</p>
                            <div class="metric-details">
                                <span class="metric-subtitle">Unique customers with transactions</span>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Analytics Table -->
                    <div class="customer-analytics-details" id="customerAnalyticsDetails">
                        <div class="customer-details-header">
                            <div class="header-content">
                                <h4>Customer Transaction Summary</h4>
                                <p class="header-subtitle">Monitor high-value customer transactions and alerts</p>
                            </div>
                            <div class="customer-details-controls">
                                <div class="filters-container">
                                    <div class="filter-group modern">
                                        <label for="customerAlertFilter">
                                            <i class="filter-icon">🔍</i>
                                            Alert Type
                                        </label>
                                        <select id="customerAlertFilter" class="modern-select">
                                            <option value="alert">All Alert Customers</option>
                                            <option value="mmk-alert">MMK Alerts (≥1B MMK)</option>
                                            <option value="usd-alert">USD Alerts (≥10K USD)</option>
                                        </select>
                                    </div>
                                    <div class="filter-group modern">
                                        <label for="customerSortBy">
                                            <i class="filter-icon">📊</i>
                                            Sort By
                                        </label>
                                        <select id="customerSortBy" class="modern-select">
                                            <option value="alert-priority">Alert Priority</option>
                                            <option value="mmk-desc">MMK Amount (High to Low)</option>
                                            <option value="usd-desc">USD Amount (High to Low)</option>
                                            <option value="total-desc">Transaction Count</option>
                                            <option value="date-desc">Date (Newest First)</option>
                                            <option value="customer-name">Customer Name</option>
                                        </select>
                                    </div>
                                    <div class="filter-group modern">
                                        <label for="customerDateFilter">
                                            <i class="filter-icon">📅</i>
                                            Date Range
                                        </label>
                                        <select id="customerDateFilter" class="modern-select">
                                            <option value="all">All Dates</option>
                                            <option value="today">Today</option>
                                            <option value="week">This Week</option>
                                            <option value="month">This Month</option>
                                        </select>
                                    </div>
                                    <div class="filter-group modern">
                                        <label for="customerSourceFileFilter">
                                            <i class="filter-icon">📁</i>
                                            Source File
                                        </label>
                                        <select id="customerSourceFileFilter" class="modern-select" multiple>
                                            <option value="all">All Source Files</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="action-buttons">
                                    <button id="refreshCustomerAnalytics" class="btn-modern refresh">
                                        <i class="btn-icon">🔄</i>
                                        <span>Refresh</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="customerAnalyticsTable" class="customer-analytics-table">
                                <thead>
                                    <tr>
                                        <th class="customer-name-header">Customer Name</th>
                                        <th class="customer-id-header">Customer ID</th>
                                        <th class="date-header">Transaction Date</th>
                                        <th class="mmk-amount-header">Total MMK Amount</th>
                                        <th class="usd-amount-header">Total USD Amount</th>
                                        <th class="count-header">Transaction Count</th>
                                        <th class="alert-status-header">Alert Status</th>
                                        <th class="actions-header">View Details</th>
                                    </tr>
                                </thead>
                                <tbody id="customerAnalyticsTableBody">
                                    <tr class="empty-table-message">
                                        <td colspan="8">No high-value customer alerts detected. Upload CSV files with customers exceeding daily thresholds (≥1B MMK or ≥10K USD).</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="customer-analytics-summary" id="customerAnalyticsSummary" style="display: none;">
                            <div class="summary-stats">
                                <div class="summary-stat">
                                    <span class="stat-label">Total Customers:</span>
                                    <span class="stat-value" id="summaryTotalCustomers">0</span>
                                </div>
                                <div class="summary-stat">
                                    <span class="stat-label">Alert Customers:</span>
                                    <span class="stat-value" id="summaryAlertCustomers">0</span>
                                </div>
                                <div class="summary-stat">
                                    <span class="stat-label">Total MMK Volume:</span>
                                    <span class="stat-value" id="summaryTotalMMK">0 MMK</span>
                                </div>
                                <div class="summary-stat">
                                    <span class="stat-label">Total USD Volume:</span>
                                    <span class="stat-value" id="summaryTotalUSD">0 USD</span>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </section>
            </div>

            <!-- TTR Monitoring Page -->
            <div id="ttr-monitoring-page" class="page-content">
                <!-- TTR Dashboard Header -->
                <section class="ttr-header-section">
                    <div class="ttr-header-container">
                        <h2>Advanced Threshold Transaction Reporting (TTR) Monitoring</h2>
                        <p class="ttr-subtitle">Anti-Money Laundering Compliance Dashboard</p>
                        <div class="ttr-status-indicators">
                            <div class="status-indicator">
                                <span class="status-dot active"></span>
                                <span class="status-text">System Active</span>
                            </div>
                            <div class="status-indicator">
                                <span class="status-dot" id="alertStatus"></span>
                                <span class="status-text" id="alertStatusText">No Active Alerts</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- TTR Summary Statistics -->
                <section class="ttr-summary-section">
                    <div class="ttr-summary-container">
                        <h3>Real-Time Monitoring Summary (Consolidated Alerts)</h3>
                        <div class="ttr-metrics-grid">
                            <div class="ttr-metric-card priority-high">
                                <h4>High Priority Alerts</h4>
                                <p id="highPriorityAlerts">0</p>
                                <div class="metric-details">
                                    <span class="metric-subtitle">Score: 80-100</span>
                                </div>
                            </div>
                            <div class="ttr-metric-card priority-medium">
                                <h4>Medium Priority Alerts</h4>
                                <p id="mediumPriorityAlerts">0</p>
                                <div class="metric-details">
                                    <span class="metric-subtitle">Score: 50-79</span>
                                </div>
                            </div>
                            <div class="ttr-metric-card priority-low">
                                <h4>Low Priority Alerts</h4>
                                <p id="lowPriorityAlerts">0</p>
                                <div class="metric-details">
                                    <span class="metric-subtitle">Score: 20-49</span>
                                </div>
                            </div>
                            <div class="ttr-metric-card threshold-breaches">
                                <h4>Threshold Breaches</h4>
                                <p id="thresholdBreaches">0</p>
                                <div class="metric-details">
                                    <span class="metric-subtitle">Single: ≥1B MMK | Daily: ≥5B MMK</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- TTR Alerts Table -->
                <section class="ttr-alerts-section">
                    <div class="ttr-alerts-container">
                        <div class="alerts-header">
                            <h3>Active Alerts</h3>
                            <div class="alerts-controls">
                                <div class="filter-group">
                                    <label for="alertPriorityFilter">Priority</label>
                                    <select id="alertPriorityFilter">
                                        <option value="all">All Priorities</option>
                                        <option value="high">High (80-100)</option>
                                        <option value="medium">Medium (50-79)</option>
                                        <option value="low">Low (20-49)</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="alertTypeFilter">Alert Type</label>
                                    <select id="alertTypeFilter">
                                        <option value="all">All Types</option>
                                        <option value="threshold">Threshold Breach</option>
                                        <option value="structuring">Structuring</option>
                                        <option value="velocity">Velocity</option>
                                        <option value="pattern">Suspicious Pattern</option>
                                        <option value="behavioral">Behavioral Anomaly</option>
                                    </select>
                                </div>
                                <button id="exportAlertsBtn" class="btn export-btn">
                                    <span class="export-icon"></span>
                                    Export Alerts
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="ttrAlertsTable" class="data-table">
                                <thead>
                                    <tr>
                                        <th>Alert ID</th>
                                        <th>Priority</th>
                                        <th>Type</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Score</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="ttrAlertsTableBody">
                                    <tr class="empty-table-message">
                                        <td colspan="8">No alerts detected. Upload CSV files to begin monitoring.</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- TTR Configuration Panel -->
                <section class="ttr-config-section">
                    <div class="ttr-config-container">
                        <div class="config-header">
                            <h3>Monitoring Configuration</h3>
                            <button class="config-toggle" id="configToggle" aria-expanded="false">
                                <span class="toggle-icon">▼</span>
                                <span class="toggle-text">Show Configuration</span>
                            </button>
                        </div>
                        <div class="config-content" id="configContent" style="display: none;">
                            <div class="config-grid">
                                <div class="config-group">
                                    <h4>Threshold Settings</h4>
                                    <div class="config-item">
                                        <label for="singleThreshold">Single Transaction Threshold (MMK)</label>
                                        <input type="number" id="singleThreshold" value="1000000000" min="100000000" step="100000000">
                                    </div>
                                    <div class="config-item">
                                        <label for="dailyThreshold">Daily Cumulative Threshold (MMK)</label>
                                        <input type="number" id="dailyThreshold" value="5000000000" min="1000000000" step="500000000">
                                    </div>
                                </div>
                                <div class="config-group">
                                    <h4>Structuring Detection</h4>
                                    <div class="config-item">
                                        <label for="structuringWindow">Time Window (hours)</label>
                                        <input type="number" id="structuringWindow" value="24" min="1" max="168">
                                    </div>
                                    <div class="config-item">
                                        <label for="structuringThreshold">Structuring Threshold (% of limit)</label>
                                        <input type="number" id="structuringThreshold" value="90" min="50" max="99">
                                    </div>
                                </div>
                                <div class="config-group">
                                    <h4>Velocity Monitoring</h4>
                                    <div class="config-item">
                                        <label for="velocityCount">Max Transactions per Hour</label>
                                        <input type="number" id="velocityCount" value="10" min="1" max="100">
                                    </div>
                                    <div class="config-item">
                                        <label for="burstWindow">Burst Detection Window (minutes)</label>
                                        <input type="number" id="burstWindow" value="5" min="1" max="60">
                                    </div>
                                </div>
                            </div>
                            <div class="config-actions">
                                <button id="saveConfigBtn" class="btn">Save Configuration</button>
                                <button id="resetConfigBtn" class="btn btn-secondary">Reset to Defaults</button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <footer>
            <p>Financial Transaction Dashboard V2.5.7 Production &copy; 2024</p>
        </footer>
    </div>

    <!-- JavaScript Files -->
    <script>
        // Check if Web Workers are supported
        if (!window.Worker) {
            console.warn('Web Workers are not supported in this browser. Falling back to main thread processing.');
        }

        // Add error handling for script loading
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('csvWorker.js')) {
                console.error('Error loading Web Worker:', e.message);
            }
        }, true);
    </script>
    <script src="js/constants.js"></script>
    <script src="js/currencyUtils.js"></script>
    <script src="js/performanceMonitor.js"></script>
    <script src="js/fileHandler.js"></script>
    <script src="js/dataProcessor.js"></script>


    <script src="js/ttrRules.js"></script>
    <script src="js/ttrExport.js"></script>
    <script src="js/highValueExport.js"></script>
    <script src="js/transactionExport.js"></script>
    <script src="js/ttrSummaryExport.js"></script>
    <script src="js/ttrFileManager.js"></script>
    <script src="js/ttrMonitoring.js"></script>
    <script src="js/ttrUI.js"></script>
    <script src="js/customerAnalytics.js"></script>
    <script src="js/app.js"></script>

    <!-- Customer Transaction Details Modal -->
    <div class="customer-transaction-modal modern" id="customerTransactionDetailsModal">
        <div class="modal-backdrop" id="modalBackdrop"></div>
        <div class="modal-container modern">
            <div class="modal-header modern">
                <div class="modal-title-section">
                    <div class="title-with-badge">
                        <h3 class="modal-title">Transaction Details</h3>
                        <span class="modal-badge" id="modalAlertStatus">No Alert</span>
                    </div>
                    <div class="modal-customer-info compact">
                        <div class="customer-info-item">
                            <i class="info-icon">👤</i>
                            <span class="info-value" id="modalCustomerName">-</span>
                        </div>
                        <div class="customer-info-item">
                            <i class="info-icon">📅</i>
                            <span class="info-value" id="modalCustomerDate">-</span>
                        </div>
                    </div>
                </div>
                <button class="modal-close-btn modern" id="modalCloseBtn" title="Close Details">
                    <i class="close-icon">✕</i>
                </button>
            </div>

            <div class="modal-body compact">
                <!-- Compact Summary Statistics -->
                <div class="modal-summary-stats compact">
                    <div class="summary-stat-card compact">
                        <div class="stat-content">
                            <span class="stat-value" id="modalTotalCount">0</span>
                            <span class="stat-label">Transactions</span>
                        </div>
                    </div>
                    <div class="summary-stat-card compact">
                        <div class="stat-content">
                            <span class="stat-value mmk" id="modalTotalMMK">0 MMK</span>
                            <span class="stat-label">Total MMK</span>
                        </div>
                    </div>
                    <div class="summary-stat-card compact">
                        <div class="stat-content">
                            <span class="stat-value usd" id="modalTotalUSD">0 USD</span>
                            <span class="stat-label">Total USD</span>
                        </div>
                    </div>
                </div>

                <!-- Compact Transaction Details Table -->
                <div class="modal-table-container compact">
                    <div class="modal-table-header compact">
                        <h4>Transaction Details</h4>
                        <button class="table-control-btn modern" id="modalExportBtn" title="Export transactions">
                            <i class="btn-icon">📥</i>
                            <span>Export</span>
                        </button>
                    </div>
                    <div class="modal-table-wrapper compact">
                        <table class="modal-transaction-table compact" id="modalTransactionTable">
                            <thead>
                                <tr>
                                    <th class="time-col">Time</th>
                                    <th class="amount-col">Amount</th>
                                    <th class="currency-col">CCY</th>
                                    <th class="type-col">Type</th>
                                    <th class="counterparty-col">Counterparty</th>
                                    <th class="serial-col">Serial</th>
                                </tr>
                            </thead>
                            <tbody id="modalTransactionTableBody">
                                <tr class="empty-message-row">
                                    <td colspan="6">No transaction details available.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="modal-footer compact">
                <div class="modal-footer-actions">
                    <button class="modal-action-btn modern" id="modalCancelBtn">
                        <i class="btn-icon">✕</i>
                        <span>Close</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
