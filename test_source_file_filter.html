<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Source File Filter Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #9b59b6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #8e44ad;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .filter-demo {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid rgba(155, 89, 182, 0.1);
        }
        .modern-select {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            font-size: 0.9rem;
            font-weight: 500;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            min-height: 44px;
            max-height: 120px;
            overflow-y: auto;
        }
        .modern-select[multiple] {
            height: auto;
            background-image: none;
            padding-right: 16px;
        }
        .modern-select[multiple] option {
            padding: 4px 8px;
            margin: 2px 0;
            border-radius: 4px;
            background: white;
            color: #495057;
            font-size: 0.85rem;
        }
        .modern-select[multiple] option:checked {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            font-weight: 600;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 15px;
        }
        .filter-group label {
            font-size: 0.8rem;
            font-weight: 600;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
    </style>
</head>
<body>
    <h1>Source File Filter Test</h1>
    
    <div class="test-container">
        <h2>Source File Filter Demo</h2>
        <div class="filter-demo">
            <div class="filter-group">
                <label for="testSourceFileFilter">
                    <i>📁</i>
                    Source File Filter
                </label>
                <select id="testSourceFileFilter" class="modern-select" multiple>
                    <option value="all">All Source Files</option>
                    <option value="daily_transactions_2024-01-20.csv">daily_transactions_2024-01-20.csv</option>
                    <option value="daily_transactions_2024-01-21.csv">daily_transactions_2024-01-21.csv</option>
                    <option value="weekly_report_2024-01.csv">weekly_report_2024-01.csv</option>
                </select>
            </div>
            <p><strong>Instructions:</strong> Hold Ctrl (Windows) or Cmd (Mac) to select multiple files. Click "All Source Files" to select everything.</p>
        </div>
    </div>

    <div class="test-container">
        <h2>Source File Filter Functionality Test</h2>
        <button class="test-button" onclick="createTestDataWithSources()">Create Test Data with Multiple Sources</button>
        <button class="test-button" onclick="testSourceFileFilter()">Test Source File Filtering</button>
        <button class="test-button" onclick="testFilterCombinations()">Test Filter Combinations</button>
        <div id="sourceFilterResults" class="test-results">Click "Create Test Data with Multiple Sources" to start testing</div>
    </div>

    <!-- Include required scripts -->
    <script src="js/constants.js"></script>
    <script src="js/currencyUtils.js"></script>
    <script src="js/customerAnalytics.js"></script>

    <!-- Test elements -->
    <div style="display: none;">
        <select id="customerAlertFilter">
            <option value="alert">All Alert Customers</option>
            <option value="mmk-alert">MMK Alerts (≥1B MMK)</option>
            <option value="usd-alert">USD Alerts (≥10K USD)</option>
        </select>
        
        <select id="customerSortBy">
            <option value="alert-priority">Alert Priority</option>
            <option value="usd-desc">USD Amount (High to Low)</option>
        </select>
        
        <select id="customerDateFilter">
            <option value="all">All Dates</option>
        </select>
        
        <select id="customerSourceFileFilter" class="modern-select" multiple>
            <option value="all">All Source Files</option>
        </select>
        
        <div id="customerAnalyticsTableBody"></div>
        <div id="customerTransactionDetailsModal"></div>
    </div>

    <script>
        let testOutput = '';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testOutput += `[${timestamp}] ${message}\n`;
            console.log(message);
        }
        
        function updateResults() {
            document.getElementById('sourceFilterResults').textContent = testOutput;
        }
        
        function clearResults() {
            testOutput = '';
            updateResults();
        }
        
        function createTestDataWithSources() {
            log('=== Creating Test Data with Multiple Source Files ===');
            clearResults();
            
            if (!window.customerAnalytics) {
                log('❌ Customer Analytics not available');
                updateResults();
                return;
            }
            
            // Initialize if needed
            if (!window.customerAnalytics.isInitialized) {
                window.customerAnalytics.initialize();
            }
            
            // Create test data sources
            const dataSources = [
                {
                    fileName: 'daily_transactions_2024-01-20.csv',
                    transactions: [
                        {
                            CUSTOMER_NAME: 'Customer A',
                            PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST001',
                            TRANSACTION_DATE: '20-JAN-24 10.30.00.000000 AM',
                            TRANSACTION_AMOUNT: 2000000000, // 2B MMK
                            TRANSACTION_CURRENCY: 'MMK'
                        },
                        {
                            CUSTOMER_NAME: 'Customer B',
                            PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST002',
                            TRANSACTION_DATE: '20-JAN-24 11.30.00.000000 AM',
                            TRANSACTION_AMOUNT: 15000, // 15K USD
                            TRANSACTION_CURRENCY: 'USD'
                        }
                    ]
                },
                {
                    fileName: 'daily_transactions_2024-01-21.csv',
                    transactions: [
                        {
                            CUSTOMER_NAME: 'Customer C',
                            PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST003',
                            TRANSACTION_DATE: '21-JAN-24 09.30.00.000000 AM',
                            TRANSACTION_AMOUNT: 1500000000, // 1.5B MMK
                            TRANSACTION_CURRENCY: 'MMK'
                        },
                        {
                            CUSTOMER_NAME: 'Customer D',
                            PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST004',
                            TRANSACTION_DATE: '21-JAN-24 14.30.00.000000 PM',
                            TRANSACTION_AMOUNT: 25000, // 25K USD
                            TRANSACTION_CURRENCY: 'USD'
                        }
                    ]
                },
                {
                    fileName: 'weekly_report_2024-01.csv',
                    transactions: [
                        {
                            CUSTOMER_NAME: 'Customer E',
                            PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST005',
                            TRANSACTION_DATE: '22-JAN-24 16.30.00.000000 PM',
                            TRANSACTION_AMOUNT: 12000, // 12K USD
                            TRANSACTION_CURRENCY: 'USD'
                        }
                    ]
                }
            ];
            
            log(`Processing ${dataSources.length} data sources...`);
            window.customerAnalytics.processMultipleDataSources(dataSources);
            
            log(`✅ Test data created successfully`);
            log(`Available source files: ${Array.from(window.customerAnalytics.sourceFiles).join(', ')}`);
            log(`Total customers: ${window.customerAnalytics.customerData.size}`);
            log(`Alert customers: ${window.customerAnalytics.alertCustomers.size}`);
            
            updateResults();
        }
        
        function testSourceFileFilter() {
            log('\n=== Testing Source File Filter ===');
            
            if (!window.customerAnalytics || !window.customerAnalytics.isInitialized) {
                log('❌ Customer Analytics not initialized. Create test data first.');
                updateResults();
                return;
            }
            
            const sourceFiles = Array.from(window.customerAnalytics.sourceFiles);
            log(`Available source files: ${sourceFiles.join(', ')}`);
            
            // Test each source file individually
            sourceFiles.forEach(sourceFile => {
                log(`\nTesting filter for: ${sourceFile}`);
                
                // Set the filter
                const filterElement = window.customerAnalytics.elements.customerSourceFileFilter;
                if (filterElement) {
                    // Clear all selections
                    Array.from(filterElement.options).forEach(option => option.selected = false);
                    
                    // Select the specific source file
                    Array.from(filterElement.options).forEach(option => {
                        if (option.value === sourceFile) {
                            option.selected = true;
                        }
                    });
                    
                    // Get filtered data
                    const filteredData = window.customerAnalytics.getFilteredAndSortedData();
                    log(`  Found ${filteredData.length} customers from this source`);
                    
                    filteredData.forEach(customer => {
                        log(`    - ${customer.customerName}: ${customer.sourceFiles.join(', ')}`);
                    });
                } else {
                    log('  ❌ Source file filter element not found');
                }
            });
            
            // Test "All Source Files"
            log(`\nTesting "All Source Files" filter`);
            const filterElement = window.customerAnalytics.elements.customerSourceFileFilter;
            if (filterElement) {
                Array.from(filterElement.options).forEach(option => option.selected = false);
                filterElement.options[0].selected = true; // Select "All Source Files"
                
                const allData = window.customerAnalytics.getFilteredAndSortedData();
                log(`  Found ${allData.length} customers total`);
            }
            
            updateResults();
        }
        
        function testFilterCombinations() {
            log('\n=== Testing Filter Combinations ===');
            
            if (!window.customerAnalytics || !window.customerAnalytics.isInitialized) {
                log('❌ Customer Analytics not initialized. Create test data first.');
                updateResults();
                return;
            }
            
            // Test combination: USD alerts + specific source file
            log('Testing: USD Alerts + daily_transactions_2024-01-21.csv');
            
            // Set alert filter to USD
            if (window.customerAnalytics.elements.customerAlertFilter) {
                window.customerAnalytics.elements.customerAlertFilter.value = 'usd-alert';
            }
            
            // Set source file filter
            const filterElement = window.customerAnalytics.elements.customerSourceFileFilter;
            if (filterElement) {
                Array.from(filterElement.options).forEach(option => option.selected = false);
                Array.from(filterElement.options).forEach(option => {
                    if (option.value === 'daily_transactions_2024-01-21.csv') {
                        option.selected = true;
                    }
                });
            }
            
            const combinedData = window.customerAnalytics.getFilteredAndSortedData();
            log(`Found ${combinedData.length} customers matching both filters`);
            
            combinedData.forEach(customer => {
                log(`  - ${customer.customerName}: ${customer.alertStatus}, Sources: ${customer.sourceFiles.join(', ')}`);
            });
            
            updateResults();
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            if (window.customerAnalytics) {
                window.customerAnalytics.initialize();
                log('Customer Analytics initialized for testing');
                updateResults();
            }
        });
        
        // Demo filter interaction
        document.getElementById('testSourceFileFilter').addEventListener('change', function(e) {
            const selected = Array.from(e.target.selectedOptions).map(option => option.value);
            console.log('Demo filter changed:', selected);
        });
    </script>
</body>
</html>
