<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Analytics Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        /* Compact table for testing */
        .test-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.85rem;
            margin-top: 15px;
        }
        .test-table th, .test-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .test-table th {
            background: #9b59b6;
            color: white;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <h1>Customer Analytics Fixes Test</h1>
    
    <div class="test-container">
        <h2>Issue 1: USD Amount Sorting Test</h2>
        <p>Testing if "USD Amount (High to Low)" sorting works correctly</p>
        <button class="test-button" onclick="testUSDSorting()">Test USD Sorting</button>
        <button class="test-button" onclick="createTestDataWithUSD()">Create Test Data with USD</button>
        <div id="usdSortResults" class="test-results">Click "Create Test Data with USD" first, then "Test USD Sorting"</div>
    </div>

    <div class="test-container">
        <h2>Issue 2: Table Layout and Responsiveness Test</h2>
        <p>Testing compact table layout and responsiveness</p>
        <button class="test-button" onclick="testTableLayout()">Test Table Layout</button>
        <button class="test-button" onclick="testResponsiveness()">Test Responsiveness</button>
        <div id="layoutResults" class="test-results">Click test buttons to check table layout and responsiveness</div>
    </div>

    <div class="test-container">
        <h2>Sample Compact Table</h2>
        <p>This shows how the compact table should look:</p>
        <table class="test-table">
            <thead>
                <tr>
                    <th style="width: 22%;">Customer Name</th>
                    <th style="width: 12%;">Customer ID</th>
                    <th style="width: 11%;">Date</th>
                    <th style="width: 14%;">MMK Amount</th>
                    <th style="width: 14%;">USD Amount</th>
                    <th style="width: 8%;">Count</th>
                    <th style="width: 11%;">Alert</th>
                    <th style="width: 8%;">Action</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Customer A</td>
                    <td>CUST001</td>
                    <td>15-JUN-25</td>
                    <td style="color: #f39c12;">2,000,000,000 MMK</td>
                    <td style="color: #3b82f6;">15,000 USD</td>
                    <td>5</td>
                    <td><span style="background: #e74c3c; color: white; padding: 2px 6px; border-radius: 8px; font-size: 0.7rem;">HIGH</span></td>
                    <td><button style="padding: 4px 8px; font-size: 0.7rem;">View</button></td>
                </tr>
                <tr>
                    <td>Customer B</td>
                    <td>CUST002</td>
                    <td>15-JUN-25</td>
                    <td style="color: #f39c12;">500,000,000 MMK</td>
                    <td style="color: #3b82f6;">12,000 USD</td>
                    <td>3</td>
                    <td><span style="background: #3b82f6; color: white; padding: 2px 6px; border-radius: 8px; font-size: 0.7rem;">USD</span></td>
                    <td><button style="padding: 4px 8px; font-size: 0.7rem;">View</button></td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Include required scripts -->
    <script src="js/constants.js"></script>
    <script src="js/currencyUtils.js"></script>
    <script src="js/customerAnalytics.js"></script>

    <!-- Test elements -->
    <div style="display: none;">
        <select id="customerAlertFilter">
            <option value="alert">All Alert Customers</option>
            <option value="mmk-alert">MMK Alerts (≥1B MMK)</option>
            <option value="usd-alert">USD Alerts (≥10K USD)</option>
        </select>
        
        <select id="customerSortBy">
            <option value="alert-priority">Alert Priority</option>
            <option value="mmk-desc">MMK Amount (High to Low)</option>
            <option value="usd-desc">USD Amount (High to Low)</option>
            <option value="total-desc">Transaction Count</option>
        </select>
        
        <div id="customerAnalyticsTableBody"></div>
        <div id="customerTransactionDetailsModal"></div>
    </div>

    <script>
        let testOutput = '';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testOutput += `[${timestamp}] ${message}\n`;
            console.log(message);
        }
        
        function updateResults(elementId) {
            document.getElementById(elementId).textContent = testOutput;
            testOutput = ''; // Clear for next test
        }
        
        function createTestDataWithUSD() {
            log('=== Creating Test Data with USD Amounts ===');
            
            if (!window.customerAnalytics) {
                log('❌ Customer Analytics not available', 'error');
                updateResults('usdSortResults');
                return;
            }
            
            // Initialize if needed
            if (!window.customerAnalytics.isInitialized) {
                window.customerAnalytics.initialize();
            }
            
            // Create test transactions with varying USD amounts
            const testTransactions = [
                {
                    CUSTOMER_NAME: 'High USD Customer',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'USD001',
                    TRANSACTION_DATE: '15-JUN-25 10.30.00.000000 AM',
                    TRANSACTION_AMOUNT: 25000, // 25K USD
                    TRANSACTION_CURRENCY: 'USD'
                },
                {
                    CUSTOMER_NAME: 'Medium USD Customer',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'USD002',
                    TRANSACTION_DATE: '15-JUN-25 11.30.00.000000 AM',
                    TRANSACTION_AMOUNT: 15000, // 15K USD
                    TRANSACTION_CURRENCY: 'USD'
                },
                {
                    CUSTOMER_NAME: 'Low USD Customer',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'USD003',
                    TRANSACTION_DATE: '15-JUN-25 12.30.00.000000 PM',
                    TRANSACTION_AMOUNT: 12000, // 12K USD
                    TRANSACTION_CURRENCY: 'USD'
                },
                {
                    CUSTOMER_NAME: 'Very High USD Customer',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'USD004',
                    TRANSACTION_DATE: '15-JUN-25 13.30.00.000000 PM',
                    TRANSACTION_AMOUNT: 50000, // 50K USD
                    TRANSACTION_CURRENCY: 'USD'
                },
                {
                    CUSTOMER_NAME: 'MMK Customer',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'MMK001',
                    TRANSACTION_DATE: '15-JUN-25 14.30.00.000000 PM',
                    TRANSACTION_AMOUNT: 2000000000, // 2B MMK
                    TRANSACTION_CURRENCY: 'MMK'
                }
            ];
            
            log(`Processing ${testTransactions.length} test transactions...`);
            window.customerAnalytics.processTransactionData(testTransactions);
            
            log(`✅ Test data created successfully`);
            log(`Customer data size: ${window.customerAnalytics.customerData.size}`);
            log(`Alert customers: ${window.customerAnalytics.alertCustomers.size}`);
            
            updateResults('usdSortResults');
        }
        
        function testUSDSorting() {
            log('=== Testing USD Sorting Functionality ===');
            
            if (!window.customerAnalytics || !window.customerAnalytics.isInitialized) {
                log('❌ Customer Analytics not initialized. Create test data first.', 'error');
                updateResults('usdSortResults');
                return;
            }
            
            // Set sort to USD descending
            log('Setting sort to USD Amount (High to Low)...');
            if (window.customerAnalytics.elements.customerSortBy) {
                window.customerAnalytics.elements.customerSortBy.value = 'usd-desc';
                
                // Get sorted data
                const sortedData = window.customerAnalytics.getFilteredAndSortedData();
                log(`Found ${sortedData.length} customers to sort`);
                
                if (sortedData.length === 0) {
                    log('⚠️ No data to sort. Create test data first.', 'warning');
                    updateResults('usdSortResults');
                    return;
                }
                
                log('USD Sorted data (all customers):');
                sortedData.forEach((item, index) => {
                    log(`  ${index + 1}. ${item.customerName}: USD ${item.usdAmount.toLocaleString()}, MMK ${item.mmkAmount.toLocaleString()}`);
                });
                
                // Verify sorting is correct
                let isSortedCorrectly = true;
                let errorDetails = [];
                
                for (let i = 1; i < sortedData.length; i++) {
                    if (sortedData[i-1].usdAmount < sortedData[i].usdAmount) {
                        isSortedCorrectly = false;
                        errorDetails.push(`Position ${i}: ${sortedData[i-1].customerName}(${sortedData[i-1].usdAmount}) < ${sortedData[i].customerName}(${sortedData[i].usdAmount})`);
                    }
                }
                
                if (isSortedCorrectly) {
                    log('✅ USD Sorting is CORRECT - customers are properly sorted by USD amount descending');
                } else {
                    log('❌ USD Sorting is INCORRECT - found sorting errors:');
                    errorDetails.forEach(error => log(`   ${error}`));
                }
                
                // Test the table update
                log('Updating table with USD sort...');
                window.customerAnalytics.updateTable();
                log('✅ Table updated successfully');
                
            } else {
                log('❌ Sort by element not found', 'error');
            }
            
            updateResults('usdSortResults');
        }
        
        function testTableLayout() {
            log('=== Testing Table Layout and Compactness ===');
            
            // Check if table exists
            const table = document.querySelector('.customer-analytics-table');
            if (table) {
                log('✅ Customer analytics table found');
                
                // Check table styles
                const computedStyle = window.getComputedStyle(table);
                log(`Table font size: ${computedStyle.fontSize}`);
                log(`Table layout: ${computedStyle.tableLayout}`);
                
                // Check header styles
                const headers = table.querySelectorAll('th');
                if (headers.length > 0) {
                    const headerStyle = window.getComputedStyle(headers[0]);
                    log(`Header padding: ${headerStyle.padding}`);
                    log(`Header font size: ${headerStyle.fontSize}`);
                }
                
                // Check cell styles
                const cells = table.querySelectorAll('td');
                if (cells.length > 0) {
                    const cellStyle = window.getComputedStyle(cells[0]);
                    log(`Cell padding: ${cellStyle.padding}`);
                    log(`Cell font size: ${cellStyle.fontSize}`);
                }
                
                log('✅ Table layout appears compact and optimized');
            } else {
                log('⚠️ Customer analytics table not found in current page');
            }
            
            updateResults('layoutResults');
        }
        
        function testResponsiveness() {
            log('=== Testing Table Responsiveness ===');
            
            // Get current viewport width
            const viewportWidth = window.innerWidth;
            log(`Current viewport width: ${viewportWidth}px`);
            
            // Check if table is responsive
            const tableContainer = document.querySelector('.table-responsive');
            if (tableContainer) {
                log('✅ Table responsive container found');
                
                const containerStyle = window.getComputedStyle(tableContainer);
                log(`Container overflow-x: ${containerStyle.overflowX}`);
                
                // Check if table fits in viewport
                const table = tableContainer.querySelector('table');
                if (table) {
                    const tableWidth = table.offsetWidth;
                    const containerWidth = tableContainer.offsetWidth;
                    
                    log(`Table width: ${tableWidth}px`);
                    log(`Container width: ${containerWidth}px`);
                    
                    if (tableWidth <= containerWidth) {
                        log('✅ Table fits within container without horizontal scroll');
                    } else {
                        log(`⚠️ Table may require horizontal scroll (${tableWidth - containerWidth}px overflow)`);
                    }
                }
            } else {
                log('⚠️ Table responsive container not found');
            }
            
            // Test different viewport sizes
            log('Responsive breakpoints check:');
            if (viewportWidth <= 480) {
                log('📱 Mobile view (≤480px) - should hide some columns');
            } else if (viewportWidth <= 768) {
                log('📱 Tablet view (≤768px) - should have compact layout');
            } else if (viewportWidth <= 1024) {
                log('💻 Small desktop (≤1024px) - should have medium compact layout');
            } else {
                log('🖥️ Large desktop (>1024px) - should have full layout');
            }
            
            updateResults('layoutResults');
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            log('Page loaded, initializing Customer Analytics...');
            if (window.customerAnalytics) {
                window.customerAnalytics.initialize();
                log('✅ Customer Analytics initialized');
            }
        });
    </script>
</body>
</html>
