/**
 * Test Customer Analytics File Processing
 * Tests the new file processing event handling and source file tracking
 */

function testCustomerAnalyticsFileProcessing() {
    console.log('=== Testing Customer Analytics File Processing ===');
    
    // Check if customer analytics is available
    if (!window.customerAnalytics) {
        console.error('❌ Customer Analytics not available');
        return false;
    }
    
    // Initialize if not already done
    if (!window.customerAnalytics.isInitialized) {
        window.customerAnalytics.initialize();
    }
    
    // Test data - simulate file processing events
    const testFiles = [
        {
            fileName: 'TTR_11Jun2025.csv',
            fileId: 'test_file_1',
            transactions: [
                {
                    CUSTOMER_NAME: 'Test Customer A',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST001',
                    TRANSACTION_DATE: '11-JUN-25',
                    TRANSACTION_AMOUNT: '1500000000', // 1.5B MMK - triggers alert
                    TRANSACTION_CURRENCY: 'MMK',
                    REPORTTYPE: 'HOC'
                },
                {
                    CUSTOMER_NAME: 'Test Customer B',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST002',
                    TRANSACTION_DATE: '11-JUN-25',
                    TRANSACTION_AMOUNT: '15000', // 15K USD - triggers alert
                    TRANSACTION_CURRENCY: 'USD',
                    REPORTTYPE: 'IBD'
                }
            ]
        },
        {
            fileName: 'TTR_12Jun2025.csv',
            fileId: 'test_file_2',
            transactions: [
                {
                    CUSTOMER_NAME: 'Test Customer C',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST003',
                    TRANSACTION_DATE: '12-JUN-25',
                    TRANSACTION_AMOUNT: '2000000000', // 2B MMK - triggers alert
                    TRANSACTION_CURRENCY: 'MMK',
                    REPORTTYPE: 'WU'
                }
            ]
        }
    ];
    
    console.log('📁 Testing file processing events...');
    
    // Clear existing data
    window.customerAnalytics.clearData();
    
    // Simulate file processing events
    testFiles.forEach((fileData, index) => {
        console.log(`Processing test file ${index + 1}: ${fileData.fileName}`);
        
        // Create and dispatch fileProcessed event
        const event = new CustomEvent('fileProcessed', {
            detail: {
                transactions: fileData.transactions,
                fileName: fileData.fileName,
                fileId: fileData.fileId,
                timestamp: Date.now()
            }
        });
        
        document.dispatchEvent(event);
    });
    
    // Wait a moment for processing
    setTimeout(() => {
        console.log('🔍 Checking results...');
        
        // Check source files
        const sourceFiles = Array.from(window.customerAnalytics.sourceFiles);
        console.log('Available source files:', sourceFiles);
        
        if (sourceFiles.length !== testFiles.length) {
            console.error(`❌ Expected ${testFiles.length} source files, got ${sourceFiles.length}`);
            return false;
        }
        
        // Check if all test files are tracked
        const expectedFiles = testFiles.map(f => f.fileName);
        const missingFiles = expectedFiles.filter(f => !sourceFiles.includes(f));
        
        if (missingFiles.length > 0) {
            console.error('❌ Missing source files:', missingFiles);
            return false;
        }
        
        console.log('✅ All source files tracked correctly');
        
        // Check customer data
        const customerCount = window.customerAnalytics.customerData.size;
        console.log(`Customer data entries: ${customerCount}`);
        
        if (customerCount === 0) {
            console.error('❌ No customer data processed');
            return false;
        }
        
        console.log('✅ Customer data processed successfully');
        
        // Test source file filter
        console.log('🔧 Testing source file filter...');
        window.customerAnalytics.updateSourceFileFilter();
        
        const filterElement = document.getElementById('customerSourceFileFilter');
        if (filterElement) {
            const options = Array.from(filterElement.options).map(opt => opt.value);
            console.log('Filter options:', options);
            
            // Should have "all" plus the test files
            const expectedOptions = ['all', ...expectedFiles];
            const missingOptions = expectedOptions.filter(opt => !options.includes(opt));
            
            if (missingOptions.length > 0) {
                console.error('❌ Missing filter options:', missingOptions);
                return false;
            }
            
            console.log('✅ Source file filter populated correctly');
        } else {
            console.warn('⚠️ Source file filter element not found');
        }
        
        // Test filtering by source file
        console.log('🔍 Testing source file filtering...');
        const filteredData = window.customerAnalytics.getFilteredAndSortedData();
        console.log(`Filtered data count: ${filteredData.length}`);
        
        if (filteredData.length > 0) {
            console.log('✅ Data filtering working');
            
            // Show sample data
            console.log('Sample filtered data:');
            filteredData.slice(0, 3).forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.customerName} - ${item.alertStatus} - Files: [${item.sourceFiles.join(', ')}]`);
            });
        } else {
            console.warn('⚠️ No filtered data returned');
        }
        
        console.log('=== Customer Analytics File Processing Test Complete ===');
        return true;
        
    }, 1000);
}

// Auto-run test when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(testCustomerAnalyticsFileProcessing, 2000);
    });
} else {
    setTimeout(testCustomerAnalyticsFileProcessing, 2000);
}

// Expose test function globally
window.testCustomerAnalyticsFileProcessing = testCustomerAnalyticsFileProcessing;

console.log('Customer Analytics File Processing Test loaded. Run testCustomerAnalyticsFileProcessing() to test manually.');
