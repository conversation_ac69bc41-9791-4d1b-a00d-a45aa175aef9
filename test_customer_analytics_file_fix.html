<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Analytics File Processing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .filter-test {
            margin: 10px 0;
        }
        .filter-test select {
            padding: 5px;
            margin: 5px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
            display: inline-block;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Customer Analytics File Processing Test</h1>
        <p>This page tests the fixed file processing and source file filtering functionality.</p>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button class="test-button" onclick="runFileProcessingTest()">Run File Processing Test</button>
            <button class="test-button" onclick="testSourceFileFilter()">Test Source File Filter</button>
            <button class="test-button" onclick="testSortFunctionality()">Test Sort Functionality</button>
            <button class="test-button" onclick="clearTestData()">Clear Test Data</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-section">
            <h3>Source File Filter Test</h3>
            <div class="filter-test">
                <label for="testSourceFileFilter">Source File Filter:</label>
                <select id="testSourceFileFilter" multiple style="height: 80px; width: 300px;">
                    <option value="all">All Source Files</option>
                </select>
                <button class="test-button" onclick="applySourceFileFilter()">Apply Filter</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Sort Test</h3>
            <div class="filter-test">
                <label for="testSortBy">Sort By:</label>
                <select id="testSortBy">
                    <option value="alert-priority">Alert Priority</option>
                    <option value="mmk-desc">MMK Amount (High to Low)</option>
                    <option value="usd-desc">USD Amount (High to Low)</option>
                    <option value="total-desc">Transaction Count</option>
                    <option value="date-desc">Date (Newest First)</option>
                    <option value="customer-name">Customer Name</option>
                </select>
                <button class="test-button" onclick="applySortTest()">Apply Sort</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Status</h3>
            <div id="testStatus">Ready to test...</div>
        </div>
        
        <div class="test-section">
            <h3>Log Output</h3>
            <div id="logOutput" class="log-output">Test log will appear here...\n</div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="js/constants.js"></script>
    <script src="js/currencyUtils.js"></script>
    <script src="js/performanceMonitor.js"></script>
    <script src="js/fileHandler.js"></script>
    <script src="js/dataProcessor.js"></script>
    <script src="js/customerAnalytics.js"></script>
    <script src="test_customer_analytics_file_processing.js"></script>

    <script>
        // Test utility functions
        function log(message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function clearLog() {
            document.getElementById('logOutput').textContent = 'Log cleared...\n';
        }
        
        function clearTestData() {
            if (window.customerAnalytics) {
                window.customerAnalytics.clearData();
                log('Test data cleared');
                setStatus('Test data cleared', 'success');
                updateSourceFileFilterTest();
            }
        }
        
        function runFileProcessingTest() {
            log('Starting file processing test...');
            setStatus('Running file processing test...', 'warning');
            
            if (window.testCustomerAnalyticsFileProcessing) {
                window.testCustomerAnalyticsFileProcessing();
                setTimeout(() => {
                    updateSourceFileFilterTest();
                    setStatus('File processing test completed', 'success');
                }, 2000);
            } else {
                log('ERROR: Test function not available');
                setStatus('Test function not available', 'error');
            }
        }
        
        function updateSourceFileFilterTest() {
            const testFilter = document.getElementById('testSourceFileFilter');
            testFilter.innerHTML = '<option value="all">All Source Files</option>';
            
            if (window.customerAnalytics && window.customerAnalytics.sourceFiles) {
                const sourceFiles = Array.from(window.customerAnalytics.sourceFiles);
                sourceFiles.forEach(fileName => {
                    const option = document.createElement('option');
                    option.value = fileName;
                    option.textContent = fileName;
                    testFilter.appendChild(option);
                });
                log(`Updated test filter with ${sourceFiles.length} source files`);
            }
        }
        
        function testSourceFileFilter() {
            log('Testing source file filter...');
            
            if (!window.customerAnalytics) {
                log('ERROR: Customer Analytics not available');
                setStatus('Customer Analytics not available', 'error');
                return;
            }
            
            const testFilter = document.getElementById('testSourceFileFilter');
            const selectedFiles = Array.from(testFilter.selectedOptions).map(opt => opt.value);
            
            log(`Selected files for filter test: ${selectedFiles.join(', ')}`);
            
            // Test the filtering logic
            const filteredData = window.customerAnalytics.getFilteredAndSortedData();
            log(`Filtered data count: ${filteredData.length}`);
            
            if (filteredData.length > 0) {
                log('Sample filtered data:');
                filteredData.slice(0, 3).forEach((item, index) => {
                    log(`  ${index + 1}. ${item.customerName} - ${item.alertStatus}`);
                });
                setStatus('Source file filter test completed', 'success');
            } else {
                log('No data returned from filter');
                setStatus('No data returned from filter', 'warning');
            }
        }
        
        function applySourceFileFilter() {
            testSourceFileFilter();
        }
        
        function testSortFunctionality() {
            log('Testing sort functionality...');
            
            if (!window.customerAnalytics) {
                log('ERROR: Customer Analytics not available');
                setStatus('Customer Analytics not available', 'error');
                return;
            }
            
            const testSort = document.getElementById('testSortBy');
            const sortValue = testSort.value;
            
            log(`Testing sort: ${sortValue}`);
            
            // Apply sort to customer analytics if elements exist
            const customerSortBy = document.getElementById('customerSortBy');
            if (customerSortBy) {
                customerSortBy.value = sortValue;
                log(`Applied sort ${sortValue} to customer analytics`);
            }
            
            const sortedData = window.customerAnalytics.getFilteredAndSortedData();
            log(`Sorted data count: ${sortedData.length}`);
            
            if (sortedData.length > 0) {
                log('Sample sorted data:');
                sortedData.slice(0, 3).forEach((item, index) => {
                    log(`  ${index + 1}. ${item.customerName} - MMK: ${item.mmkAmount.toLocaleString()} - USD: ${item.usdAmount.toLocaleString()}`);
                });
                setStatus('Sort functionality test completed', 'success');
            } else {
                log('No data returned from sort');
                setStatus('No data returned from sort', 'warning');
            }
        }
        
        function applySortTest() {
            testSortFunctionality();
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            log('Test page loaded');
            setStatus('Test page ready', 'success');
            
            if (window.customerAnalytics) {
                window.customerAnalytics.initialize();
                log('Customer Analytics initialized');
            }
            
            setTimeout(() => {
                updateSourceFileFilterTest();
            }, 1000);
        });
    </script>
</body>
</html>
