/**
 * Streaming Export Web Worker
 * Handles large data exports in a separate thread to prevent UI blocking
 */

// Handle messages from the main thread
self.onmessage = function(e) {
    const { action, data } = e.data;

    try {
        if (action === 'exportCSVStreaming') {
            const result = exportCSVStreaming(data.transactions, data.options);
            self.postMessage({
                action: 'exportComplete',
                result: result
            });
        } else if (action === 'processExportChunk') {
            const result = processExportChunk(data.chunk, data.headers, data.options);
            self.postMessage({
                action: 'chunkProcessed',
                result: result
            });
        }
    } catch (error) {
        self.postMessage({
            action: 'error',
            error: error.message
        });
    }
};

// Export CSV data using streaming approach
function exportCSVStreaming(transactions, options = {}) {
    const chunkSize = options.chunkSize || 5000;
    const headers = options.headers || [
        'Transaction Date',
        'Transaction Amount (MMK)',
        'Transaction Currency',
        'Report Type',
        'Customer ID',
        'Customer Name',
        'Account Number',
        'Transaction Type',
        'Transaction Description',
        'File Name',
        'File ID'
    ];

    let csvContent = '';
    let processedCount = 0;
    const totalTransactions = transactions.length;

    // Add headers
    csvContent += headers.map(header => escapeCSVField(header)).join(',') + '\n';

    // Process transactions in chunks
    for (let i = 0; i < totalTransactions; i += chunkSize) {
        const chunk = transactions.slice(i, i + chunkSize);
        
        // Convert chunk to CSV rows
        const chunkCSV = chunk.map(transaction => [
            transaction.TRANSACTION_DATE || '',
            parseFloat(transaction.TRANSACTION_AMOUNT) || 0,
            transaction.TRANSACTION_CURRENCY || '',
            transaction.REPORTTYPE || '',
            transaction.CUSTOMER_ID || '',
            transaction.CUSTOMER_NAME || '',
            transaction.ACCOUNT_NUMBER || '',
            transaction.TRANSACTION_TYPE || '',
            transaction.TRANSACTION_DESCRIPTION || '',
            transaction.fileName || '',
            transaction.fileId || ''
        ].map(field => escapeCSVField(field)).join(',')).join('\n');

        csvContent += chunkCSV + '\n';
        processedCount += chunk.length;

        // Report progress
        self.postMessage({
            action: 'progress',
            progress: {
                processed: processedCount,
                total: totalTransactions,
                percentage: Math.round((processedCount / totalTransactions) * 100)
            }
        });

        // Yield control periodically
        if (i + chunkSize < totalTransactions) {
            // Small delay to prevent blocking
            const start = Date.now();
            while (Date.now() - start < 1) {
                // Busy wait for 1ms
            }
        }
    }

    return {
        csvContent: csvContent,
        recordCount: processedCount,
        success: true
    };
}

// Process a single chunk for export
function processExportChunk(chunk, headers, options = {}) {
    const rows = chunk.map(transaction => [
        transaction.TRANSACTION_DATE || '',
        parseFloat(transaction.TRANSACTION_AMOUNT) || 0,
        transaction.TRANSACTION_CURRENCY || '',
        transaction.REPORTTYPE || '',
        transaction.CUSTOMER_ID || '',
        transaction.CUSTOMER_NAME || '',
        transaction.ACCOUNT_NUMBER || '',
        transaction.TRANSACTION_TYPE || '',
        transaction.TRANSACTION_DESCRIPTION || '',
        transaction.fileName || '',
        transaction.fileId || ''
    ]);

    // Convert to CSV format
    const csvRows = rows.map(row => 
        row.map(field => escapeCSVField(field)).join(',')
    );

    return {
        csvRows: csvRows,
        processedCount: chunk.length
    };
}

// Escape CSV field
function escapeCSVField(field) {
    if (field === null || field === undefined) {
        return '';
    }

    const stringField = String(field);
    if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
        return `"${stringField.replace(/"/g, '""')}"`;
    }
    return stringField;
}

// Helper function to create a blob URL for downloading
function createBlobURL(content, mimeType = 'text/csv') {
    const blob = new Blob([content], { type: mimeType });
    return URL.createObjectURL(blob);
}

// Memory management helper
function clearMemory() {
    // Force garbage collection by creating and destroying large objects
    const temp = new Array(1000).fill(null).map(() => ({ 
        data: new Array(1000).join('x') 
    }));
    temp.length = 0;
}

// Progress reporting helper
function reportProgress(processed, total, message = '') {
    self.postMessage({
        action: 'progress',
        progress: {
            processed: processed,
            total: total,
            percentage: Math.round((processed / total) * 100),
            message: message
        }
    });
}

console.log('Streaming Export Worker initialized');
