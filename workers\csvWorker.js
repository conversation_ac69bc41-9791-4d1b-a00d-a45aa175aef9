/**
 * CSV Processing Web Worker
 * Handles CSV parsing and data processing in a separate thread
 */

// Handle messages from the main thread
self.onmessage = function(e) {
    const { action, data } = e.data;

    try {
        if (action === 'processChunk') {
            const result = processChunk(data.chunk, data.header, data.isFirstChunk);
            self.postMessage({
                action: 'chunkProcessed',
                result: result
            });
        } else if (action === 'parseCSVLine') {
            const result = parseCSVLine(data.line);
            self.postMessage({
                action: 'lineProcessed',
                result: result
            });
        }
    } catch (error) {
        self.postMessage({
            action: 'error',
            error: error.message
        });
    }
};

// Process a chunk of CSV data with memory optimization and validation
function processChunk(chunk, header, isFirstChunk) {
    // Input validation
    if (!chunk || typeof chunk !== 'string') {
        throw new Error('Invalid chunk: chunk must be a non-empty string');
    }

    if (!header || !Array.isArray(header)) {
        throw new Error('Invalid header: header must be a non-empty array');
    }

    // Split the chunk into lines
    const lines = chunk.split('\n');

    // Pre-allocate arrays for better performance
    const processedLines = [];
    processedLines.length = lines.length; // Pre-allocate space
    let processedCount = 0;

    let startIndex = isFirstChunk ? 1 : 0; // Skip header if this is the first chunk

    // Create field index map for faster lookups
    const fieldIndexMap = new Map();
    const requiredFields = ['REPORTTYPE', 'TRANSACTION_AMOUNT', 'TRANSACTION_CURRENCY',
                           'ACCOUNT_HOLDER_ACCOUNT_ROLE', 'TRANSACTION_DATE', 'SERIAL_NUMBER', 'SERIAL_NO',
                           'CUSTOMER_NAME', 'PARTICIPANT_NAME_CONDUCTOR', 'PARTICIPANT_NAME_COUNTERPARTY',
                           'PARTICIPANT_ID_NUMBER_CONDUCTOR', 'PARTICIPANT_ID_NUMBER_COUNTERPARTY',
                           'TRANSACTION_ID', 'CUSTOMER_ID', 'ACCOUNT_NUMBER', 'TRANSACTION_TYPE',
                           'CHANNEL', 'LOCATION', 'BUSINESS_TYPE'];

    requiredFields.forEach(field => {
        const index = header.indexOf(field);
        if (index !== -1) {
            fieldIndexMap.set(field, index);
        }
    });

    for (let i = startIndex; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line === '') continue;

        const rowData = parseCSVLine(line);

        // Only process rows that have enough data
        if (rowData.length >= header.length) {
            // Create a minimal object with only the fields we need using the index map
            const rowObject = {};
            let hasReportType = false;
            let isValidRow = true;

            for (const [field, index] of fieldIndexMap) {
                const value = rowData[index] || '';

                // Basic data validation
                if (field === 'TRANSACTION_AMOUNT') {
                    const amount = parseFloat(value);
                    if (isNaN(amount) || amount < 0) {
                        isValidRow = false;
                        break;
                    }
                    rowObject[field] = amount;
                } else if (field === 'TRANSACTION_CURRENCY') {
                    const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];

                    // Only default to MMK if the value is truly invalid or empty
                    if (!value || value.trim() === '') {
                        rowObject[field] = 'MMK';
                        console.log(`Currency validation: Empty value, defaulting to MMK`);
                    } else {
                        const upperCurrency = value.toUpperCase().trim();
                        if (!supportedCurrencies.includes(upperCurrency)) {
                            // Invalid currency, default to MMK
                            console.log(`Currency validation: Invalid currency "${value}" (normalized: "${upperCurrency}"), defaulting to MMK`);
                            rowObject[field] = 'MMK';
                        } else {
                            console.log(`Currency validation: Valid currency "${value}" -> "${upperCurrency}"`);
                            rowObject[field] = upperCurrency;
                        }
                    }
                } else {
                    rowObject[field] = value;
                }

                if (field === 'REPORTTYPE' && value) {
                    hasReportType = true;
                }
            }

            // Only add rows that have a REPORTTYPE value and are valid
            if (hasReportType && isValidRow) {
                processedLines[processedCount++] = rowObject;
            }
        }
    }

    // Trim array to actual size
    processedLines.length = processedCount;

    return {
        processedLines: processedLines,
        metrics: calculateMetrics(processedLines),
        dateData: aggregateByDate(processedLines)
    };
}

// Parse a CSV line into an array of values
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    // Add the last field
    result.push(current.trim());

    return result;
}

// Calculate metrics from processed lines
function calculateMetrics(lines) {
    const metrics = {
        totalTransactions: 0,
        totalAmount: 0,
        hocCount: 0,
        hocAmount: 0,
        hocAmountMMK: 0,
        hocAmountUSD: 0,
        hocCreditCount: 0,
        hocCreditAmount: 0,
        hocCreditAmountMMK: 0,
        hocCreditAmountUSD: 0,
        hocCreditAmountCNY: 0,
        hocCreditAmountEUR: 0,
        hocCreditAmountINR: 0,
        hocCreditAmountJPY: 0,
        hocCreditAmountSGD: 0,
        hocCreditAmountTHB: 0,
        hocDebitCount: 0,
        hocDebitAmount: 0,
        hocDebitAmountMMK: 0,
        hocDebitAmountUSD: 0,
        hocDebitAmountCNY: 0,
        hocDebitAmountEUR: 0,
        hocDebitAmountINR: 0,
        hocDebitAmountJPY: 0,
        hocDebitAmountSGD: 0,
        hocDebitAmountTHB: 0,
        ibdCount: 0,
        ibdAmount: 0,
        ibdAmountMMK: 0,
        ibdAmountUSD: 0,
        ibdCreditCount: 0,
        ibdCreditAmount: 0,
        ibdCreditAmountMMK: 0,
        ibdCreditAmountUSD: 0,
        ibdCreditAmountCNY: 0,
        ibdCreditAmountEUR: 0,
        ibdCreditAmountINR: 0,
        ibdCreditAmountJPY: 0,
        ibdCreditAmountSGD: 0,
        ibdCreditAmountTHB: 0,
        ibdDebitCount: 0,
        ibdDebitAmount: 0,
        ibdDebitAmountMMK: 0,
        ibdDebitAmountUSD: 0,
        ibdDebitAmountCNY: 0,
        ibdDebitAmountEUR: 0,
        ibdDebitAmountINR: 0,
        ibdDebitAmountJPY: 0,
        ibdDebitAmountSGD: 0,
        ibdDebitAmountTHB: 0,
        wuCount: 0,
        wuAmount: 0,
        wuAmountMMK: 0,
        wuAmountUSD: 0,
        wuCreditCount: 0,
        wuCreditAmount: 0,
        wuCreditAmountMMK: 0,
        wuCreditAmountUSD: 0,
        wuCreditAmountCNY: 0,
        wuCreditAmountEUR: 0,
        wuCreditAmountINR: 0,
        wuCreditAmountJPY: 0,
        wuCreditAmountSGD: 0,
        wuCreditAmountTHB: 0,
        wuDebitCount: 0,
        wuDebitAmount: 0,
        wuDebitAmountMMK: 0,
        wuDebitAmountUSD: 0,
        wuDebitAmountCNY: 0,
        wuDebitAmountEUR: 0,
        wuDebitAmountINR: 0,
        wuDebitAmountJPY: 0,
        wuDebitAmountSGD: 0,
        wuDebitAmountTHB: 0,
        hocUniqueSerialCount: 0,
        ibdUniqueSerialCount: 0,
        wuUniqueSerialCount: 0,
        totalUniqueSerialCount: 0,
        currencyCounts: {
            MMK: 0,
            USD: 0,
            SGD: 0,
            EUR: 0,
            JPY: 0,
            CNY: 0,
            THB: 0,
            INR: 0
        },
        currencyAmounts: {
            MMK: 0,
            USD: 0,
            SGD: 0,
            EUR: 0,
            JPY: 0,
            CNY: 0,
            THB: 0,
            INR: 0
        },
        currencyCreditAmounts: {
            MMK: 0,
            USD: 0,
            SGD: 0,
            EUR: 0,
            JPY: 0,
            CNY: 0,
            THB: 0,
            INR: 0
        },
        currencyDebitAmounts: {
            MMK: 0,
            USD: 0,
            SGD: 0,
            EUR: 0,
            JPY: 0,
            CNY: 0,
            THB: 0,
            INR: 0
        },
        // High-value transaction counts by currency
        highValueTransactionCounts: {
            MMK: 0,
            USD: 0,
            SGD: 0,
            EUR: 0,
            JPY: 0,
            CNY: 0,
            THB: 0,
            INR: 0
        },
        highValueTransactionCount: 0,  // Count of transactions >= 1B MMK (legacy)
        highValueTransactionCountUSD: 0  // Count of transactions >= 10K USD (legacy)
    };

    // High-value transaction threshold (1 billion MMK)
    const HIGH_VALUE_THRESHOLD = **********;

    // Sets to track unique serial numbers
    const hocSerialNumbers = new Set();
    const ibdSerialNumbers = new Set();
    const wuSerialNumbers = new Set();
    const allSerialNumbers = new Set();

    // Helper function to safely add numeric values
    const safeAdd = (a, b) => {
        const numA = typeof a === 'number' && !isNaN(a) ? a : 0;
        const numB = typeof b === 'number' && !isNaN(b) ? b : 0;
        return numA + numB;
    };

    // Process each line with optimized performance
    const linesLength = lines.length;
    for (let i = 0; i < linesLength; i++) {
        const row = lines[i];

        // Parse amount once and cache (optimized)
        const amountStr = row.TRANSACTION_AMOUNT;
        const amount = typeof amountStr === 'string' ?
            parseFloat(amountStr) || 0 :
            (typeof amountStr === 'number' ? amountStr : 0);

        // Update total counts
        metrics.totalTransactions++;
        metrics.totalAmount += amount;

        // Cache frequently accessed properties
        const currency = row.TRANSACTION_CURRENCY;
        const reportType = row.REPORTTYPE;
        const accountRole = row.ACCOUNT_HOLDER_ACCOUNT_ROLE;
        const serialNo = row.SERIAL_NUMBER || row.SERIAL_NO || '';

        // Check if this is a high-value transaction for any currency
        const currencyThresholds = {
            MMK: **********,  // 1 billion MMK
            USD: 10000,       // 10,000 USD
            SGD: 15000,       // 15,000 SGD
            EUR: 9000,        // 9,000 EUR
            JPY: 1100000,     // 1,100,000 JPY
            CNY: 70000,       // 70,000 CNY
            THB: 350000,      // 350,000 THB
            INR: 800000       // 800,000 INR
        };

        const threshold = currencyThresholds[currency];
        if (threshold && amount >= threshold) {
            // Update currency-specific high-value counts
            if (metrics.highValueTransactionCounts && metrics.highValueTransactionCounts[currency] !== undefined) {
                metrics.highValueTransactionCounts[currency]++;
            }

            // Maintain backward compatibility with legacy counters
            if (currency === 'MMK') {
                metrics.highValueTransactionCount++;
            } else if (currency === 'USD') {
                metrics.highValueTransactionCountUSD++;
            }
        }

        // Determine transaction type once
        const isCredit = accountRole === 'C';
        const isDebit = accountRole === 'D';

        // Update report type counts (optimized branching)
        if (reportType === 'HOC') {
            metrics.hocCount++;
            metrics.hocAmount += amount;

            // Update currency-specific HOC amounts
            if (currency === 'MMK') {
                metrics.hocAmountMMK += amount;
            } else if (currency === 'USD') {
                metrics.hocAmountUSD += amount;
            }
            // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

            // Track unique serial numbers for HOC
            if (serialNo) {
                hocSerialNumbers.add(serialNo);
                allSerialNumbers.add(serialNo);
            }

            // Update HOC credit/debit counts
            if (isCredit) {
                metrics.hocCreditCount++;
                metrics.hocCreditAmount += amount;

                // Update currency-specific HOC credit amounts
                if (currency === 'MMK') {
                    metrics.hocCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    metrics.hocCreditAmountUSD += amount;
                } else if (currency === 'CNY') {
                    metrics.hocCreditAmountCNY += amount;
                } else if (currency === 'EUR') {
                    metrics.hocCreditAmountEUR += amount;
                } else if (currency === 'INR') {
                    metrics.hocCreditAmountINR += amount;
                } else if (currency === 'JPY') {
                    metrics.hocCreditAmountJPY += amount;
                } else if (currency === 'SGD') {
                    metrics.hocCreditAmountSGD += amount;
                } else if (currency === 'THB') {
                    metrics.hocCreditAmountTHB += amount;
                }
            } else if (isDebit) {
                metrics.hocDebitCount++;
                metrics.hocDebitAmount += amount;

                // Update currency-specific HOC debit amounts
                if (currency === 'MMK') {
                    metrics.hocDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    metrics.hocDebitAmountUSD += amount;
                } else if (currency === 'CNY') {
                    metrics.hocDebitAmountCNY += amount;
                } else if (currency === 'EUR') {
                    metrics.hocDebitAmountEUR += amount;
                } else if (currency === 'INR') {
                    metrics.hocDebitAmountINR += amount;
                } else if (currency === 'JPY') {
                    metrics.hocDebitAmountJPY += amount;
                } else if (currency === 'SGD') {
                    metrics.hocDebitAmountSGD += amount;
                } else if (currency === 'THB') {
                    metrics.hocDebitAmountTHB += amount;
                }
            }
        } else if (reportType === 'IBD') {
            metrics.ibdCount++;
            metrics.ibdAmount += amount;

            // Update currency-specific IBD amounts
            if (currency === 'MMK') {
                metrics.ibdAmountMMK += amount;
            } else if (currency === 'USD') {
                metrics.ibdAmountUSD += amount;
            }
            // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

            // Track unique serial numbers for IBD
            if (serialNo) {
                ibdSerialNumbers.add(serialNo);
                allSerialNumbers.add(serialNo);
            }

            // Update IBD credit/debit counts
            if (isCredit) {
                metrics.ibdCreditCount++;
                metrics.ibdCreditAmount += amount;

                // Update currency-specific IBD credit amounts
                if (currency === 'MMK') {
                    metrics.ibdCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    metrics.ibdCreditAmountUSD += amount;
                } else if (currency === 'CNY') {
                    metrics.ibdCreditAmountCNY += amount;
                } else if (currency === 'EUR') {
                    metrics.ibdCreditAmountEUR += amount;
                } else if (currency === 'INR') {
                    metrics.ibdCreditAmountINR += amount;
                } else if (currency === 'JPY') {
                    metrics.ibdCreditAmountJPY += amount;
                } else if (currency === 'SGD') {
                    metrics.ibdCreditAmountSGD += amount;
                } else if (currency === 'THB') {
                    metrics.ibdCreditAmountTHB += amount;
                }
            } else if (isDebit) {
                metrics.ibdDebitCount++;
                metrics.ibdDebitAmount += amount;

                // Update currency-specific IBD debit amounts
                if (currency === 'MMK') {
                    metrics.ibdDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    metrics.ibdDebitAmountUSD += amount;
                } else if (currency === 'CNY') {
                    metrics.ibdDebitAmountCNY += amount;
                } else if (currency === 'EUR') {
                    metrics.ibdDebitAmountEUR += amount;
                } else if (currency === 'INR') {
                    metrics.ibdDebitAmountINR += amount;
                } else if (currency === 'JPY') {
                    metrics.ibdDebitAmountJPY += amount;
                } else if (currency === 'SGD') {
                    metrics.ibdDebitAmountSGD += amount;
                } else if (currency === 'THB') {
                    metrics.ibdDebitAmountTHB += amount;
                }
            }
        } else if (reportType === 'WU') {
            // WU (Western Union) transactions - separate metrics (not included in IBD)
            metrics.wuCount++;
            metrics.wuAmount += amount;

            // Update currency-specific WU amounts
            if (currency === 'MMK') {
                metrics.wuAmountMMK += amount;
            } else if (currency === 'USD') {
                metrics.wuAmountUSD += amount;
            }
            // Other currencies (CNY, EUR, INR, JPY, SGD, THB) are handled in credit/debit sections

            // Track unique serial numbers for WU
            if (serialNo) {
                wuSerialNumbers.add(serialNo);
                allSerialNumbers.add(serialNo);
            }

            // Update WU credit/debit counts
            if (isCredit) {
                metrics.wuCreditCount++;
                metrics.wuCreditAmount += amount;

                // Update currency-specific WU credit amounts
                if (currency === 'MMK') {
                    metrics.wuCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    metrics.wuCreditAmountUSD += amount;
                } else if (currency === 'CNY') {
                    metrics.wuCreditAmountCNY += amount;
                } else if (currency === 'EUR') {
                    metrics.wuCreditAmountEUR += amount;
                } else if (currency === 'INR') {
                    metrics.wuCreditAmountINR += amount;
                } else if (currency === 'JPY') {
                    metrics.wuCreditAmountJPY += amount;
                } else if (currency === 'SGD') {
                    metrics.wuCreditAmountSGD += amount;
                } else if (currency === 'THB') {
                    metrics.wuCreditAmountTHB += amount;
                }
            } else if (isDebit) {
                metrics.wuDebitCount++;
                metrics.wuDebitAmount += amount;

                // Update currency-specific WU debit amounts
                if (currency === 'MMK') {
                    metrics.wuDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    metrics.wuDebitAmountUSD += amount;
                } else if (currency === 'CNY') {
                    metrics.wuDebitAmountCNY += amount;
                } else if (currency === 'EUR') {
                    metrics.wuDebitAmountEUR += amount;
                } else if (currency === 'INR') {
                    metrics.wuDebitAmountINR += amount;
                } else if (currency === 'JPY') {
                    metrics.wuDebitAmountJPY += amount;
                } else if (currency === 'SGD') {
                    metrics.wuDebitAmountSGD += amount;
                } else if (currency === 'THB') {
                    metrics.wuDebitAmountTHB += amount;
                }
            }
        }

        // Update currency counts for all supported currencies
        const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
        if (supportedCurrencies.includes(currency)) {
            metrics.currencyCounts[currency]++;
            // REMOVED: metrics.currencyAmounts[currency] += amount;
            // Currency amounts are already calculated in report-type specific sections above to prevent double counting

            // Track credit/debit amounts by currency
            if (isCredit) {
                metrics.currencyCreditAmounts[currency] += amount;
            } else if (isDebit) {
                metrics.currencyDebitAmounts[currency] += amount;
            }
        } else {
            // Default to MMK for unsupported currencies
            metrics.currencyCounts.MMK++;
            // REMOVED: metrics.currencyAmounts.MMK += amount;
            // Currency amounts are already calculated in report-type specific sections above to prevent double counting

            // Track credit/debit amounts by currency for unsupported currencies (default to MMK)
            if (isCredit) {
                metrics.currencyCreditAmounts.MMK += amount;
            } else if (isDebit) {
                metrics.currencyDebitAmounts.MMK += amount;
            }
        }
    }

    // Calculate total currency amounts from report-type specific amounts to prevent double counting
    metrics.currencyAmounts.MMK = (metrics.hocAmountMMK || 0) + (metrics.ibdAmountMMK || 0) + (metrics.wuAmountMMK || 0);
    metrics.currencyAmounts.USD = (metrics.hocAmountUSD || 0) + (metrics.ibdAmountUSD || 0) + (metrics.wuAmountUSD || 0);

    // For other currencies (SGD, EUR, JPY, CNY, THB, INR), calculate from credit/debit amounts
    // since they don't have report-type specific tracking
    const otherCurrencies = ['SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
    otherCurrencies.forEach(currency => {
        metrics.currencyAmounts[currency] = (metrics.currencyCreditAmounts[currency] || 0) + (metrics.currencyDebitAmounts[currency] || 0);
    });

    // Update unique serial counts
    metrics.hocUniqueSerialCount = hocSerialNumbers.size;
    metrics.ibdUniqueSerialCount = ibdSerialNumbers.size;
    metrics.wuUniqueSerialCount = wuSerialNumbers.size;
    metrics.totalUniqueSerialCount = allSerialNumbers.size;

    return metrics;
}

// Aggregate data by date
function aggregateByDate(lines) {
    const dateData = {};

    for (const row of lines) {
        // Extract date from transaction date using a standardized approach
        let date = 'Unknown';
        if (row.TRANSACTION_DATE) {
            try {
                // Extract just the date part if there's a space (time component)
                let dateStr = row.TRANSACTION_DATE;
                if (dateStr.includes(' ')) {
                    dateStr = dateStr.split(' ')[0];
                }

                // Format: "26-JAN-25" (DD-MMM-YY)
                if (/^\d{2}-[A-Z]{3}-\d{2}$/.test(dateStr)) {
                    date = dateStr;
                }
                // Format: "2023-01-01" (ISO format YYYY-MM-DD)
                else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                    // Convert to DD-MMM-YY format for consistency
                    const dateObj = new Date(dateStr);
                    if (!isNaN(dateObj.getTime())) {
                        date = dateObj.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short',
                            year: '2-digit'
                        }).replace(/ /g, '-').toUpperCase();
                    } else {
                        date = dateStr;
                    }
                }
                // Try to parse as date if it's in another format
                else {
                    const dateObj = new Date(dateStr);
                    if (!isNaN(dateObj.getTime())) {
                        date = dateObj.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short',
                            year: '2-digit'
                        }).replace(/ /g, '-').toUpperCase();
                    } else {
                        date = dateStr;
                    }
                }
            } catch (error) {
                console.error('Error parsing date:', error);
                date = 'Unknown';
            }
        }

        // Initialize date entry if it doesn't exist
        if (!dateData[date]) {
            dateData[date] = {
                hocCreditCount: 0,
                hocCreditAmountMMK: 0,
                hocCreditAmountUSD: 0,
                hocDebitCount: 0,
                hocDebitAmountMMK: 0,
                hocDebitAmountUSD: 0,
                ibdCreditCount: 0,
                ibdCreditAmountMMK: 0,
                ibdCreditAmountUSD: 0,
                ibdDebitCount: 0,
                ibdDebitAmountMMK: 0,
                ibdDebitAmountUSD: 0,
                wuCreditCount: 0,
                wuCreditAmountMMK: 0,
                wuCreditAmountUSD: 0,
                wuDebitCount: 0,
                wuDebitAmountMMK: 0,
                wuDebitAmountUSD: 0,
                // Legacy fields for backward compatibility
                hocCreditAmount: 0,
                hocDebitAmount: 0,
                ibdCreditAmount: 0,
                ibdDebitAmount: 0
            };
        }

        // Get transaction amount as number with validation
        const amount = typeof row.TRANSACTION_AMOUNT === 'string' ?
            parseFloat(row.TRANSACTION_AMOUNT) || 0 :
            (typeof row.TRANSACTION_AMOUNT === 'number' ? row.TRANSACTION_AMOUNT : 0);

        // Determine transaction type
        const isCredit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'C';
        const isDebit = row.ACCOUNT_HOLDER_ACCOUNT_ROLE === 'D';

        // Get currency for proper field assignment
        const currency = row.TRANSACTION_CURRENCY || 'MMK';

        // Update counts and amounts based on transaction type
        if (row.REPORTTYPE === 'HOC') {
            if (isCredit) {
                dateData[date].hocCreditCount++;
                dateData[date].hocCreditAmount += amount; // Legacy total
                if (currency === 'MMK') {
                    dateData[date].hocCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].hocCreditAmountUSD += amount;
                }
            } else if (isDebit) {
                dateData[date].hocDebitCount++;
                dateData[date].hocDebitAmount += amount; // Legacy total
                if (currency === 'MMK') {
                    dateData[date].hocDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].hocDebitAmountUSD += amount;
                }
            }
        } else if (row.REPORTTYPE === 'IBD') {
            if (isCredit) {
                dateData[date].ibdCreditCount++;
                dateData[date].ibdCreditAmount += amount; // Legacy total
                if (currency === 'MMK') {
                    dateData[date].ibdCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].ibdCreditAmountUSD += amount;
                }
            } else if (isDebit) {
                dateData[date].ibdDebitCount++;
                dateData[date].ibdDebitAmount += amount; // Legacy total
                if (currency === 'MMK') {
                    dateData[date].ibdDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].ibdDebitAmountUSD += amount;
                }
            }
        } else if (row.REPORTTYPE === 'WU' || row.REPORTTYPE === 'IBU') {
            // For WU and IBU transactions (IBU treated as equivalent to WU),
            // add to separate WU metrics for table data only (excluding WU IBD transactions)
            // Note: Other parts of the system continue to include WU in IBD metrics
            if (isCredit) {
                dateData[date].wuCreditCount++;
                if (currency === 'MMK') {
                    dateData[date].wuCreditAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].wuCreditAmountUSD += amount;
                }
            } else if (isDebit) {
                dateData[date].wuDebitCount++;
                if (currency === 'MMK') {
                    dateData[date].wuDebitAmountMMK += amount;
                } else if (currency === 'USD') {
                    dateData[date].wuDebitAmountUSD += amount;
                }
            }
        }
    }

    return dateData;
}

// Helper function to aggregate metrics (for batched processing)
function aggregateMetrics(target, source) {
    const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);

    target.totalTransactions = safeAdd(target.totalTransactions, source.totalTransactions);
    target.totalAmount = safeAdd(target.totalAmount, source.totalAmount);
    target.hocCount = safeAdd(target.hocCount, source.hocCount);
    target.hocAmount = safeAdd(target.hocAmount, source.hocAmount);
    target.hocCreditCount = safeAdd(target.hocCreditCount, source.hocCreditCount);
    target.hocCreditAmount = safeAdd(target.hocCreditAmount, source.hocCreditAmount);
    target.hocCreditAmountMMK = safeAdd(target.hocCreditAmountMMK, source.hocCreditAmountMMK);
    target.hocCreditAmountUSD = safeAdd(target.hocCreditAmountUSD, source.hocCreditAmountUSD);
    target.hocCreditAmountCNY = safeAdd(target.hocCreditAmountCNY, source.hocCreditAmountCNY);
    target.hocCreditAmountEUR = safeAdd(target.hocCreditAmountEUR, source.hocCreditAmountEUR);
    target.hocCreditAmountINR = safeAdd(target.hocCreditAmountINR, source.hocCreditAmountINR);
    target.hocCreditAmountJPY = safeAdd(target.hocCreditAmountJPY, source.hocCreditAmountJPY);
    target.hocCreditAmountSGD = safeAdd(target.hocCreditAmountSGD, source.hocCreditAmountSGD);
    target.hocCreditAmountTHB = safeAdd(target.hocCreditAmountTHB, source.hocCreditAmountTHB);
    target.hocDebitCount = safeAdd(target.hocDebitCount, source.hocDebitCount);
    target.hocDebitAmount = safeAdd(target.hocDebitAmount, source.hocDebitAmount);
    target.hocDebitAmountMMK = safeAdd(target.hocDebitAmountMMK, source.hocDebitAmountMMK);
    target.hocDebitAmountUSD = safeAdd(target.hocDebitAmountUSD, source.hocDebitAmountUSD);
    target.hocDebitAmountCNY = safeAdd(target.hocDebitAmountCNY, source.hocDebitAmountCNY);
    target.hocDebitAmountEUR = safeAdd(target.hocDebitAmountEUR, source.hocDebitAmountEUR);
    target.hocDebitAmountINR = safeAdd(target.hocDebitAmountINR, source.hocDebitAmountINR);
    target.hocDebitAmountJPY = safeAdd(target.hocDebitAmountJPY, source.hocDebitAmountJPY);
    target.hocDebitAmountSGD = safeAdd(target.hocDebitAmountSGD, source.hocDebitAmountSGD);
    target.hocDebitAmountTHB = safeAdd(target.hocDebitAmountTHB, source.hocDebitAmountTHB);
    target.ibdCount = safeAdd(target.ibdCount, source.ibdCount);
    target.ibdAmount = safeAdd(target.ibdAmount, source.ibdAmount);
    target.ibdCreditCount = safeAdd(target.ibdCreditCount, source.ibdCreditCount);
    target.ibdCreditAmount = safeAdd(target.ibdCreditAmount, source.ibdCreditAmount);
    target.ibdCreditAmountMMK = safeAdd(target.ibdCreditAmountMMK, source.ibdCreditAmountMMK);
    target.ibdCreditAmountUSD = safeAdd(target.ibdCreditAmountUSD, source.ibdCreditAmountUSD);
    target.ibdCreditAmountCNY = safeAdd(target.ibdCreditAmountCNY, source.ibdCreditAmountCNY);
    target.ibdCreditAmountEUR = safeAdd(target.ibdCreditAmountEUR, source.ibdCreditAmountEUR);
    target.ibdCreditAmountINR = safeAdd(target.ibdCreditAmountINR, source.ibdCreditAmountINR);
    target.ibdCreditAmountJPY = safeAdd(target.ibdCreditAmountJPY, source.ibdCreditAmountJPY);
    target.ibdCreditAmountSGD = safeAdd(target.ibdCreditAmountSGD, source.ibdCreditAmountSGD);
    target.ibdCreditAmountTHB = safeAdd(target.ibdCreditAmountTHB, source.ibdCreditAmountTHB);
    target.ibdDebitCount = safeAdd(target.ibdDebitCount, source.ibdDebitCount);
    target.ibdDebitAmount = safeAdd(target.ibdDebitAmount, source.ibdDebitAmount);
    target.ibdDebitAmountMMK = safeAdd(target.ibdDebitAmountMMK, source.ibdDebitAmountMMK);
    target.ibdDebitAmountUSD = safeAdd(target.ibdDebitAmountUSD, source.ibdDebitAmountUSD);
    target.ibdDebitAmountCNY = safeAdd(target.ibdDebitAmountCNY, source.ibdDebitAmountCNY);
    target.ibdDebitAmountEUR = safeAdd(target.ibdDebitAmountEUR, source.ibdDebitAmountEUR);
    target.ibdDebitAmountINR = safeAdd(target.ibdDebitAmountINR, source.ibdDebitAmountINR);
    target.ibdDebitAmountJPY = safeAdd(target.ibdDebitAmountJPY, source.ibdDebitAmountJPY);
    target.ibdDebitAmountSGD = safeAdd(target.ibdDebitAmountSGD, source.ibdDebitAmountSGD);
    target.ibdDebitAmountTHB = safeAdd(target.ibdDebitAmountTHB, source.ibdDebitAmountTHB);

    // Add WU metrics
    target.wuCount = safeAdd(target.wuCount, source.wuCount);
    target.wuAmount = safeAdd(target.wuAmount, source.wuAmount);
    target.wuAmountMMK = safeAdd(target.wuAmountMMK, source.wuAmountMMK);
    target.wuAmountUSD = safeAdd(target.wuAmountUSD, source.wuAmountUSD);
    target.wuCreditCount = safeAdd(target.wuCreditCount, source.wuCreditCount);
    target.wuCreditAmount = safeAdd(target.wuCreditAmount, source.wuCreditAmount);
    target.wuCreditAmountMMK = safeAdd(target.wuCreditAmountMMK, source.wuCreditAmountMMK);
    target.wuCreditAmountUSD = safeAdd(target.wuCreditAmountUSD, source.wuCreditAmountUSD);
    target.wuCreditAmountCNY = safeAdd(target.wuCreditAmountCNY, source.wuCreditAmountCNY);
    target.wuCreditAmountEUR = safeAdd(target.wuCreditAmountEUR, source.wuCreditAmountEUR);
    target.wuCreditAmountINR = safeAdd(target.wuCreditAmountINR, source.wuCreditAmountINR);
    target.wuCreditAmountJPY = safeAdd(target.wuCreditAmountJPY, source.wuCreditAmountJPY);
    target.wuCreditAmountSGD = safeAdd(target.wuCreditAmountSGD, source.wuCreditAmountSGD);
    target.wuCreditAmountTHB = safeAdd(target.wuCreditAmountTHB, source.wuCreditAmountTHB);
    target.wuDebitCount = safeAdd(target.wuDebitCount, source.wuDebitCount);
    target.wuDebitAmount = safeAdd(target.wuDebitAmount, source.wuDebitAmount);
    target.wuDebitAmountMMK = safeAdd(target.wuDebitAmountMMK, source.wuDebitAmountMMK);
    target.wuDebitAmountUSD = safeAdd(target.wuDebitAmountUSD, source.wuDebitAmountUSD);
    target.wuDebitAmountCNY = safeAdd(target.wuDebitAmountCNY, source.wuDebitAmountCNY);
    target.wuDebitAmountEUR = safeAdd(target.wuDebitAmountEUR, source.wuDebitAmountEUR);
    target.wuDebitAmountINR = safeAdd(target.wuDebitAmountINR, source.wuDebitAmountINR);
    target.wuDebitAmountJPY = safeAdd(target.wuDebitAmountJPY, source.wuDebitAmountJPY);
    target.wuDebitAmountSGD = safeAdd(target.wuDebitAmountSGD, source.wuDebitAmountSGD);
    target.wuDebitAmountTHB = safeAdd(target.wuDebitAmountTHB, source.wuDebitAmountTHB);

    // Update currency counts and amounts for all supported currencies
    const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
    supportedCurrencies.forEach(currency => {
        target.currencyCounts[currency] = safeAdd(target.currencyCounts[currency], source.currencyCounts[currency]);
        target.currencyAmounts[currency] = safeAdd(target.currencyAmounts[currency], source.currencyAmounts[currency]);
        target.currencyCreditAmounts[currency] = safeAdd(target.currencyCreditAmounts[currency], source.currencyCreditAmounts[currency]);
        target.currencyDebitAmounts[currency] = safeAdd(target.currencyDebitAmounts[currency], source.currencyDebitAmounts[currency]);
    });

    // Update high-value transaction counts by currency
    if (source.highValueTransactionCounts && target.highValueTransactionCounts) {
        supportedCurrencies.forEach(currency => {
            target.highValueTransactionCounts[currency] = safeAdd(target.highValueTransactionCounts[currency], source.highValueTransactionCounts[currency]);
        });
    }

    target.highValueTransactionCount = safeAdd(target.highValueTransactionCount, source.highValueTransactionCount);
    target.highValueTransactionCountUSD = safeAdd(target.highValueTransactionCountUSD, source.highValueTransactionCountUSD);
}

// Helper function to aggregate date data (for batched processing)
function aggregateDateData(target, source) {
    const safeAdd = (a, b) => (Number(a) || 0) + (Number(b) || 0);

    for (const [date, data] of Object.entries(source)) {
        if (!target[date]) {
            target[date] = {
                hocCreditCount: 0,
                hocCreditAmountMMK: 0,
                hocCreditAmountUSD: 0,
                hocDebitCount: 0,
                hocDebitAmountMMK: 0,
                hocDebitAmountUSD: 0,
                ibdCreditCount: 0,
                ibdCreditAmountMMK: 0,
                ibdCreditAmountUSD: 0,
                ibdDebitCount: 0,
                ibdDebitAmountMMK: 0,
                ibdDebitAmountUSD: 0,
                wuCreditCount: 0,
                wuCreditAmountMMK: 0,
                wuCreditAmountUSD: 0,
                wuDebitCount: 0,
                wuDebitAmountMMK: 0,
                wuDebitAmountUSD: 0,
                // Legacy fields for backward compatibility
                hocCreditAmount: 0,
                hocDebitAmount: 0,
                ibdCreditAmount: 0,
                ibdDebitAmount: 0
            };
        }

        target[date].hocCreditCount = safeAdd(target[date].hocCreditCount, data.hocCreditCount);
        target[date].hocCreditAmountMMK = safeAdd(target[date].hocCreditAmountMMK, data.hocCreditAmountMMK || 0);
        target[date].hocCreditAmountUSD = safeAdd(target[date].hocCreditAmountUSD, data.hocCreditAmountUSD || 0);
        target[date].hocDebitCount = safeAdd(target[date].hocDebitCount, data.hocDebitCount);
        target[date].hocDebitAmountMMK = safeAdd(target[date].hocDebitAmountMMK, data.hocDebitAmountMMK || 0);
        target[date].hocDebitAmountUSD = safeAdd(target[date].hocDebitAmountUSD, data.hocDebitAmountUSD || 0);
        target[date].ibdCreditCount = safeAdd(target[date].ibdCreditCount, data.ibdCreditCount);
        target[date].ibdCreditAmountMMK = safeAdd(target[date].ibdCreditAmountMMK, data.ibdCreditAmountMMK || 0);
        target[date].ibdCreditAmountUSD = safeAdd(target[date].ibdCreditAmountUSD, data.ibdCreditAmountUSD || 0);
        target[date].ibdDebitCount = safeAdd(target[date].ibdDebitCount, data.ibdDebitCount);
        target[date].ibdDebitAmountMMK = safeAdd(target[date].ibdDebitAmountMMK, data.ibdDebitAmountMMK || 0);
        target[date].ibdDebitAmountUSD = safeAdd(target[date].ibdDebitAmountUSD, data.ibdDebitAmountUSD || 0);
        target[date].wuCreditCount = safeAdd(target[date].wuCreditCount, data.wuCreditCount || 0);
        target[date].wuCreditAmountMMK = safeAdd(target[date].wuCreditAmountMMK, data.wuCreditAmountMMK || 0);
        target[date].wuCreditAmountUSD = safeAdd(target[date].wuCreditAmountUSD, data.wuCreditAmountUSD || 0);
        target[date].wuDebitCount = safeAdd(target[date].wuDebitCount, data.wuDebitCount || 0);
        target[date].wuDebitAmountMMK = safeAdd(target[date].wuDebitAmountMMK, data.wuDebitAmountMMK || 0);
        target[date].wuDebitAmountUSD = safeAdd(target[date].wuDebitAmountUSD, data.wuDebitAmountUSD || 0);
        // Legacy fields for backward compatibility
        target[date].hocCreditAmount = safeAdd(target[date].hocCreditAmount, data.hocCreditAmount);
        target[date].hocDebitAmount = safeAdd(target[date].hocDebitAmount, data.hocDebitAmount);
        target[date].ibdCreditAmount = safeAdd(target[date].ibdCreditAmount, data.ibdCreditAmount);
        target[date].ibdDebitAmount = safeAdd(target[date].ibdDebitAmount, data.ibdDebitAmount);
    }
}
