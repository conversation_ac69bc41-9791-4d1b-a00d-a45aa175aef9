<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Breakdown Removal Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .currency-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .currency-table th, .currency-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .currency-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button.danger {
            background-color: #dc3545;
        }
        .test-button.danger:hover {
            background-color: #c82333;
        }
        .test-results {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.pass {
            background-color: #d4edda;
            color: #155724;
        }
        .status.fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Currency Breakdown File Removal Fix Test</h1>
        <p>This test verifies that the currency breakdown section updates correctly when CSV files are removed from the dashboard.</p>
        
        <div class="test-section">
            <h2>Test Instructions</h2>
            <ol>
                <li>Upload multiple CSV files with different currency transactions</li>
                <li>Verify currency breakdown shows correct totals</li>
                <li>Remove one or more files</li>
                <li>Verify currency breakdown updates to reflect only remaining files</li>
                <li>Use the test buttons below to simulate and verify the fix</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>Test Controls</h2>
            <button class="test-button" onclick="testCurrencyRecalculation()">Test Currency Recalculation</button>
            <button class="test-button" onclick="simulateFileRemoval()">Simulate File Removal</button>
            <button class="test-button" onclick="verifyUIUpdate()">Verify UI Update</button>
            <button class="test-button danger" onclick="resetTest()">Reset Test</button>
        </div>

        <div class="test-section">
            <h2>Current Currency Breakdown</h2>
            <table class="currency-table" id="testCurrencyTable">
                <thead>
                    <tr>
                        <th>Currency</th>
                        <th>Credit Amount</th>
                        <th>Debit Amount</th>
                        <th>Total Amount</th>
                    </tr>
                </thead>
                <tbody id="testCurrencyTableBody">
                    <tr>
                        <td colspan="4">No data available - please load the main dashboard first</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>Test Results</h2>
            <div id="testResults" class="test-results">
Test results will appear here...
            </div>
        </div>
    </div>

    <script>
        // Test functions for currency breakdown fix
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'pass' ? 'pass' : type === 'fail' ? 'fail' : 'info';
            resultsDiv.innerHTML += `<div class="status ${statusClass}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function testCurrencyRecalculation() {
            logResult('Testing currency recalculation functionality...', 'info');
            
            if (typeof window.dataProcessor === 'undefined') {
                logResult('❌ FAIL: DataProcessor not found. Please load the main dashboard first.', 'fail');
                return;
            }

            if (typeof window.dataProcessor.recalculateCurrencyMetrics !== 'function') {
                logResult('❌ FAIL: recalculateCurrencyMetrics method not found', 'fail');
                return;
            }

            try {
                // Test the recalculation method
                window.dataProcessor.recalculateCurrencyMetrics();
                logResult('✅ PASS: Currency recalculation method executed successfully', 'pass');
                
                // Update the test table
                updateTestCurrencyTable();
                
            } catch (error) {
                logResult(`❌ FAIL: Error during currency recalculation: ${error.message}`, 'fail');
            }
        }

        function simulateFileRemoval() {
            logResult('Simulating file removal scenario...', 'info');
            
            if (typeof window.fileHandler === 'undefined') {
                logResult('❌ FAIL: FileHandler not found. Please load the main dashboard first.', 'fail');
                return;
            }

            if (typeof window.fileHandler.rebuildRawDataFromActiveFiles !== 'function') {
                logResult('❌ FAIL: rebuildRawDataFromActiveFiles method not found', 'fail');
                return;
            }

            try {
                // Get current file count
                const fileCount = window.fileHandler.files ? window.fileHandler.files.length : 0;
                logResult(`Current file count: ${fileCount}`, 'info');
                
                if (fileCount === 0) {
                    logResult('⚠️ WARNING: No files loaded. Please upload CSV files first.', 'info');
                    return;
                }

                // Test the rebuild method with current active files
                const activeFiles = window.fileHandler.files.filter(file => !file.isRemoved);
                window.fileHandler.rebuildRawDataFromActiveFiles(activeFiles);
                
                logResult('✅ PASS: File removal simulation completed', 'pass');
                
                // Update the test table
                updateTestCurrencyTable();
                
            } catch (error) {
                logResult(`❌ FAIL: Error during file removal simulation: ${error.message}`, 'fail');
            }
        }

        function verifyUIUpdate() {
            logResult('Verifying UI update functionality...', 'info');
            
            if (typeof window.dataProcessor === 'undefined') {
                logResult('❌ FAIL: DataProcessor not found', 'fail');
                return;
            }

            if (typeof window.dataProcessor.updateCurrencyBreakdown !== 'function') {
                logResult('❌ FAIL: updateCurrencyBreakdown method not found', 'fail');
                return;
            }

            try {
                // Test the UI update method
                window.dataProcessor.updateCurrencyBreakdown();
                logResult('✅ PASS: Currency breakdown UI update executed successfully', 'pass');
                
                // Update the test table
                updateTestCurrencyTable();
                
                // Check if the main currency table exists and has data
                const mainTable = document.getElementById('currencyBreakdownTableBody');
                if (mainTable) {
                    const rowCount = mainTable.children.length;
                    logResult(`Main currency table has ${rowCount} rows`, 'info');
                } else {
                    logResult('⚠️ WARNING: Main currency breakdown table not found', 'info');
                }
                
            } catch (error) {
                logResult(`❌ FAIL: Error during UI update: ${error.message}`, 'fail');
            }
        }

        function updateTestCurrencyTable() {
            const tableBody = document.getElementById('testCurrencyTableBody');
            
            if (typeof window.dataProcessor === 'undefined') {
                tableBody.innerHTML = '<tr><td colspan="4">DataProcessor not available</td></tr>';
                return;
            }

            try {
                const metrics = window.dataProcessor.getSummaryMetrics();
                const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
                
                tableBody.innerHTML = '';
                
                supportedCurrencies.forEach(currency => {
                    const creditAmount = metrics.currencyCreditAmounts?.[currency] || 0;
                    const debitAmount = metrics.currencyDebitAmounts?.[currency] || 0;
                    const totalAmount = metrics.currencyAmounts?.[currency] || 0;
                    
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${currency}</td>
                        <td>${formatCurrency(creditAmount, currency)}</td>
                        <td>${formatCurrency(debitAmount, currency)}</td>
                        <td>${formatCurrency(totalAmount, currency)}</td>
                    `;
                    tableBody.appendChild(row);
                });
                
                logResult('Test currency table updated successfully', 'info');
                
            } catch (error) {
                tableBody.innerHTML = `<tr><td colspan="4">Error: ${error.message}</td></tr>`;
                logResult(`Error updating test table: ${error.message}`, 'fail');
            }
        }

        function formatCurrency(amount, currency) {
            if (amount === 0) return '0';
            
            if (currency === 'MMK') {
                return new Intl.NumberFormat('en-US').format(amount) + ' MMK';
            } else {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: currency,
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(amount);
            }
        }

        function resetTest() {
            document.getElementById('testResults').innerHTML = 'Test results will appear here...\n';
            document.getElementById('testCurrencyTableBody').innerHTML = '<tr><td colspan="4">Test reset - please run tests again</td></tr>';
            logResult('Test environment reset', 'info');
        }

        // Auto-update the test table every 5 seconds if dataProcessor is available
        setInterval(() => {
            if (typeof window.dataProcessor !== 'undefined') {
                updateTestCurrencyTable();
            }
        }, 5000);

        // Initial setup
        window.addEventListener('load', () => {
            logResult('Currency Breakdown Removal Fix Test loaded', 'info');
            logResult('Please ensure the main dashboard is loaded before running tests', 'info');
        });
    </script>
</body>
</html>
