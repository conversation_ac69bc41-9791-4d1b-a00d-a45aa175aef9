/**
 * Currency Breakdown Fix Verification Script
 * 
 * This script can be run in the browser console to verify that the
 * currency breakdown file removal fix is working correctly.
 */

(function() {
    'use strict';

    console.log('🔍 Currency Breakdown Fix Verification Script');
    console.log('============================================');

    // Verification functions
    const verifyFix = {
        
        // Check if required methods exist
        checkMethods() {
            console.log('\n📋 Checking Required Methods...');
            
            const checks = [
                {
                    name: 'window.dataProcessor',
                    exists: typeof window.dataProcessor !== 'undefined',
                    required: true
                },
                {
                    name: 'window.fileHandler',
                    exists: typeof window.fileHandler !== 'undefined',
                    required: true
                },
                {
                    name: 'dataProcessor.recalculateCurrencyMetrics',
                    exists: typeof window.dataProcessor?.recalculateCurrencyMetrics === 'function',
                    required: true
                },
                {
                    name: 'dataProcessor.updateCurrencyBreakdown',
                    exists: typeof window.dataProcessor?.updateCurrencyBreakdown === 'function',
                    required: true
                },
                {
                    name: 'fileHandler.rebuildRawDataFromActiveFiles',
                    exists: typeof window.fileHandler?.rebuildRawDataFromActiveFiles === 'function',
                    required: true
                },
                {
                    name: 'fileHandler.updateDashboardAfterRemoval',
                    exists: typeof window.fileHandler?.updateDashboardAfterRemoval === 'function',
                    required: true
                }
            ];

            let allPassed = true;
            checks.forEach(check => {
                const status = check.exists ? '✅ PASS' : '❌ FAIL';
                console.log(`${status}: ${check.name}`);
                if (check.required && !check.exists) {
                    allPassed = false;
                }
            });

            return allPassed;
        },

        // Check current currency state
        checkCurrencyState() {
            console.log('\n💰 Current Currency State...');
            
            if (!window.dataProcessor) {
                console.log('❌ DataProcessor not available');
                return false;
            }

            try {
                const metrics = window.dataProcessor.getSummaryMetrics();
                const supportedCurrencies = ['MMK', 'USD', 'SGD', 'EUR', 'JPY', 'CNY', 'THB', 'INR'];
                
                console.log('Currency Amounts:');
                supportedCurrencies.forEach(currency => {
                    const amount = metrics.currencyAmounts?.[currency] || 0;
                    const credit = metrics.currencyCreditAmounts?.[currency] || 0;
                    const debit = metrics.currencyDebitAmounts?.[currency] || 0;
                    
                    if (amount > 0 || credit > 0 || debit > 0) {
                        console.log(`  ${currency}: Total=${amount}, Credit=${credit}, Debit=${debit}`);
                    }
                });

                const rawDataCount = window.dataProcessor.rawData ? window.dataProcessor.rawData.length : 0;
                console.log(`Raw Data: ${rawDataCount} transactions`);

                return true;
            } catch (error) {
                console.log(`❌ Error checking currency state: ${error.message}`);
                return false;
            }
        },

        // Check file state
        checkFileState() {
            console.log('\n📁 Current File State...');
            
            if (!window.fileHandler) {
                console.log('❌ FileHandler not available');
                return false;
            }

            try {
                const totalFiles = window.fileHandler.files ? window.fileHandler.files.length : 0;
                const activeFiles = window.fileHandler.files ? 
                    window.fileHandler.files.filter(file => !file.isRemoved).length : 0;
                const removedFiles = totalFiles - activeFiles;

                console.log(`Total Files: ${totalFiles}`);
                console.log(`Active Files: ${activeFiles}`);
                console.log(`Removed Files: ${removedFiles}`);

                if (window.fileHandler.files && window.fileHandler.files.length > 0) {
                    console.log('File Details:');
                    window.fileHandler.files.forEach((file, index) => {
                        const status = file.isRemoved ? 'REMOVED' : 'ACTIVE';
                        console.log(`  ${index}: ${file.name} (${status})`);
                    });
                }

                return true;
            } catch (error) {
                console.log(`❌ Error checking file state: ${error.message}`);
                return false;
            }
        },

        // Test currency recalculation
        testRecalculation() {
            console.log('\n🔄 Testing Currency Recalculation...');
            
            if (!window.dataProcessor?.recalculateCurrencyMetrics) {
                console.log('❌ recalculateCurrencyMetrics method not available');
                return false;
            }

            try {
                // Get state before
                const beforeMetrics = window.dataProcessor.getSummaryMetrics();
                const beforeMMK = beforeMetrics.currencyAmounts?.MMK || 0;
                const beforeUSD = beforeMetrics.currencyAmounts?.USD || 0;

                console.log(`Before - MMK: ${beforeMMK}, USD: ${beforeUSD}`);

                // Trigger recalculation
                window.dataProcessor.recalculateCurrencyMetrics();

                // Get state after
                const afterMetrics = window.dataProcessor.getSummaryMetrics();
                const afterMMK = afterMetrics.currencyAmounts?.MMK || 0;
                const afterUSD = afterMetrics.currencyAmounts?.USD || 0;

                console.log(`After - MMK: ${afterMMK}, USD: ${afterUSD}`);
                console.log('✅ Currency recalculation completed successfully');

                return true;
            } catch (error) {
                console.log(`❌ Error during recalculation test: ${error.message}`);
                return false;
            }
        },

        // Test UI update
        testUIUpdate() {
            console.log('\n🖥️ Testing UI Update...');
            
            if (!window.dataProcessor?.updateCurrencyBreakdown) {
                console.log('❌ updateCurrencyBreakdown method not available');
                return false;
            }

            try {
                // Check if currency table exists
                const currencyTable = document.getElementById('currencyBreakdownTableBody');
                if (!currencyTable) {
                    console.log('⚠️ Currency breakdown table not found in DOM');
                }

                // Trigger UI update
                window.dataProcessor.updateCurrencyBreakdown();
                console.log('✅ Currency breakdown UI update completed successfully');

                // Check table content after update
                if (currencyTable) {
                    const rowCount = currencyTable.children.length;
                    console.log(`Currency table now has ${rowCount} rows`);
                }

                return true;
            } catch (error) {
                console.log(`❌ Error during UI update test: ${error.message}`);
                return false;
            }
        },

        // Simulate file removal scenario
        simulateFileRemoval() {
            console.log('\n🗑️ Simulating File Removal Scenario...');

            if (!window.fileHandler?.rebuildRawDataFromActiveFiles) {
                console.log('❌ rebuildRawDataFromActiveFiles method not available');
                return false;
            }

            try {
                const activeFiles = window.fileHandler.files ?
                    window.fileHandler.files.filter(file => !file.isRemoved) : [];

                if (activeFiles.length === 0) {
                    console.log('⚠️ No active files to test with');
                    return false;
                }

                console.log(`Testing with ${activeFiles.length} active files`);

                // Test the rebuild method
                window.fileHandler.rebuildRawDataFromActiveFiles(activeFiles);
                console.log('✅ Raw data rebuild simulation completed successfully');

                return true;
            } catch (error) {
                console.log(`❌ Error during file removal simulation: ${error.message}`);
                return false;
            }
        },

        // Test file object integrity after removal
        testFileObjectIntegrity() {
            console.log('\n🔍 Testing File Object Integrity...');

            if (!window.fileHandler?.files) {
                console.log('❌ FileHandler files array not available');
                return false;
            }

            try {
                let validFiles = 0;
                let invalidFiles = 0;
                let removedFiles = 0;

                window.fileHandler.files.forEach((file, index) => {
                    if (file.isRemoved) {
                        removedFiles++;
                        // Check if removed files still have File methods
                        if (typeof file.slice === 'function') {
                            console.log(`✅ Removed file ${file.name} still has File methods (good for re-upload)`);
                        } else {
                            console.log(`❌ Removed file ${file.name} missing File methods (would cause re-upload error)`);
                            invalidFiles++;
                        }
                    } else {
                        if (typeof file.slice === 'function') {
                            validFiles++;
                        } else {
                            console.log(`❌ Active file ${file.name} missing File methods`);
                            invalidFiles++;
                        }
                    }
                });

                console.log(`File integrity summary:`);
                console.log(`  Valid active files: ${validFiles}`);
                console.log(`  Removed files: ${removedFiles}`);
                console.log(`  Invalid files: ${invalidFiles}`);

                return invalidFiles === 0;
            } catch (error) {
                console.log(`❌ Error during file integrity test: ${error.message}`);
                return false;
            }
        },

        // Test session tracking cleanup
        testSessionTracking() {
            console.log('\n📋 Testing Session Tracking...');

            if (!window.fileHandler?.uploadedFileNamesInSession) {
                console.log('❌ Session tracking not available');
                return false;
            }

            try {
                const sessionFiles = Array.from(window.fileHandler.uploadedFileNamesInSession);
                const actualFiles = window.fileHandler.files ? window.fileHandler.files.map(f => f.name.toLowerCase()) : [];
                const activeFiles = window.fileHandler.files ?
                    window.fileHandler.files.filter(f => !f.isRemoved).map(f => f.name.toLowerCase()) : [];

                console.log(`Session tracking has ${sessionFiles.length} files`);
                console.log(`Actual files array has ${actualFiles.length} files`);
                console.log(`Active files: ${activeFiles.length}`);

                // Check for orphaned entries in session tracking
                const orphanedEntries = sessionFiles.filter(sessionFile =>
                    !activeFiles.includes(sessionFile));

                if (orphanedEntries.length > 0) {
                    console.log(`⚠️ Found ${orphanedEntries.length} orphaned session entries:`, orphanedEntries);
                    console.log('This could prevent re-upload of removed files');
                    return false;
                } else {
                    console.log('✅ Session tracking is clean - no orphaned entries');
                    return true;
                }
            } catch (error) {
                console.log(`❌ Error during session tracking test: ${error.message}`);
                return false;
            }
        },

        // Test TTR infinite retry fix
        testTTRRetryFix() {
            console.log('\n🔄 Testing TTR Infinite Retry Fix...');

            if (!window.dataProcessor) {
                console.log('❌ DataProcessor not available');
                return false;
            }

            try {
                // Check if retry counter reset method exists
                if (typeof window.dataProcessor.resetTTRRetryCounter !== 'function') {
                    console.log('❌ resetTTRRetryCounter method not found');
                    return false;
                }

                // Check if showEmptyTTRTable method exists
                if (typeof window.dataProcessor.showEmptyTTRTable !== 'function') {
                    console.log('❌ showEmptyTTRTable method not found');
                    return false;
                }

                // Test retry counter reset
                window.dataProcessor.ttrRetryCount = 5; // Set a test value
                window.dataProcessor.resetTTRRetryCounter();

                if (window.dataProcessor.ttrRetryCount !== 0) {
                    console.log('❌ TTR retry counter not properly reset');
                    return false;
                }

                console.log('✅ TTR retry counter reset works correctly');

                // Test empty table display
                window.dataProcessor.showEmptyTTRTable();
                console.log('✅ Empty TTR table display works correctly');

                // Check current state
                const hasProcessedData = window.fileHandler?.processedData?.length > 0;
                const hasActiveFiles = window.fileHandler?.files?.some(f => !f.isRemoved);

                console.log(`Current state - Processed data: ${hasProcessedData}, Active files: ${hasActiveFiles}`);

                if (!hasProcessedData && !hasActiveFiles) {
                    console.log('✅ No infinite retry should occur with current state');
                } else {
                    console.log('ℹ️ Files present - TTR updates should work normally');
                }

                return true;
            } catch (error) {
                console.log(`❌ Error during TTR retry fix test: ${error.message}`);
                return false;
            }
        },

        // Test WU serial number display fix
        testWUSerialDisplay() {
            console.log('\n📋 Testing WU Serial Number Display Fix...');

            if (!window.dataProcessor) {
                console.log('❌ DataProcessor not available');
                return false;
            }

            try {
                // Check if getWUSerialNumbersForFile method exists
                if (typeof window.dataProcessor.getWUSerialNumbersForFile !== 'function') {
                    console.log('❌ getWUSerialNumbersForFile method not found');
                    return false;
                }

                // Test with mock data if no real files available
                const processedFiles = window.fileHandler?.processedData || [];

                if (processedFiles.length === 0) {
                    console.log('ℹ️ No processed files available for WU serial testing');
                    console.log('✅ WU serial display method exists and is ready');
                    return true;
                }

                // Test with actual files
                let testedFiles = 0;
                let filesWithWU = 0;
                let truncatedResults = 0;

                processedFiles.forEach(fileData => {
                    const fileName = fileData.fileName;
                    try {
                        const wuSerials = window.dataProcessor.getWUSerialNumbersForFile(fileName);
                        testedFiles++;

                        if (wuSerials && wuSerials !== '-') {
                            filesWithWU++;
                            console.log(`File: ${fileName}`);
                            console.log(`WU Serials: ${wuSerials.substring(0, 100)}${wuSerials.length > 100 ? '...' : ''}`);

                            // Check for old truncation pattern
                            if (wuSerials.includes('(+') && wuSerials.includes(' more)')) {
                                truncatedResults++;
                                console.log(`⚠️ File ${fileName} still shows truncated results: ${wuSerials}`);
                            } else if (wuSerials.includes(',')) {
                                // Count serials in the result
                                const serialCount = wuSerials.split(',').length;
                                console.log(`✅ File ${fileName} shows ${serialCount} WU serials without truncation`);
                            }
                        }
                    } catch (error) {
                        console.log(`❌ Error testing WU serials for ${fileName}: ${error.message}`);
                    }
                });

                console.log(`\nWU Serial Display Test Summary:`);
                console.log(`- Files tested: ${testedFiles}`);
                console.log(`- Files with WU serials: ${filesWithWU}`);
                console.log(`- Files with truncated results: ${truncatedResults}`);

                if (truncatedResults > 0) {
                    console.log('❌ Some files still show truncated WU serial results');
                    return false;
                } else {
                    console.log('✅ All WU serial displays appear to be working correctly');
                    return true;
                }

            } catch (error) {
                console.log(`❌ Error during WU serial display test: ${error.message}`);
                return false;
            }
        },

        // Run all verification tests
        runAll() {
            console.log('🚀 Running Complete Verification Suite...');
            console.log('==========================================');

            const results = {
                methods: this.checkMethods(),
                currencyState: this.checkCurrencyState(),
                fileState: this.checkFileState(),
                recalculation: this.testRecalculation(),
                uiUpdate: this.testUIUpdate(),
                fileRemoval: this.simulateFileRemoval(),
                fileIntegrity: this.testFileObjectIntegrity(),
                sessionTracking: this.testSessionTracking(),
                ttrRetryFix: this.testTTRRetryFix(),
                wuSerialDisplay: this.testWUSerialDisplay()
            };

            console.log('\n📊 Verification Results Summary:');
            console.log('================================');
            
            let passCount = 0;
            let totalTests = 0;
            
            Object.entries(results).forEach(([test, passed]) => {
                const status = passed ? '✅ PASS' : '❌ FAIL';
                console.log(`${status}: ${test}`);
                if (passed) passCount++;
                totalTests++;
            });

            const overallStatus = passCount === totalTests ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED';
            console.log(`\n${overallStatus} (${passCount}/${totalTests})`);

            if (passCount === totalTests) {
                console.log('\n🎉 Currency Breakdown Fix is working correctly!');
                console.log('The fix should properly handle file removal and currency recalculation.');
            } else {
                console.log('\n⚠️ Some issues detected. Please check the failed tests above.');
            }

            return results;
        }
    };

    // Auto-run verification if script is executed directly
    if (typeof window !== 'undefined') {
        // Add verification functions to global scope for manual testing
        window.verifyCurrencyFix = verifyFix;
        
        console.log('\n💡 Usage:');
        console.log('- Run all tests: verifyCurrencyFix.runAll()');
        console.log('- Check methods: verifyCurrencyFix.checkMethods()');
        console.log('- Check currency state: verifyCurrencyFix.checkCurrencyState()');
        console.log('- Test recalculation: verifyCurrencyFix.testRecalculation()');
        console.log('- Test UI update: verifyCurrencyFix.testUIUpdate()');
        console.log('- Simulate file removal: verifyCurrencyFix.simulateFileRemoval()');
        console.log('- Test file integrity: verifyCurrencyFix.testFileObjectIntegrity()');
        console.log('- Test session tracking: verifyCurrencyFix.testSessionTracking()');
        console.log('- Test TTR retry fix: verifyCurrencyFix.testTTRRetryFix()');
        console.log('- Test WU serial display: verifyCurrencyFix.testWUSerialDisplay()');
        
        // Run basic checks automatically
        setTimeout(() => {
            console.log('\n🔍 Running automatic basic verification...');
            verifyFix.checkMethods();
            verifyFix.checkCurrencyState();
            verifyFix.checkFileState();
        }, 1000);
    }

    return verifyFix;
})();
