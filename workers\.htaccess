# Apache Configuration for Web Workers Directory Protection
# Prevent direct access to worker files

# Deny access to all files in this directory
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Allow access only to worker files with authentication
<Files "*.js">
    # Require HTTP Basic Authentication
    AuthType Basic
    AuthName "Restricted Worker Files"
    AuthUserFile /path/to/.htpasswd
    Require valid-user
    
    # Additional security headers
    Head<PERSON> always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Prevent caching of sensitive files
    Header always set Cache-Control "no-cache, no-store, must-revalidate"
    Header always set Pragma "no-cache"
    Header always set Expires "0"
</Files>

# Block common attack patterns
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block direct access attempts
    RewriteCond %{HTTP_REFERER} !^https?://[^/]+/application\.html [NC]
    RewriteCond %{HTTP_REFERER} !^https?://[^/]+/index\.html [NC]
    RewriteCond %{REQUEST_URI} \.js$ [NC]
    RewriteRule .* - [F,L]
    
    # Block suspicious user agents
    RewriteCond %{HTTP_USER_AGENT} (wget|curl|libwww) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Disable directory browsing
Options -Indexes

# Disable server signature
ServerSignature Off
