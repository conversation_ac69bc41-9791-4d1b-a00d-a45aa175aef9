<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Analytics Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <h1>Customer Analytics Fix Test</h1>
    
    <div class="test-container">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="testInitialization()">Test Initialization</button>
        <button class="test-button" onclick="testElements()">Test DOM Elements</button>
        <button class="test-button" onclick="testFilters()">Test Filters</button>
        <button class="test-button" onclick="testSampleData()">Test with Sample Data</button>
        <button class="test-button" onclick="testModal()">Test Modal</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="testResults" class="test-results">Click a test button to see results...</div>
    </div>

    <!-- Minimal Customer Analytics Elements for Testing -->
    <div style="display: none;">
        <select id="customerAlertFilter">
            <option value="alert">All Alert Customers</option>
            <option value="mmk-alert">MMK Alerts (≥1B MMK)</option>
            <option value="usd-alert">USD Alerts (≥10K USD)</option>
        </select>
        
        <select id="customerSortBy">
            <option value="alert-priority">Alert Priority (High to Low)</option>
            <option value="mmk-desc">MMK Amount (High to Low)</option>
            <option value="usd-desc">USD Amount (High to Low)</option>
        </select>
        
        <select id="customerDateFilter">
            <option value="all">All Dates</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
        </select>
        
        <div id="customerAnalyticsTableBody"></div>
        <div id="customerAnalyticsSummary"></div>
        
        <!-- Modal Elements -->
        <div id="customerTransactionDetailsModal" style="display: none;">
            <div id="modalBackdrop"></div>
            <button id="modalCloseBtn">Close</button>
            <button id="modalCancelBtn">Cancel</button>
            <span id="modalCustomerName">-</span>
            <span id="modalCustomerDate">-</span>
            <span id="modalTotalCount">0</span>
            <span id="modalTotalMMK">0</span>
            <span id="modalTotalUSD">0</span>
            <span id="modalAlertStatus">No Alert</span>
            <table id="modalTransactionTable">
                <tbody id="modalTransactionTableBody"></tbody>
            </table>
            <button id="modalExportBtn">Export</button>
        </div>
    </div>

    <!-- Load Required Scripts -->
    <script src="js/constants.js"></script>
    <script src="js/currencyUtils.js"></script>
    <script src="js/customerAnalytics.js"></script>

    <script>
        let testOutput = '';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testOutput += `[${timestamp}] ${message}\n`;
            updateResults();
            
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        }
        
        function updateResults() {
            document.getElementById('testResults').textContent = testOutput;
        }
        
        function clearResults() {
            testOutput = '';
            updateResults();
        }
        
        function testInitialization() {
            log('=== Testing Customer Analytics Initialization ===');
            
            if (window.customerAnalytics) {
                log('✓ Customer Analytics object exists');
                log(`Initialization status: ${window.customerAnalytics.isInitialized}`);
                
                if (!window.customerAnalytics.isInitialized) {
                    log('Attempting to initialize...');
                    window.customerAnalytics.initialize();
                    log(`After initialization: ${window.customerAnalytics.isInitialized}`);
                }
            } else {
                log('✗ Customer Analytics object not found', 'error');
            }
        }
        
        function testElements() {
            log('=== Testing DOM Elements ===');
            
            if (!window.customerAnalytics) {
                log('✗ Customer Analytics not available', 'error');
                return;
            }
            
            const criticalElements = [
                'customerAlertFilter', 'customerSortBy', 'customerDateFilter',
                'customerAnalyticsTableBody', 'customerTransactionModal'
            ];
            
            criticalElements.forEach(elementKey => {
                const element = window.customerAnalytics.elements[elementKey];
                const domElement = document.getElementById(elementKey);
                log(`${elementKey}: CA=${!!element} DOM=${!!domElement} ${element ? '✓' : '✗'}`);
            });
        }
        
        function testFilters() {
            log('=== Testing Filter Functionality ===');
            
            if (!window.customerAnalytics || !window.customerAnalytics.isInitialized) {
                log('✗ Customer Analytics not initialized', 'error');
                return;
            }
            
            const filterElement = window.customerAnalytics.elements.customerAlertFilter;
            if (filterElement) {
                log('Testing filter changes...');
                ['alert', 'mmk-alert', 'usd-alert'].forEach(value => {
                    filterElement.value = value;
                    log(`Filter set to: ${value}`);
                    // Trigger change event
                    filterElement.dispatchEvent(new Event('change'));
                });
            } else {
                log('✗ Filter element not found', 'error');
            }
        }
        
        function testSampleData() {
            log('=== Testing with Sample Data ===');
            
            if (!window.customerAnalytics) {
                log('✗ Customer Analytics not available', 'error');
                return;
            }
            
            // Create sample transaction data
            const sampleTransactions = [
                {
                    CUSTOMER_NAME: 'Test Customer A',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST001',
                    TRANSACTION_DATE: '15-JUN-25 10.30.00.000000 AM',
                    TRANSACTION_AMOUNT: 2000000000, // 2B MMK
                    TRANSACTION_CURRENCY: 'MMK'
                },
                {
                    CUSTOMER_NAME: 'Test Customer B',
                    PARTICIPANT_ID_NUMBER_CONDUCTOR: 'CUST002',
                    TRANSACTION_DATE: '15-JUN-25 11.30.00.000000 AM',
                    TRANSACTION_AMOUNT: 15000, // 15K USD
                    TRANSACTION_CURRENCY: 'USD'
                }
            ];
            
            log(`Processing ${sampleTransactions.length} sample transactions...`);
            window.customerAnalytics.processTransactionData(sampleTransactions);
            
            log(`Customer data size: ${window.customerAnalytics.customerData.size}`);
            log(`Alert customers: ${window.customerAnalytics.alertCustomers.size}`);
        }
        
        function testModal() {
            log('=== Testing Modal Functionality ===');
            
            if (!window.customerAnalytics) {
                log('✗ Customer Analytics not available', 'error');
                return;
            }
            
            const modalElement = window.customerAnalytics.elements.customerTransactionModal;
            if (modalElement) {
                log('✓ Modal element found');
                log('Testing modal display...');
                
                // Test showing modal
                window.customerAnalytics.showCustomerTransactionModal();
                
                setTimeout(() => {
                    log('Testing modal hide...');
                    window.customerAnalytics.hideCustomerTransactionModal();
                }, 2000);
            } else {
                log('✗ Modal element not found', 'error');
            }
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            log('Page loaded, testing initialization...');
            testInitialization();
        });
    </script>
</body>
</html>
